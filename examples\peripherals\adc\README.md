# ADC

This project will introduce how to use the `tuyaos 3 adc` related interfaces to obtain the values collected by `adc`.

* `ADC` Introduction

  `ADC` (`Analog-to-Digital Converter`), also known as an `AD` converter. `ADC` is a device that converts analog quantities into digital quantities. A common application is converting continuously varying voltage values into digital quantities. In simple terms, it can be used to collect voltage.

Detailed interface introduction can be viewed in the Tuya Wind IDE in VS Code at [TuyaOS API Documentation](https://developer.tuya.com/cn/docs/iot-device-dev/tuyaos-wind-ide?id=Kbfy6kfuuqqu3#title-12-TuyaOS%20%E6%96%87%E6%A1%A3%E5%AF%BC%E8%88%AA).

## Execution Results

Each call to `example_adc` will print the current adc value collected.

```c
[01-01 00:01:34 TUYA D][lr:0x70309] ADC0 value = 4049
```

## Technical Support

You can obtain support from Tuya through the following methods:

- TuyaOS Forum: https://www.tuyaos.com

- Developer Center: https://developer.tuya.com

- Help Center: https://support.tuya.com/help

- Technical Support Ticket Center: https://service.console.tuya.com