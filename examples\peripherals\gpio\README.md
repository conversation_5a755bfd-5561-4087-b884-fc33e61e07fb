# GPIO

This project will introduce how to use the `tuyaos 3 gpio` related interfaces to set `GPIO` to input, output, or interrupt mode.

Detailed introduction to related interfaces can be viewed in the TuyaOS API documentation in the Tuya Wind IDE in VS Code at [TuyaOS API Documentation](https://developer.tuya.com/cn/docs/iot-device-dev/tuyaos-wind-ide?id=Kbfy6kfuuqqu3#title-12-TuyaOS%20Documentation%20Navigation).

## Process Introduction

![gpio process 12138.png](https://airtake-public-data-1254153901.cos.ap-shanghai.myqcloud.com/content-platform/hestia/1655889099cf84a97457b.png)

## Execution Results

Reading the level.

```c
[01-01 00:06:18 TUYA D][lr:0x4a98b] GPIO High
```

Pressing the button triggers the GPIO interrupt.

```c
[01-01 00:00:42 TUYA D][lr:0x4a9bb] ------------<PERSON><PERSON> IRQ Callback------------
```
## Technical Support

You can obtain support from Tuya through the following methods:

- TuyaOS Forum: https://www.tuyaos.com

- Developer Center: https://developer.tuya.com

- Help Center: https://support.tuya.com/help

- Technical Support Ticket Center: https://service.console.tuya.com