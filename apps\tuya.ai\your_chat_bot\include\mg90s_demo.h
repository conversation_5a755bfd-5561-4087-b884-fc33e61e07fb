/**
 * @file mg90s_demo.h
 * @brief MG90S舵机增强功能演示头文件
 * 
 * 基于Adafruit PWM库优化的MG90S舵机控制演示接口
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#ifndef __MG90S_DEMO_H__
#define __MG90S_DEMO_H__

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief MG90S舵机基础功能演示
 * 
 * 演示基础的角度控制功能
 * 包括0°、45°、90°、135°、180°的角度设置
 */
void mg90s_basic_demo(void);

/**
 * @brief MG90S舵机平滑运动演示
 * 
 * 演示平滑运动控制功能
 * 包括不同速度的平滑移动
 */
void mg90s_smooth_move_demo(void);

/**
 * @brief MG90S舵机位置反馈演示
 * 
 * 演示位置反馈检测功能
 * 通过PWM信号分析估算当前位置
 */
void mg90s_position_feedback_demo(void);

/**
 * @brief MG90S舵机扭矩优化演示
 * 
 * 演示扭矩优化功能
 * 根据不同负载等级进行自适应调整
 */
void mg90s_torque_optimization_demo(void);

/**
 * @brief MG90S舵机综合演示
 * 
 * 运行所有演示功能的综合测试
 * 包括基础控制、平滑运动、位置反馈、扭矩优化
 */
void mg90s_comprehensive_demo(void);

#ifdef __cplusplus
}
#endif

#endif /* __MG90S_DEMO_H__ */
