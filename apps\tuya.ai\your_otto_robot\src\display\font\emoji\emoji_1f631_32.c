
#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
#include "lvgl.h"
#elif defined(LV_BUILD_TEST)
#include "../lvgl.h"
#else
#include "lvgl/lvgl.h"
#endif

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_EMOJI_1F631_32
#define LV_ATTRIBUTE_EMOJI_1F631_32
#endif

static const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_EMOJI_1F631_32 uint8_t
    emoji_1f631_32_map[] = {

        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x94, 0xde, 0xb6, 0xd6, 0xd8, 0xd6, 0xda, 0xce, 0xfd, 0xc6, 0xfe, 0xbe, 0xfe, 0xbe, 0xfd, 0xc6,
        0xda, 0xce, 0xd8, 0xd6, 0xb6, 0xd6, 0x74, 0xde, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb4, 0xde, 0xd8, 0xce, 0xfd, 0xc6, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfd, 0xbe,
        0xd8, 0xce, 0xd4, 0xde, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb5, 0xe6, 0xd8, 0xd6,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xb8, 0xd6, 0x73, 0xde,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xb4, 0xde, 0xdb, 0xc6, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xda, 0xce, 0xb4, 0xde, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb5, 0xde, 0xfd, 0xc6, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfc, 0xc6, 0xb4, 0xde, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0xb4, 0xde, 0xfd, 0xc6, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfc, 0xc6, 0xb4, 0xde,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x73, 0xde, 0xdb, 0xc6, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0x1e, 0xcf, 0x5e, 0xdf, 0x5e, 0xd7, 0xfe, 0xc6, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xc6, 0x5e, 0xd7, 0x5e, 0xdf, 0x1e, 0xcf, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xda, 0xce, 0xb5, 0xe6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0xd8, 0xd6, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0x7e, 0xdf, 0xdf, 0xf7,
        0xdf, 0xf7, 0xdf, 0xf7, 0xbf, 0xf7, 0x1e, 0xc7, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0x1e, 0xc7,
        0xbf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0x5e, 0xdf, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xb7, 0xd6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb4, 0xde, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0x5e, 0xd7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0x7f, 0xe7,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0x7f, 0xe7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7,
        0xdf, 0xf7, 0x5e, 0xd7, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfd, 0xc6, 0x94, 0xde, 0x00, 0x00,
        0x00, 0x00, 0xd8, 0xce, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xc6, 0xbf, 0xf7, 0xdf, 0xf7,
        0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xbf, 0xf7, 0xdb, 0xc6, 0xda, 0xce, 0xda, 0xce, 0xdb, 0xc6,
        0xbf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xbf, 0xf7, 0xfe, 0xc6, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xb8, 0xd6, 0x00, 0x00, 0x94, 0xde, 0xfd, 0xc6, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0x5e, 0xd7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7,
        0xdf, 0xf7, 0x6a, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x6a, 0xfe, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7,
        0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0x5e, 0xd7, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xdd, 0xc6,
        0xb3, 0xde, 0xb6, 0xd6, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0x7e, 0xe7, 0xdf, 0xf7,
        0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xbe, 0xf7, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x6a, 0xfe, 0xbf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0x7e, 0xe7,
        0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe, 0xb6, 0xd6, 0xd8, 0xd6, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xfe, 0xbe, 0xb7, 0xd6, 0x7a, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7,
        0xdf, 0xf7, 0x7b, 0xf7, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x7b, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7,
        0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0x7a, 0xf7, 0xb7, 0xd6, 0xfe, 0xbe, 0xfe, 0xbe, 0xfe, 0xbe,
        0xfe, 0xbe, 0xd8, 0xce, 0xda, 0xce, 0xfe, 0xbe, 0xfe, 0xbe, 0xfd, 0xc6, 0x90, 0xee, 0x69, 0xfe, 0x58, 0xff,
        0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0x13, 0xff, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x13, 0xff, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7,
        0x58, 0xff, 0x69, 0xfe, 0x90, 0xee, 0xfd, 0xc6, 0xfe, 0xbe, 0xfe, 0xbe, 0xda, 0xce, 0xfd, 0xc6, 0xfe, 0xbe,
        0xdd, 0xc6, 0x8e, 0xf6, 0x69, 0xfe, 0x69, 0xfe, 0x13, 0xff, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7,
        0xdf, 0xf7, 0x9c, 0xf7, 0x6a, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x6a, 0xfe, 0xbd, 0xf7,
        0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0x13, 0xff, 0x69, 0xfe, 0x69, 0xfe, 0x8e, 0xf6,
        0xfd, 0xc6, 0xfe, 0xbe, 0xfc, 0xc6, 0xfe, 0xbe, 0xfe, 0xbe, 0x8f, 0xee, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x6b, 0xfe, 0xbd, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xbe, 0xf7, 0xae, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xae, 0xfe, 0xbe, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7,
        0xbd, 0xf7, 0x8b, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x8e, 0xee, 0xfe, 0xbe, 0xfd, 0xbe, 0xd9, 0xce,
        0x52, 0xde, 0x88, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x8d, 0xfe, 0x58, 0xff, 0x9c, 0xf7,
        0x57, 0xff, 0x8c, 0xfe, 0x69, 0xfe, 0x67, 0xdd, 0x42, 0x93, 0x60, 0x6a, 0x60, 0x6a, 0x62, 0x93, 0x67, 0xdd,
        0x69, 0xfe, 0x8c, 0xfe, 0x57, 0xff, 0x9c, 0xf7, 0x58, 0xff, 0x8c, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x88, 0xfe, 0x52, 0xde, 0xd9, 0xce, 0x8a, 0xf5, 0x48, 0xfd, 0x88, 0xfd, 0xc8, 0xfd, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x06, 0xcd, 0x40, 0x6a,
        0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x40, 0x6a, 0x06, 0xd5, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xc8, 0xfd, 0x88, 0xfd, 0x48, 0xfd, 0x8a, 0xf5,
        0x68, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x68, 0xfd, 0xa8, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x29, 0xf6, 0x60, 0x72, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62,
        0x20, 0x62, 0x80, 0x72, 0x29, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0xc8, 0xfd, 0x68, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x68, 0xfd, 0x68, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd,
        0x88, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x06, 0xcd, 0x20, 0x62,
        0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x06, 0xcd, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x2a, 0xfe, 0xa8, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd,
        0x68, 0xfd, 0x68, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0xe9, 0xfd, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x85, 0xbc, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62,
        0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x85, 0xbc, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0xc8, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x68, 0xfd, 0x68, 0xfd, 0x48, 0xfd, 0x48, 0xfd,
        0x48, 0xfd, 0x48, 0xfd, 0xa8, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x85, 0xbc,
        0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x85, 0xbc,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xa8, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd,
        0x48, 0xfd, 0x67, 0xfd, 0x68, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x68, 0xfd, 0xe8, 0xfd,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x85, 0xbc, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62,
        0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x85, 0xbc, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0xe8, 0xfd, 0x68, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x67, 0xfd, 0x68, 0xfd, 0x48, 0xfd,
        0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0xa9, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x85, 0xbc, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62,
        0x85, 0xbc, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xc8, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd,
        0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd,
        0xc8, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x85, 0xbc, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62,
        0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x85, 0xbc, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0xa8, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x89, 0xfd, 0x00, 0x00,
        0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0xa8, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x85, 0xbc, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62,
        0x20, 0x62, 0x85, 0xbc, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xa8, 0xfd, 0x48, 0xfd, 0x48, 0xfd,
        0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd,
        0x48, 0xfd, 0xa8, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xc6, 0xc4, 0x20, 0x62, 0x20, 0x62,
        0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0xe6, 0xcc, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0xa8, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x00, 0x00,
        0x00, 0x00, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x88, 0xfd, 0x49, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0xe8, 0xf5, 0x40, 0x6a, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62,
        0x20, 0x62, 0x40, 0x6a, 0xe8, 0xf5, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x88, 0xfd, 0x48, 0xfd,
        0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x47, 0xfd, 0x48, 0xfd, 0x48, 0xfd,
        0x48, 0xfd, 0x48, 0xfd, 0x88, 0xfd, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x65, 0xb4,
        0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x65, 0xb4, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0x88, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x47, 0xfd,
        0x00, 0x00, 0x00, 0x00, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x49, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xa5, 0xbc, 0x80, 0x72, 0x20, 0x62, 0x20, 0x62,
        0x80, 0x72, 0xa5, 0xc4, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0x47, 0xfd,
        0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x00, 0x00, 0xa9, 0xfd, 0x48, 0xfd, 0x48, 0xfd,
        0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x47, 0xfd, 0x00, 0x00, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0xa8, 0xe5, 0xa8, 0xe5, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0x00, 0x00, 0x68, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd,
        0x47, 0xfd, 0x4a, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0x2a, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x47, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x48, 0xfd, 0x68, 0xfd, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1b, 0x93, 0xd8, 0xf4, 0xfd, 0xff, 0xff, 0xfd, 0xf4, 0xd7, 0x92, 0x1a,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x46, 0xd9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd9, 0x45, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x12, 0xcb, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xca, 0x10, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x45, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x59, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x54, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x45, 0xfd, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xfc, 0x2f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xf5, 0x12, 0x00, 0x00, 0x00, 0x00, 0xcb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x00, 0x00,
        0x00, 0x46, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x2e, 0x00, 0x00, 0xd9, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x00, 0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0x18, 0x93, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0x90, 0xd8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd6, 0xf4, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xe1, 0x4f, 0x0b, 0x61, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x5f, 0x0b, 0x4f, 0xe0,
        0xfe, 0xff, 0xf2, 0x46, 0x34, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x32, 0x4b, 0xf3, 0xff, 0xfe, 0xfc, 0xff, 0xff, 0xfd,
        0x2c, 0x79, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0x75, 0x30, 0xfd, 0xff, 0xff, 0xfc, 0xef, 0xff, 0xff, 0xff, 0xe2, 0x0a, 0xed, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xeb,
        0x09, 0xe4, 0xff, 0xff, 0xff, 0xef, 0xca, 0xff, 0xff, 0xff, 0xff, 0x51, 0x8b, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x88, 0x56, 0xff, 0xff, 0xff,
        0xff, 0xca, 0x8c, 0xff, 0xff, 0xff, 0xff, 0xce, 0x3c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x38, 0xd0, 0xff, 0xff, 0xff, 0xff, 0x89, 0x5c, 0xff,
        0xff, 0xff, 0xff, 0xfc, 0x04, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xed, 0x04, 0xfd, 0xff, 0xff, 0xff, 0xff, 0x59, 0x34, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x3f, 0xc3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xbe, 0x45, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0c, 0xff, 0xff, 0xff, 0xff, 0xff, 0x94, 0x97, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x93, 0x98, 0xff, 0xff,
        0xff, 0xff, 0xfe, 0x0a, 0x00, 0xee, 0xff, 0xff, 0xff, 0xff, 0xbd, 0x7a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x77, 0xc0, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x00,
        0x00, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xd9, 0x68, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x65, 0xdb, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x00, 0x00, 0xcc, 0xff, 0xff,
        0xff, 0xff, 0xe5, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0x5b, 0xe7, 0xff, 0xff, 0xff, 0xff, 0xc7, 0x00, 0x00, 0xd1, 0xff, 0xff, 0xff, 0xff, 0xda, 0x63,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x62, 0xdb,
        0xff, 0xff, 0xff, 0xff, 0xcd, 0x00, 0x00, 0xdb, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x4a, 0xf7, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x49, 0xb5, 0xff, 0xff, 0xff, 0xff,
        0xd8, 0x00, 0x07, 0xfb, 0xff, 0xff, 0xff, 0xff, 0x9d, 0x00, 0x1d, 0x97, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x95, 0x1c, 0x00, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x06, 0x30, 0xff,
        0xff, 0xff, 0xff, 0xff, 0x83, 0x00, 0x00, 0x00, 0x09, 0x55, 0x91, 0xc7, 0xe3, 0xf6, 0xf6, 0xe3, 0xc7, 0x91,
        0x54, 0x09, 0x00, 0x00, 0x00, 0x85, 0xff, 0xff, 0xff, 0xff, 0xff, 0x2e,

};

const lv_image_dsc_t emoji_1f631_32 = {
    .header.magic = LV_IMAGE_HEADER_MAGIC,
    .header.cf = LV_COLOR_FORMAT_RGB565A8,
    .header.flags = 0,
    .header.w = 32,
    .header.h = 32,
    .header.stride = 64,
    .data_size = sizeof(emoji_1f631_32_map),
    .data = emoji_1f631_32_map,
};
