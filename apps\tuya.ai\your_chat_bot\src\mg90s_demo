/**
 * @file mg90s_demo.c
 * @brief MG90S舵机增强功能演示
 * 
 * 基于Adafruit PWM库优化的MG90S舵机控制演示
 * 展示精确校准、平滑运动、位置反馈、扭矩优化等功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "pwm_door_control.h"
#include "tal_api.h"
#include "tkl_output.h"

/**
 * @brief MG90S舵机基础功能演示
 */
void mg90s_basic_demo(void)
{
    PR_INFO("🎬 MG90S舵机基础功能演示开始...");
    
    // 初始化PWM门控制系统
    OPERATE_RET ret = pwm_door_control_init();
    if (ret != OPRT_OK) {
        PR_ERR("❌ PWM门控制系统初始化失败: %d", ret);
        return;
    }
    
    // 演示1: 基础角度控制
    PR_INFO("📊 演示1: 基础角度控制");
    uint16_t demo_angles[] = {0, 45, 90, 135, 180};
    int angle_count = sizeof(demo_angles) / sizeof(demo_angles[0]);
    
    for (int i = 0; i < angle_count; i++) {
        PR_INFO("   设置角度: %d°", demo_angles[i]);
        
        // 控制门1舵机
        ret = pwm_door_servo_angle_control(SERVO_ID_DOOR_1, demo_angles[i]);
        if (ret == OPRT_OK) {
            PR_INFO("   ✅ 门1舵机角度设置成功");
        }
        
        tal_system_sleep(2000); // 等待2秒观察运动
    }
    
    PR_INFO("✅ 基础功能演示完成");
}

/**
 * @brief MG90S舵机平滑运动演示
 */
void mg90s_smooth_move_demo(void)
{
    PR_INFO("🎬 MG90S舵机平滑运动演示开始...");
    
    // 演示2: 平滑运动控制
    PR_INFO("📊 演示2: 平滑运动控制");
    
    // 从0度平滑移动到180度
    PR_INFO("   平滑移动: 0° -> 180° (速度: 30ms/度)");
    OPERATE_RET ret = pwm_door_servo_smooth_move(SERVO_ID_DOOR_1, 180, 30);
    if (ret == OPRT_OK) {
        PR_INFO("   ✅ 平滑移动到180°完成");
    }
    
    tal_system_sleep(1000);
    
    // 从180度快速平滑移动到90度
    PR_INFO("   快速平滑移动: 180° -> 90° (速度: 15ms/度)");
    ret = pwm_door_servo_smooth_move(SERVO_ID_DOOR_1, 90, 15);
    if (ret == OPRT_OK) {
        PR_INFO("   ✅ 快速平滑移动到90°完成");
    }
    
    tal_system_sleep(1000);
    
    // 慢速平滑移动到0度
    PR_INFO("   慢速平滑移动: 90° -> 0° (速度: 50ms/度)");
    ret = pwm_door_servo_smooth_move(SERVO_ID_DOOR_1, 0, 50);
    if (ret == OPRT_OK) {
        PR_INFO("   ✅ 慢速平滑移动到0°完成");
    }
    
    PR_INFO("✅ 平滑运动演示完成");
}

/**
 * @brief MG90S舵机位置反馈演示
 */
void mg90s_position_feedback_demo(void)
{
    PR_INFO("🎬 MG90S舵机位置反馈演示开始...");
    
    // 演示3: 位置反馈检测
    PR_INFO("📊 演示3: 位置反馈检测");
    
    uint16_t test_positions[] = {30, 60, 120, 150};
    int pos_count = sizeof(test_positions) / sizeof(test_positions[0]);
    
    for (int i = 0; i < pos_count; i++) {
        // 设置目标位置
        PR_INFO("   设置目标位置: %d°", test_positions[i]);
        OPERATE_RET ret = pwm_door_servo_angle_control(SERVO_ID_DOOR_1, test_positions[i]);
        
        if (ret == OPRT_OK) {
            tal_system_sleep(1000); // 等待舵机到位
            
            // 检测当前位置
            uint16_t detected_angle;
            ret = pwm_door_servo_get_position(SERVO_ID_DOOR_1, &detected_angle);
            if (ret == OPRT_OK) {
                int16_t error = detected_angle - test_positions[i];
                PR_INFO("   📡 位置反馈: 目标=%d°, 检测=%d°, 误差=%d°", 
                        test_positions[i], detected_angle, error);
            }
        }
    }
    
    PR_INFO("✅ 位置反馈演示完成");
}

/**
 * @brief MG90S舵机扭矩优化演示
 */
void mg90s_torque_optimization_demo(void)
{
    PR_INFO("🎬 MG90S舵机扭矩优化演示开始...");
    
    // 演示4: 扭矩优化
    PR_INFO("📊 演示4: 扭矩优化");
    
    // 设置到90度位置进行扭矩优化测试
    pwm_door_servo_angle_control(SERVO_ID_DOOR_1, 90);
    tal_system_sleep(1000);
    
    // 测试不同负载等级的扭矩优化
    uint8_t load_levels[] = {10, 30, 60, 90};
    const char* load_names[] = {"轻负载", "中等负载", "重负载", "满负载"};
    int load_count = sizeof(load_levels) / sizeof(load_levels[0]);
    
    for (int i = 0; i < load_count; i++) {
        PR_INFO("   测试%s优化 (负载: %d%%)", load_names[i], load_levels[i]);
        
        OPERATE_RET ret = pwm_door_servo_optimize_torque(SERVO_ID_DOOR_1, load_levels[i]);
        if (ret == OPRT_OK) {
            PR_INFO("   ✅ %s扭矩优化成功", load_names[i]);
        }
        
        tal_system_sleep(2000); // 观察优化效果
    }
    
    PR_INFO("✅ 扭矩优化演示完成");
}

/**
 * @brief MG90S舵机综合演示
 */
void mg90s_comprehensive_demo(void)
{
    PR_INFO("🎉 MG90S舵机综合功能演示开始...");
    PR_INFO("基于Adafruit PWM库优化的MG90S舵机控制系统");
    PR_INFO("功能特性: 精确校准、平滑运动、位置反馈、扭矩优化");
    
    // 运行所有演示
    mg90s_basic_demo();
    tal_system_sleep(2000);
    
    mg90s_smooth_move_demo();
    tal_system_sleep(2000);
    
    mg90s_position_feedback_demo();
    tal_system_sleep(2000);
    
    mg90s_torque_optimization_demo();
    tal_system_sleep(2000);
    
    // 运行完整的增强功能测试
    PR_INFO("🧪 运行完整的MG90S增强功能测试...");
    pwm_door_control_run_mg90s_tests();
    
    // 清理系统
    pwm_door_control_cleanup();
    
    PR_INFO("🎉 MG90S舵机综合功能演示完成!");
    PR_INFO("系统特性总结:");
    PR_INFO("  ✅ 精确校准: 基于Adafruit算法的分段线性插值");
    PR_INFO("  ✅ 平滑运动: 避免机械冲击的渐进式控制");
    PR_INFO("  ✅ 位置反馈: PWM信号分析的位置估算");
    PR_INFO("  ✅ 扭矩优化: 负载自适应的频率调整");
    PR_INFO("  ✅ 个体校准: 补偿舵机个体差异");
}
