dist: trusty
sudo: required
language: c
 
compiler:
- gcc

before_install:
- sudo apt-get update
- sudo apt-get install libpng12-dev

install:
- ./autogen.sh
- ./configure --with-tests && make
- sudo make install
- mkdir build && cd build
- cmake .. -DWITH_TESTS=yes -DBUILD_SHARED_LIBS=on && make
- DESTDIR=$PWD/install make install

script:
- cd $TRAVIS_BUILD_DIR/tests
- ./test_all.sh
- cd $TRAVIS_BUILD_DIR/build/tests
- cp $TRAVIS_BUILD_DIR/tests/frame ./
- make test
