/**
 * @file example_spi.c
 * @brief SPI driver example for SDK.
 *
 * This file provides an example implementation of an SPI (Serial Peripheral Interface) driver using the Tuya SDK.
 * It demonstrates the configuration and usage of SPI communication to interact with peripheral devices such as sensors,
 * memory chips, and other microcontrollers. The example covers initializing the SPI bus, configuring SPI parameters
 * (mode, frequency, data bits, bit order), sending data to a peripheral device, and deinitializing the SPI bus after
 * communication is complete.
 *
 * The SPI driver example aims to help developers understand how to use SPI communication in their Tuya IoT projects for
 * applications requiring high-speed serial data transfer. It includes detailed examples of setting up SPI
 * configurations, handling data transmission, and integrating these functionalities within a multitasking environment.
 *
 * @note This example is designed to be adaptable to various Tuya IoT devices and platforms, showcasing fundamental SPI
 * operations critical for IoT device development.
 *
 * @copyright Copyright (c) 2021-2024 Tuya Inc. All Rights Reserved.
 *
 */

#include "tuya_cloud_types.h"

#include "tal_api.h"
#include "tkl_output.h"
#include "tkl_spi.h"
/***********************************************************
*************************micro define***********************
***********************************************************/
#define SPI_ID TUYA_SPI_NUM_0

#define SPI_FREQ 10000

/***********************************************************
***********************typedef define***********************
***********************************************************/

/***********************************************************
***********************variable define**********************
***********************************************************/

/***********************************************************
***********************function define**********************
***********************************************************/

/**
 * @brief user_main
 *
 * @param[in] param:Task parameters
 * @return none
 */
void user_main()
{
    OPERATE_RET rt = OPRT_OK;
    uint8_t send_buff[] = {"Hello Tuya"};

    /* basic init */
    tal_log_init(TAL_LOG_LEVEL_DEBUG, 1024, (TAL_LOG_OUTPUT_CB)tkl_log_output);

    PR_NOTICE("Application information:");
    PR_NOTICE("Project name:        %s", PROJECT_NAME);
    PR_NOTICE("App version:         %s", PROJECT_VERSION);
    PR_NOTICE("Compile time:        %s", __DATE__);
    PR_NOTICE("TuyaOpen version:    %s", OPEN_VERSION);
    PR_NOTICE("TuyaOpen commit-id:  %s", OPEN_COMMIT);
    PR_NOTICE("Platform chip:       %s", PLATFORM_CHIP);
    PR_NOTICE("Platform board:      %s", PLATFORM_BOARD);
    PR_NOTICE("Platform commit-id:  %s", PLATFORM_COMMIT);

    /*spi init*/
    TUYA_SPI_BASE_CFG_T spi_cfg = {.mode = TUYA_SPI_MODE0,
                                   .freq_hz = SPI_FREQ,
                                   .databits = TUYA_SPI_DATA_BIT8,
                                   .bitorder = TUYA_SPI_ORDER_LSB2MSB,
                                   .role = TUYA_SPI_ROLE_MASTER,
                                   .type = TUYA_SPI_AUTO_TYPE};
    TUYA_CALL_ERR_GOTO(tkl_spi_init(SPI_ID, &spi_cfg), __EXIT);

    TUYA_CALL_ERR_LOG(tkl_spi_send(SPI_ID, send_buff, CNTSOF(send_buff)));
    PR_NOTICE("spi send \"%s\" finish", send_buff);

    TUYA_CALL_ERR_LOG(tkl_spi_deinit(SPI_ID));

__EXIT:
    return;
}

/**
 * @brief main
 *
 * @param argc
 * @param argv
 * @return void
 */
#if OPERATING_SYSTEM == SYSTEM_LINUX
void main(int argc, char *argv[])
{
    user_main();

    while (1) {
        tal_system_sleep(500);
    }
}
#else

/* Tuya thread handle */
static THREAD_HANDLE ty_app_thread = NULL;

/**
 * @brief  task thread
 *
 * @param[in] arg:Parameters when creating a task
 * @return none
 */
static void tuya_app_thread(void *arg)
{
    user_main();

    tal_thread_delete(ty_app_thread);
    ty_app_thread = NULL;
}

void tuya_app_main(void)
{
    THREAD_CFG_T thrd_param = {4096, 4, "tuya_app_main"};
    tal_thread_create_and_start(&ty_app_thread, NULL, NULL, tuya_app_thread, NULL, &thrd_param);
}
#endif