/**
 * @file medication_log_manager.h
 * @brief 服药日志管理模块头文件
 * 
 * 提供服药日志记录、Flash存储和DP118上传功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#ifndef __MEDICATION_LOG_MANAGER_H__
#define __MEDICATION_LOG_MANAGER_H__

#include "tuya_cloud_types.h"
#include "tal_api.h"

#ifdef __cplusplus
extern "C" {
#endif

// ========== 配置常量 ==========

#define MEDICATION_LOG_FLASH_ADDR       0x1F0000    /**< Flash存储地址 (1MB-64KB) */
#define MEDICATION_LOG_FLASH_SIZE       0x10000     /**< 64KB存储空间 */
#define MEDICATION_LOG_MAGIC            0x4D454449  /**< "MEDI" 魔数 */
#define MAX_LOG_ENTRIES_FLASH           100         /**< 最大日志条目数 */
#define LOG_ENTRY_SIZE                  128         /**< 每条日志大小 */

// DP点定义
#define DP_MEDICATION_RECORD            118         /**< DP118: 服药记录缓存 */
#define DP_ERROR_LOG                    107         /**< DP107: 报错信息 */
#define DP_TEST_LOG                     108         /**< DP108: 测试日志 */

// 上传配置
#define RECORD_UPLOAD_MAX_LENGTH        255         /**< DP118最大长度 */
#define RECORD_BATCH_SIZE               5           /**< 批量上传记录数量 */

// ========== 枚举定义 ==========

/**
 * @brief 服药日志显示类型枚举
 */
typedef enum {
    MEDICATION_DISPLAY_LOG = 0,     /**< 日志信息 */
    MEDICATION_DISPLAY_STATUS,      /**< 状态信息 */
    MEDICATION_DISPLAY_ERROR,       /**< 错误信息 */
    MEDICATION_DISPLAY_SUCCESS      /**< 成功信息 */
} medication_display_type_e;

/**
 * @brief 服药记录上传状态枚举
 */
typedef enum {
    UPLOAD_STATUS_IDLE = 0,         /**< 空闲状态 */
    UPLOAD_STATUS_PENDING,          /**< 等待上传 */
    UPLOAD_STATUS_UPLOADING,        /**< 正在上传 */
    UPLOAD_STATUS_SUCCESS,          /**< 上传成功 */
    UPLOAD_STATUS_FAILED            /**< 上传失败 */
} upload_status_e;

// ========== 结构体定义 ==========

/**
 * @brief Flash中的日志条目结构
 */
typedef struct {
    uint32_t magic;                     /**< 魔数验证 */
    uint32_t timestamp;                 /**< 时间戳 */
    char medication_name[32];           /**< 药品名称 */
    char log_entry[64];                 /**< 日志内容 */
    uint32_t crc;                       /**< CRC校验 */
    bool uploaded;                      /**< 是否已上传到DP118 */
} __attribute__((packed)) flash_log_entry_t;

/**
 * @brief Flash日志头部结构
 */
typedef struct {
    uint32_t magic;                     /**< 魔数 */
    uint32_t version;                   /**< 版本号 */
    uint32_t entry_count;               /**< 条目数量 */
    uint32_t write_index;               /**< 写入索引 */
    uint32_t last_upload_index;         /**< 最后上传索引 */
    uint32_t reserved[3];               /**< 预留字段 */
} __attribute__((packed)) flash_log_header_t;

/**
 * @brief 服药记录统计信息结构
 */
typedef struct {
    uint32_t total_records;             /**< 总记录数 */
    uint32_t uploaded_records;          /**< 已上传记录数 */
    uint32_t pending_records;           /**< 待上传记录数 */
    uint32_t upload_success_count;      /**< 上传成功次数 */
    uint32_t upload_failed_count;       /**< 上传失败次数 */
    upload_status_e current_status;     /**< 当前上传状态 */
} medication_log_stats_t;

// ========== 函数声明 ==========

/**
 * @brief 初始化服药日志管理器
 * 
 * 初始化Flash存储系统和上传机制
 * 
 * @return OPERATE_RET 操作结果
 *         - OPRT_OK: 成功
 *         - 其他: 失败
 */
OPERATE_RET medication_log_manager_init(void);

/**
 * @brief 清理服药日志管理器
 * 
 * 释放资源，保存未完成的上传任务
 */
void medication_log_manager_cleanup(void);

/**
 * @brief 记录服药日志
 * 
 * @param medication_name 药品名称
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_manager_write_dispense(const char *medication_name);

/**
 * @brief 上传服药记录到DP118
 * 
 * 将未上传的服药记录批量上传到DP118
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_manager_upload_records(void);

/**
 * @brief 获取服药记录统计信息
 * 
 * @param stats 统计信息结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_manager_get_stats(medication_log_stats_t *stats);

/**
 * @brief 获取最近的服药记录
 * 
 * @param records 记录缓冲区
 * @param max_records 最大记录数
 * @param actual_count 实际获取的记录数
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_manager_get_recent_records(char records[][128], 
                                                     int max_records, 
                                                     int *actual_count);

/**
 * @brief 强制上传所有记录
 * 
 * 强制将所有记录（包括已上传的）重新上传到DP118
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_manager_force_upload_all(void);

/**
 * @brief 清空所有服药记录
 * 
 * 清空Flash中的所有服药记录（谨慎使用）
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_manager_clear_all_records(void);

/**
 * @brief 设置自动上传模式
 * 
 * @param auto_upload 是否启用自动上传
 */
void medication_log_manager_set_auto_upload(bool auto_upload);

/**
 * @brief 更新服药日志管理器
 * 
 * 处理自动上传任务，需要在主循环中定期调用
 */
void medication_log_manager_update(void);

// ========== 显示函数声明 ==========

/**
 * @brief 显示状态信息
 * 
 * @param status_msg 状态消息
 */
void medication_log_manager_display_status(const char *status_msg);

/**
 * @brief 显示成功信息
 * 
 * @param success_msg 成功消息
 */
void medication_log_manager_display_success(const char *success_msg);

/**
 * @brief 打印最近的服药日志
 */
void medication_log_manager_print_recent(void);

// ========== 测试函数声明 ==========

/**
 * @brief 运行服药日志管理器完整测试
 * 
 * 该函数会测试服药日志管理器的所有功能，包括：
 * - Flash存储功能
 * - 日志记录功能
 * - DP118上传功能
 * - 统计信息管理
 */
void medication_log_manager_run_tests(void);

#ifdef __cplusplus
}
#endif

#endif /* __MEDICATION_LOG_MANAGER_H__ */
