# Ktuyaconf
config PLATFORM_CHOICE
    string
    default "T5AI"

config PLATFORM_T5
    bool
    default y

config OPERATING_SYSTEM
    int
    default 98
    ---help---
        100     /* LINUX */
        98      /* RTOS */

rsource "./TKL_Kconfig"

rsource "./OS_SERVICE_Kconfig"

choice
    prompt "Choice a board"

    config BOARD_CHOICE_TUYA_T5AI_BOARD
        bool "TUYA_T5AI_BOARD"
        if (BOARD_CHOICE_TUYA_T5AI_BOARD)
            rsource "./TUYA_T5AI_BOARD/Kconfig"
        endif

    config BOARD_CHOICE_TUYA_T5AI_EVB
        bool "TUYA_T5AI_EVB"
        if (BOARD_CHOICE_TUYA_T5AI_EVB)
            rsource "./TUYA_T5AI_EVB/Kconfig"
        endif

    config BOARD_CHOICE_TUYA_T5AI_MINI
        bool "TUYA_T5AI_MINI"
        if (BOARD_CHOICE_TUYA_T5AI_MINI)
            rsource "./TUYA_T5AI_MINI/Kconfig"
        endif

    config BOARD_CHOICE_T5AI_MOJI_1_28
        bool "T5AI_MOJI_1_28"
        if (BOARD_CHOICE_T5AI_MOJI_1_28)
            rsource "./T5AI_MOJI_1_28/Kconfig"
        endif
endchoice
