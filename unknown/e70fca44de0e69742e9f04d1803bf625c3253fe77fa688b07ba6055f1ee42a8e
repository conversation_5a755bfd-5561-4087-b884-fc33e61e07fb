/*******************************************************************************
 * Size: 16 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --force-fast-kern-format --font fa-regular-400.ttf --format lvgl --lv-include
 *lvgl.h --bpp 4 -o font_awesome_16_4.c --size 16 -r
 *0xf5a4,0xf118,0xf59b,0xf588,0xe384,0xf556,0xf5b3,0xf584,0xf579,0xe36b,0xe375,0xe39b,0xf4da,0xe398,0xe392,0xe372,0xf598,0xe409,0xe38d,0xe3a4,0xe36d,0xf240,0xf241,0xf242,0xf243,0xf244,0xf377,0xf376,0xf1eb,0xf6ab,0xf6aa,0xf6ac,0xf012,0xf68f,0xf68e,0xf68d,0xf68c,0xf695,0xf028,0xf6a8,0xf027,0xf6a9,0xf001,0xf00c,0xf00d,0xf011,0xf013,0xf1f8,0xf015,0xf03e,0xf044,0xf048,0xf051,0xf04b,0xf04c,0xf04d,0xf060,0xf061,0xf062,0xf063,0xf071,0xf0f3,0xf3c5,0xf0ac,0xf124,0xf7c2,0xf293,0xf075,0xe1ec,0xf007,0xe04b,0xf019
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef FONT_AWESOME_16_4
#define FONT_AWESOME_16_4 1
#endif

#if FONT_AWESOME_16_4

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+E04B "" */
    0x0, 0x0, 0x0, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x2c, 0xff, 0xff, 0xff, 0xb1, 0x0, 0x0,
    0xbe, 0x87, 0x77, 0x78, 0xeb, 0x0, 0xd0, 0xf8, 0x26, 0x2, 0x60, 0x8f, 0xd, 0xf0, 0xf7, 0xaf, 0x3a, 0xf3, 0x7f, 0xf,
    0xf0, 0xf7, 0x26, 0x2, 0x60, 0x7f, 0xf, 0xd0, 0xf8, 0x41, 0x41, 0x41, 0x8f, 0xd, 0x0, 0xbe, 0xda, 0xda, 0xdb, 0xfb,
    0x0, 0x0, 0x2b, 0xff, 0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xef, 0xff, 0xff, 0xff, 0xfe,
    0x80, 0x8f, 0xa7, 0x77, 0x77, 0x77, 0x7a, 0xf8, 0xea, 0x0, 0xa3, 0xa3, 0xa3, 0x0, 0xae, 0xfb, 0x77, 0xda, 0xda,
    0xda, 0x77, 0xbf, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+E1EC "" */
    0x0, 0x0, 0x75, 0x9, 0x40, 0x66, 0x0, 0x0, 0x0, 0x0, 0xca, 0xe, 0x90, 0xbb, 0x0, 0x0, 0x0, 0x2a, 0xfe, 0xcf, 0xec,
    0xff, 0xa2, 0x0, 0x0, 0xde, 0xbb, 0xbb, 0xbb, 0xbb, 0xec, 0x0, 0x68, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x86, 0xcf,
    0xf7, 0x0, 0x40, 0x0, 0x40, 0x7f, 0xfc, 0x0, 0xf7, 0x3, 0xf6, 0x1, 0xf1, 0x7f, 0x0, 0x79, 0xf7, 0xa, 0xfd, 0x1,
    0xf1, 0x7f, 0x97, 0xad, 0xf7, 0x1f, 0x9f, 0x41, 0xf1, 0x7f, 0xda, 0x0, 0xf7, 0x8f, 0xff, 0xb1, 0xf1, 0x7f, 0x0,
    0x8a, 0xf7, 0x64, 0x2, 0x80, 0x80, 0x7f, 0xa8, 0x9c, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xc9, 0x0, 0xed, 0x77, 0x77,
    0x77, 0x77, 0xde, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0xca, 0xe, 0x90, 0xbb, 0x0, 0x0, 0x0,
    0x0, 0xa7, 0xc, 0x60, 0x99, 0x0, 0x0,

    /* U+E36B "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf6, 0x3, 0x10, 0x2, 0x20, 0x6f, 0x80, 0x2f, 0x96, 0xec, 0x40, 0x7, 0xdd, 0x39, 0xf2, 0x8f,
    0x19, 0x20, 0x0, 0x0, 0x4, 0x71, 0xf8, 0xcb, 0x0, 0x4f, 0x40, 0x7, 0xf4, 0x0, 0xbc, 0xe9, 0x0, 0x4f, 0x40, 0x7,
    0xf4, 0x0, 0x9e, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xcb, 0x0, 0x0, 0x3, 0x10, 0x0, 0x0, 0xbc, 0x8f, 0x10,
    0x0, 0x7f, 0xe1, 0x0, 0x1, 0xf8, 0x2f, 0x90, 0x0, 0xbf, 0xf4, 0x0, 0x9, 0xf2, 0x8, 0xf6, 0x0, 0xaf, 0xf3, 0x0, 0x6f,
    0x80, 0x0, 0xbf, 0x91, 0x2a, 0x70, 0x19, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0x0, 0x29,
    0xdf, 0xfd, 0x92, 0x0, 0x0,

    /* U+E36D "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf2, 0x8f, 0x10,
    0x3, 0x0, 0x0, 0x30, 0x1, 0xf8, 0xcb, 0x0, 0x5f, 0x60, 0x5, 0xf6, 0x0, 0xbc, 0xe9, 0x0, 0x2a, 0x20, 0x2, 0xa2, 0x0,
    0x9e, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xcb, 0x0, 0x0, 0x2, 0x67, 0x71, 0x0, 0xbc, 0x8f, 0x10, 0x3, 0xcf,
    0xff, 0xf5, 0x1, 0xf8, 0x2f, 0x90, 0x1f, 0xe6, 0x10, 0x0, 0x9, 0xf2, 0x8, 0xf6, 0x6, 0x10, 0x0, 0x0, 0x6f, 0x80,
    0x0, 0xbf, 0x91, 0x0, 0x0, 0x19, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0x0, 0x29, 0xdf, 0xfd,
    0x92, 0x0, 0x0,

    /* U+E372 "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb8, 0x8b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x81, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x2f, 0x80, 0x5, 0x50, 0x5, 0x50, 0x9, 0xf2, 0x9f, 0x10,
    0xcf, 0xd0, 0xd, 0xfc, 0x1, 0xf9, 0xdb, 0x6, 0xf5, 0x0, 0x0, 0x5f, 0x60, 0xbd, 0xf8, 0x1, 0x50, 0x0, 0x0, 0x5, 0x10,
    0x9f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xdb, 0x0, 0x40, 0x0, 0x0, 0x4, 0x0, 0xad, 0x9f, 0x12, 0xfb, 0x10,
    0x1, 0xbf, 0x20, 0xe8, 0x2f, 0x80, 0x6f, 0xfb, 0xbf, 0xff, 0x7, 0xf2, 0x8, 0xf5, 0x2, 0x9c, 0xdf, 0x2f, 0xf, 0x90,
    0x0, 0xbf, 0x81, 0x0, 0xf, 0xf, 0xb, 0x0, 0x0, 0x8, 0xff, 0xb8, 0xd, 0xf, 0x0, 0x0, 0x0, 0x0, 0x29, 0xdf, 0x0, 0xd,
    0x0, 0x0,

    /* U+E375 "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb8, 0x8c, 0xff, 0x80, 0x0, 0x0, 0xbf, 0xc3, 0x0, 0x0,
    0x6e, 0xfb, 0x0, 0x8, 0xff, 0xe8, 0x0, 0x0, 0xbe, 0xff, 0x80, 0x2f, 0xc7, 0x66, 0x0, 0x0, 0x33, 0x7c, 0xf2, 0x8f,
    0x3e, 0xdd, 0xc0, 0xb, 0xaa, 0xb2, 0xf8, 0xcb, 0x7b, 0x62, 0xb4, 0x5a, 0x63, 0xa5, 0xbc, 0xe8, 0x7a, 0x94, 0xa4,
    0x6a, 0x96, 0xa6, 0x8e, 0xe8, 0x1e, 0xaa, 0xc0, 0x1e, 0xaa, 0xe1, 0x8e, 0xca, 0x1, 0x66, 0x0, 0x1, 0x66, 0x10, 0xac,
    0x8f, 0x0, 0x0, 0x9e, 0xd4, 0x0, 0x0, 0xf8, 0x2f, 0x80, 0x7, 0xff, 0xff, 0x10, 0x8, 0xf2, 0x8, 0xf5, 0x9, 0xff,
    0xff, 0x30, 0x5f, 0x80, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb8, 0x8b, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0,

    /* U+E384 "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x2f, 0x90, 0x3c, 0x0, 0x0, 0xc3, 0x9, 0xf2, 0x8f, 0x15,
    0xe5, 0x0, 0x0, 0x5e, 0x51, 0xf8, 0xcb, 0x5c, 0x30, 0x0, 0x0, 0x3, 0xc5, 0xbc, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9e, 0xe8, 0x6, 0x54, 0x60, 0x6, 0x44, 0x60, 0x9e, 0xcb, 0x8, 0xff, 0x70, 0x8, 0xff, 0x70, 0xbc, 0x8f, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xf8, 0x2f, 0x90, 0x0, 0x78, 0x87, 0x0, 0x9, 0xf2, 0x8, 0xf6, 0x1, 0xff, 0xff, 0x10, 0x6f, 0x80,
    0x0, 0xbf, 0x91, 0x0, 0x0, 0x19, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0x0, 0x29, 0xdf, 0xfd,
    0x92, 0x0, 0x0,

    /* U+E38D "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x82, 0xd, 0xfd, 0x0, 0x0, 0x8f, 0xfb, 0x88, 0xbf, 0xa0, 0x3e, 0x30, 0x0, 0xbf, 0x81,
    0x0, 0x0, 0x0, 0xd, 0xfd, 0x0, 0x8f, 0x50, 0x0, 0x0, 0x5f, 0xf5, 0x0, 0x0, 0x2f, 0x80, 0x0, 0x0, 0x0, 0xaa, 0x0,
    0xa2, 0x9, 0xf1, 0x0, 0x0, 0x0, 0x5f, 0xf5, 0xf, 0x80, 0xdb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbd, 0xf, 0x80, 0x64,
    0x46, 0x0, 0x64, 0x46, 0x8, 0xf0, 0xf8, 0x8, 0xff, 0x70, 0x8, 0xff, 0x70, 0x8f, 0xd, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xd0, 0x9f, 0x10, 0x0, 0x1, 0x10, 0x0, 0x1, 0xf9, 0x2, 0xf8, 0x0, 0x1, 0xff, 0x10, 0x0, 0x8f, 0x20, 0x8, 0xf5,
    0x0, 0x1f, 0xf1, 0x0, 0x5f, 0x80, 0x0, 0xb, 0xf8, 0x10, 0x11, 0x1, 0x8f, 0xb0, 0x0, 0x0, 0x8, 0xff, 0xb8, 0x8b,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x2, 0x9d, 0xff, 0xd9, 0x20, 0x0, 0x0,

    /* U+E392 "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf2, 0x8f, 0x10,
    0x25, 0x20, 0x2, 0x52, 0x1, 0xf8, 0xcb, 0x3, 0xfe, 0xf3, 0x3f, 0xef, 0x30, 0xbc, 0xe9, 0x0, 0x30, 0x30, 0x3, 0x3,
    0x0, 0x9e, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xcb, 0x0, 0x11, 0x0, 0x0, 0x11, 0x0, 0xbc, 0x8f, 0x10, 0xae,
    0x61, 0x16, 0xea, 0x1, 0xf8, 0x2f, 0x90, 0x1c, 0xff, 0xff, 0xc1, 0x9, 0xf2, 0x8, 0xf6, 0x0, 0x37, 0x73, 0x0, 0x6f,
    0x80, 0x0, 0xbf, 0x91, 0x0, 0x0, 0x19, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0x0, 0x29, 0xdf,
    0xfd, 0x92, 0x0, 0x0,

    /* U+E398 "" */
    0x0, 0x0, 0x28, 0xce, 0xec, 0x82, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb8, 0x8b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x80, 0x2f, 0xff, 0xff, 0xd8, 0x8d, 0xff, 0xff, 0xf2, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xdf, 0xff, 0xff, 0xf5, 0x5f, 0xff, 0xff, 0xfd, 0xfa, 0xff, 0xff, 0xf1,
    0x1f, 0xff, 0xff, 0xaf, 0xf8, 0x37, 0x77, 0x30, 0x3, 0x77, 0x73, 0x8f, 0xdb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbd,
    0x9f, 0x10, 0x68, 0x0, 0x0, 0x86, 0x1, 0xf9, 0x2f, 0x80, 0x7f, 0xd9, 0x9d, 0xf7, 0x8, 0xf2, 0x8, 0xf5, 0x4, 0xbf,
    0xfb, 0x40, 0x5f, 0x80, 0x0, 0xbf, 0x81, 0x0, 0x0, 0x18, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb8, 0x8b, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x28, 0xce, 0xec, 0x82, 0x0, 0x0,

    /* U+E39B "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb8, 0x8b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x18, 0xfb, 0x0, 0x8, 0xf9, 0xce, 0xb2, 0x0, 0x0, 0x5f, 0x80, 0x2f, 0x92, 0x41, 0x6c, 0x0, 0x0, 0x8, 0xf2, 0x9f,
    0x10, 0x2c, 0x30, 0x0, 0x30, 0x1, 0xf9, 0xdb, 0x0, 0x5f, 0x60, 0x5, 0xf6, 0x0, 0xbd, 0xf9, 0x0, 0x2, 0x0, 0x2, 0xa2,
    0x0, 0x8f, 0xf8, 0x0, 0x4, 0x20, 0x0, 0x0, 0x0, 0x8f, 0xda, 0x0, 0x9, 0xee, 0x60, 0x0, 0x0, 0xbd, 0x92, 0x72, 0x0,
    0x0, 0x42, 0x0, 0x1, 0xf9, 0x20, 0xf7, 0x2, 0x8e, 0xfa, 0x0, 0x8, 0xf2, 0x0, 0xfc, 0xcf, 0xfb, 0x50, 0x0, 0x5f,
    0x80, 0x0, 0xff, 0xff, 0xe0, 0x0, 0x18, 0xfb, 0x0, 0x0, 0xff, 0xff, 0x93, 0x8b, 0xff, 0x80, 0x0, 0x0, 0x8f, 0xfd,
    0x1b, 0xfd, 0x92, 0x0, 0x0,

    /* U+E3A4 "" */
    0x0, 0x0, 0x1, 0x7a, 0xca, 0x83, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xca, 0xbe, 0xfc, 0x20, 0x0, 0x0, 0xc, 0xf8, 0x10,
    0x0, 0x36, 0xde, 0x40, 0x0, 0xa, 0xf4, 0x0, 0x0, 0xdd, 0xf7, 0xce, 0x10, 0x4, 0xf6, 0x0, 0x0, 0x6f, 0x88, 0xe1,
    0xeb, 0x0, 0xbd, 0x1c, 0xf7, 0x6, 0x70, 0x9e, 0x7, 0xf2, 0xf, 0x79, 0xa7, 0xe2, 0xc, 0xdf, 0x51, 0x2f, 0x62, 0xf5,
    0x97, 0x7d, 0x20, 0x2, 0x6d, 0xd0, 0xf9, 0x3f, 0x41, 0xcd, 0x70, 0x2, 0xbf, 0xff, 0xe, 0x81, 0xf7, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xd1, 0xf6, 0xd, 0xc0, 0x0, 0x5e, 0xff, 0xff, 0xf8, 0x5f, 0x20, 0x7f, 0x40, 0x9f, 0x85, 0xa2, 0xfd,
    0x1d, 0xc0, 0x0, 0xce, 0x25, 0xf4, 0x0, 0xe, 0x3a, 0xf3, 0x0, 0x1, 0xee, 0x4a, 0x50, 0x0, 0xfc, 0xf6, 0x0, 0x0, 0x1,
    0xbf, 0xfd, 0x31, 0x9f, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xef, 0xfc, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+E409 "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x2f, 0xaa, 0xfc, 0x20, 0x2b, 0xfb, 0x29, 0xf2, 0x8f,
    0x9a, 0x3f, 0xc0, 0xb7, 0x3f, 0xb1, 0xf8, 0xcb, 0xb4, 0x2, 0xf0, 0xf0, 0x2, 0xf0, 0xbc, 0xe9, 0x7f, 0xff, 0xc0,
    0xcf, 0xff, 0xc0, 0x9e, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xcb, 0xf, 0xff, 0xff, 0xff, 0xff, 0xc0, 0xbc,
    0x8f, 0x1b, 0xff, 0xff, 0xfc, 0x8e, 0x91, 0xf8, 0x2f, 0x93, 0xff, 0xff, 0xa0, 0x5e, 0x19, 0xf2, 0x8, 0xf6, 0x5f,
    0xff, 0x7a, 0xe3, 0x6f, 0x80, 0x0, 0xbf, 0x92, 0x7a, 0xa6, 0x29, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xec, 0x0, 0x0, 0x0, 0x0, 0x5, 0xaf, 0xff, 0xef, 0x0, 0x0, 0x1, 0x6b, 0xff,
    0xe9, 0x40, 0x7f, 0x0, 0x0, 0x5f, 0xfd, 0x83, 0x0, 0x0, 0x8f, 0x0, 0x0, 0x7f, 0x20, 0x0, 0x2, 0x7c, 0xff, 0x0, 0x0,
    0x7f, 0x0, 0x38, 0xdf, 0xfd, 0xcf, 0x0, 0x0, 0x7f, 0x9e, 0xff, 0xc7, 0x20, 0x7f, 0x0, 0x0, 0x7f, 0xfa, 0x51, 0x0,
    0x0, 0x7f, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x19, 0xef, 0xdf, 0x0, 0x0, 0x7f,
    0x0, 0x0, 0xaf, 0x99, 0xff, 0x19, 0xef, 0xdf, 0x0, 0x0, 0xf9, 0x0, 0x9f, 0xaf, 0x99, 0xff, 0x0, 0x0, 0xaf, 0x99,
    0xfa, 0xf9, 0x0, 0x9f, 0x0, 0x0, 0x19, 0xee, 0x91, 0xaf, 0x99, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19, 0xee, 0x91, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F007 "" */
    0x0, 0x0, 0x2a, 0xee, 0xa2, 0x0, 0x0, 0x0, 0x2, 0xee, 0x99, 0xee, 0x20, 0x0, 0x0, 0xa, 0xe2, 0x0, 0x2e, 0xa0, 0x0,
    0x0, 0xf, 0x90, 0x0, 0x9, 0xe0, 0x0, 0x0, 0xf, 0x90, 0x0, 0x9, 0xe0, 0x0, 0x0, 0xa, 0xe2, 0x0, 0x2e, 0xa0, 0x0, 0x0,
    0x2, 0xee, 0x99, 0xee, 0x20, 0x0, 0x0, 0x0, 0x2a, 0xff, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x57, 0x88, 0x75, 0x10, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xa, 0xfa, 0x30, 0x0, 0x3, 0xaf, 0xa0, 0x5f,
    0x70, 0x0, 0x0, 0x0, 0x7, 0xf5, 0xbd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdb, 0xfc, 0x88, 0x88, 0x88, 0x88, 0x88, 0xce, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x80, 0x0, 0xc, 0x80, 0x0, 0x0, 0x8,
    0xf8, 0x0, 0x0, 0x8, 0xf8, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x0, 0x0, 0x9f, 0x80, 0x8, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf8, 0x8f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x70, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0x50, 0x0, 0x0, 0x6, 0xa0, 0xa, 0xf6, 0x0, 0x0, 0x6f, 0xa0, 0x0, 0xaf, 0x60, 0x6,
    0xfa, 0x0, 0x0, 0xa, 0xf7, 0x7f, 0xa0, 0x0, 0x0, 0x0, 0xaf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0,
    0x7, 0xfa, 0xaf, 0x70, 0x0, 0x0, 0x8f, 0x90, 0x9, 0xf8, 0x0, 0x8, 0xf9, 0x0, 0x0, 0x9f, 0x80, 0xd, 0x80, 0x0, 0x0,
    0x8, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x2d, 0x50, 0xe, 0x90, 0xd, 0x80, 0x0, 0x2, 0xed, 0x10, 0xe, 0x90, 0x7, 0xf8, 0x0, 0xb, 0xe1, 0x0, 0xe,
    0x90, 0x0, 0x8f, 0x30, 0x3f, 0x60, 0x0, 0xe, 0x90, 0x0, 0xe, 0xb0, 0x8f, 0x0, 0x0, 0xe, 0x90, 0x0, 0x8, 0xf0, 0xad,
    0x0, 0x0, 0xe, 0x90, 0x0, 0x5, 0xf2, 0xbc, 0x0, 0x0, 0xb, 0x60, 0x0, 0x4, 0xf3, 0x9e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xf1, 0x5f, 0x30, 0x0, 0x0, 0x0, 0x0, 0xb, 0xd0, 0xe, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x60, 0x4, 0xf9, 0x0, 0x0,
    0x0, 0x3, 0xec, 0x0, 0x0, 0x7f, 0xc4, 0x0, 0x1, 0x8f, 0xd1, 0x0, 0x0, 0x4, 0xdf, 0xfd, 0xef, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x8a, 0x96, 0x10, 0x0, 0x0,

    /* U+F012 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc6, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0xe9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xc6, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0,
    0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0x0, 0xc6, 0x0, 0xe9, 0x0, 0xe9,
    0x0, 0xe9, 0x0, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0xc6,
    0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0xe9, 0x0, 0xe9, 0x0,
    0xe9, 0x0, 0xe9, 0x0, 0xe9, 0xc6, 0x0, 0xc6, 0x0, 0xc6, 0x0, 0xc6, 0x0, 0xc6,

    /* U+F013 "" */
    0x0, 0x0, 0x4, 0xdf, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0, 0xc, 0xea, 0xaf, 0x90, 0x0, 0x0, 0x0, 0x88, 0x5f, 0x80, 0xb,
    0xd4, 0x86, 0x0, 0x9, 0xff, 0xfe, 0x10, 0x3, 0xff, 0xff, 0x60, 0x3f, 0x81, 0x30, 0x0, 0x0, 0x13, 0xb, 0xf0, 0xae,
    0x0, 0x0, 0x8d, 0xc5, 0x0, 0x3, 0xf6, 0x9f, 0x50, 0x9, 0xf9, 0xbf, 0x50, 0x8, 0xf6, 0xb, 0xf0, 0xf, 0x80, 0xc, 0xc0,
    0x3f, 0x90, 0x9, 0xf1, 0xf, 0x80, 0xc, 0xd0, 0x4f, 0x60, 0x7f, 0x80, 0xb, 0xf9, 0xbf, 0x80, 0xb, 0xf4, 0xae, 0x0,
    0x1, 0xbf, 0xf9, 0x0, 0x2, 0xf7, 0x4f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf1, 0xb, 0xfd, 0xfb, 0x0, 0x2, 0xdf, 0xcf,
    0x80, 0x1, 0xbb, 0x8f, 0x70, 0xa, 0xe8, 0xca, 0x0, 0x0, 0x0, 0xd, 0xd6, 0x7e, 0x90, 0x0, 0x0, 0x0, 0x0, 0x5, 0xdf,
    0xfc, 0x20, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe3, 0x3e, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfd,
    0x10, 0x1, 0xdf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0x0, 0xb, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xb0, 0x0, 0x1, 0xdf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfd, 0x10, 0xd, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xd0, 0x5, 0x2f, 0x70, 0x3, 0x77, 0x77, 0x30, 0x7, 0xf2, 0x50, 0x0, 0xf, 0x70, 0xe, 0xff, 0xff, 0xe0,
    0x7, 0xf0, 0x0, 0x0, 0xf, 0x70, 0xf, 0x70, 0x7, 0xf0, 0x7, 0xf0, 0x0, 0x0, 0xf, 0x70, 0xf, 0x70, 0x7, 0xf0, 0x7,
    0xf0, 0x0, 0x0, 0xf, 0x70, 0xf, 0x70, 0x7, 0xf0, 0x7, 0xf0, 0x0, 0x0, 0xf, 0x80, 0xf, 0x70, 0x7, 0xf0, 0x8, 0xf0,
    0x0, 0x0, 0xb, 0xe8, 0x7f, 0xb7, 0x7b, 0xf7, 0x8e, 0xb0, 0x0, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20,
    0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0xc, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x1e, 0x60, 0xe, 0x90, 0xa, 0xa0, 0x0, 0x0, 0xb, 0xf6, 0xe, 0x90, 0xbf, 0x50, 0x0, 0x0,
    0x0, 0xbf, 0x6e, 0x9b, 0xf6, 0x0, 0x0, 0x6, 0x77, 0x2b, 0xff, 0xff, 0x64, 0x77, 0x40, 0xaf, 0xff, 0xd1, 0xbf, 0xf6,
    0x4f, 0xff, 0xf5, 0xf9, 0x0, 0x0, 0x9, 0x50, 0x0, 0x0, 0xda, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x2, 0xe1, 0xca, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x60, 0xca, 0xed, 0x77, 0x77, 0x77, 0x77, 0x77, 0x78, 0xf9, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x1, 0xbc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0x0, 0x0, 0x0, 0x0, 0x3, 0xee, 0xbf, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xd2, 0x7f, 0x0, 0x0, 0x6f, 0xff, 0xfc, 0x10, 0x7f, 0x0, 0x60, 0xfc, 0x77, 0x70, 0x0, 0x7f, 0x3, 0xf8,
    0xf7, 0x0, 0x0, 0x0, 0x7f, 0x0, 0xae, 0xf7, 0x0, 0x0, 0x0, 0x7f, 0x0, 0xae, 0xfc, 0x77, 0x70, 0x0, 0x7f, 0x3, 0xf8,
    0x6f, 0xff, 0xfc, 0x10, 0x7f, 0x0, 0x60, 0x0, 0x0, 0x4f, 0xd2, 0x7f, 0x0, 0x0, 0x0, 0x0, 0x3, 0xee, 0xbf, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcc, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbc, 0x0, 0x0, 0x6, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xff, 0x0, 0x0, 0x1, 0xde, 0x20, 0x0, 0x0, 0x3, 0xee, 0xbf, 0x0, 0x3, 0xb1, 0x1e, 0xb0, 0x0, 0x0, 0x4f, 0xd2,
    0x7f, 0x0, 0x2, 0xfc, 0x6, 0xf3, 0x6f, 0xff, 0xfc, 0x10, 0x7f, 0x0, 0x60, 0x4f, 0x60, 0xf9, 0xfc, 0x77, 0x70, 0x0,
    0x7f, 0x3, 0xf8, 0xc, 0xc0, 0xad, 0xf7, 0x0, 0x0, 0x0, 0x7f, 0x0, 0xae, 0x8, 0xf0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x7f,
    0x0, 0xae, 0x9, 0xf0, 0x8f, 0xfc, 0x77, 0x70, 0x0, 0x7f, 0x3, 0xf8, 0xc, 0xc0, 0xad, 0x6f, 0xff, 0xfc, 0x10, 0x7f,
    0x0, 0x60, 0x4f, 0x60, 0xf9, 0x0, 0x0, 0x4f, 0xd2, 0x7f, 0x0, 0x2, 0xfc, 0x6, 0xf3, 0x0, 0x0, 0x3, 0xee, 0xbf, 0x0,
    0x3, 0xb1, 0x1e, 0xb0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0x0, 0x0, 0x1, 0xdf, 0x20, 0x0, 0x0, 0x0, 0x1, 0xcc, 0x0, 0x0,
    0x6, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0xed, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0xde, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf7, 0x8, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0xf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7,
    0x8, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x4e, 0x40, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x1, 0xef, 0xe1,
    0x0, 0x7f, 0xf7, 0x1, 0xd7, 0xc, 0xff, 0xfc, 0x0, 0x7f, 0xf7, 0xb, 0xff, 0xbf, 0xff, 0xff, 0x90, 0x7f, 0xf7, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x7f, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4,

    /* U+F044 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbd, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xec, 0xf7, 0xa, 0xff, 0xff, 0xd0, 0x2,
    0xef, 0x20, 0xcd, 0xaf, 0x97, 0x77, 0x50, 0x2e, 0xff, 0x82, 0xeb, 0xf9, 0x0, 0x0, 0x2, 0xee, 0x29, 0xff, 0xe2, 0xf7,
    0x0, 0x0, 0x2e, 0xe2, 0x2, 0xfe, 0x20, 0xf7, 0x0, 0x2, 0xee, 0x20, 0x2e, 0xe2, 0x0, 0xf7, 0x0, 0xd, 0xe2, 0x2, 0xee,
    0x20, 0x0, 0xf7, 0x0, 0x3f, 0x50, 0x2e, 0xe2, 0x0, 0x0, 0xf7, 0x0, 0x8f, 0x15, 0xee, 0x20, 0x5d, 0x0, 0xf7, 0x0,
    0xdf, 0xff, 0xd2, 0x0, 0x7f, 0x0, 0xf7, 0x0, 0xdd, 0x83, 0x0, 0x0, 0x7f, 0x0, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0x0, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x0, 0xaf, 0x97, 0x77, 0x77, 0x77, 0x79, 0xfa, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0,

    /* U+F048 "" */
    0xd5, 0x0, 0x0, 0x7e, 0x5f, 0x70, 0x2, 0xcf, 0xf7, 0xf7, 0x5, 0xff, 0x6f, 0x7f, 0x9a, 0xfc, 0x20, 0xf7, 0xff, 0xf7,
    0x0, 0xf, 0x7f, 0xd3, 0x0, 0x0, 0xf7, 0xfd, 0x30, 0x0, 0xf, 0x7f, 0xff, 0x70, 0x0, 0xf7, 0xf9, 0xaf, 0xc1, 0xf,
    0x7f, 0x70, 0x5f, 0xe5, 0xf7, 0xf7, 0x0, 0x2c, 0xff, 0x7d, 0x50, 0x0, 0x7, 0xe5,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8e, 0xa1, 0x0, 0x0, 0x0, 0x0, 0xfd, 0xff, 0x60, 0x0, 0x0, 0x0, 0xf8, 0x2b, 0xfc,
    0x30, 0x0, 0x0, 0xf8, 0x0, 0x5e, 0xf9, 0x10, 0x0, 0xf8, 0x0, 0x0, 0x8f, 0xe5, 0x0, 0xf8, 0x0, 0x0, 0x2, 0xcf, 0xc2,
    0xf8, 0x0, 0x0, 0x0, 0x6, 0xed, 0xf8, 0x0, 0x0, 0x0, 0x6, 0xed, 0xf8, 0x0, 0x0, 0x2, 0xcf, 0xc2, 0xf8, 0x0, 0x0,
    0x8f, 0xe5, 0x0, 0xf8, 0x0, 0x5e, 0xf9, 0x10, 0x0, 0xf8, 0x2b, 0xfc, 0x30, 0x0, 0x0, 0xfd, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x8f, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x8f, 0xfd, 0x22, 0xdf, 0xf8, 0xfc, 0x8f, 0x77, 0xf8, 0xcf, 0xf8, 0xf, 0x88, 0xf0, 0x8f, 0xf8, 0xf, 0x88, 0xf0,
    0x8f, 0xf8, 0xf, 0x88, 0xf0, 0x8f, 0xf8, 0xf, 0x88, 0xf0, 0x8f, 0xf8, 0xf, 0x88, 0xf0, 0x8f, 0xf8, 0xf, 0x88, 0xf0,
    0x8f, 0xf8, 0xf, 0x88, 0xf0, 0x8f, 0xf8, 0xf, 0x88, 0xf0, 0x8f, 0xfc, 0x8f, 0x77, 0xf8, 0xcf, 0x8f, 0xfd, 0x22,
    0xdf, 0xf8,

    /* U+F04D "" */
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xe4, 0xed, 0x77, 0x77, 0x77, 0x77, 0xde, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xed, 0x77, 0x77,
    0x77, 0x77, 0xde, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xe4,

    /* U+F051 "" */
    0x5e, 0x70, 0x0, 0x5, 0xd8, 0xff, 0xc1, 0x0, 0x8f, 0x8f, 0x6f, 0xf5, 0x8, 0xf8, 0xf0, 0x2c, 0xfa, 0x9f, 0x8f, 0x0,
    0x7, 0xff, 0xf8, 0xf0, 0x0, 0x3, 0xdf, 0x8f, 0x0, 0x0, 0x3d, 0xf8, 0xf0, 0x0, 0x7f, 0xff, 0x8f, 0x1, 0xcf, 0xa9,
    0xf8, 0xf5, 0xff, 0x50, 0x8f, 0x8f, 0xfc, 0x20, 0x8, 0xf5, 0xe7, 0x0, 0x0, 0x5d,

    /* U+F060 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xe8,
    0x88, 0x88, 0x88, 0x88, 0x86, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x1d, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xdf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,

    /* U+F061 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x80, 0x68, 0x88,
    0x88, 0x88, 0x88, 0x8e, 0xf9, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,

    /* U+F062 "" */
    0x0, 0x0, 0x1, 0xd9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0x80, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xfe, 0xf8, 0x0, 0x0, 0x0,
    0xc, 0xf4, 0xe9, 0x9f, 0x70, 0x0, 0x0, 0xcf, 0x40, 0xe9, 0xa, 0xf6, 0x0, 0xb, 0xf5, 0x0, 0xe9, 0x0, 0xaf, 0x50,
    0x1e, 0x50, 0x0, 0xe9, 0x0, 0xa, 0xa0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc6, 0x0, 0x0, 0x0,

    /* U+F063 "" */
    0x0, 0x0, 0x0, 0xc6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe9, 0x0, 0x0, 0x0, 0xe, 0x60, 0x0, 0xe9, 0x0, 0xa, 0xa0, 0xb, 0xf5, 0x0, 0xe9, 0x0, 0xaf, 0x60, 0x0, 0xcf,
    0x40, 0xe9, 0x9, 0xf7, 0x0, 0x0, 0xc, 0xf4, 0xe9, 0x9f, 0x70, 0x0, 0x0, 0x1, 0xdf, 0xfe, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x1, 0xd9, 0x0, 0x0, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x3d, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xed, 0xde, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf3, 0x3f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x90, 0x9, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xbe, 0x1a, 0x61, 0xeb, 0x0, 0x0, 0x0, 0x5, 0xf6,
    0xe, 0x90, 0x6f, 0x50, 0x0, 0x0, 0x1e, 0xc0, 0xe, 0x90, 0xc, 0xe0, 0x0, 0x0, 0x9f, 0x30, 0xe, 0x90, 0x3, 0xf9, 0x0,
    0x3, 0xf9, 0x0, 0x7, 0x30, 0x0, 0x9f, 0x30, 0xc, 0xe1, 0x0, 0x2, 0x10, 0x0, 0xe, 0xc0, 0x6f, 0x60, 0x0, 0x1f, 0xc0,
    0x0, 0x5, 0xf6, 0xec, 0x0, 0x0, 0x9, 0x60, 0x0, 0x0, 0xce, 0xec, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0xce, 0x5e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,

    /* U+F075 "" */
    0x0, 0x0, 0x4, 0xad, 0xff, 0xda, 0x40, 0x0, 0x0, 0x0, 0x3c, 0xfe, 0xa8, 0x8a, 0xef, 0xc3, 0x0, 0x0, 0x4f, 0xd4, 0x0,
    0x0, 0x0, 0x4d, 0xf4, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x9, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0x90, 0xea, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xae, 0xf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf0, 0xea, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xae, 0x9, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x90, 0x2f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x0,
    0x9f, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xf4, 0x0, 0xd, 0xc8, 0xfe, 0xa8, 0x8a, 0xef, 0xd3, 0x0, 0x5, 0xff, 0xfa, 0xad,
    0xff, 0xda, 0x40, 0x0, 0x0, 0xee, 0x92, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0AC "" */
    0x0, 0x0, 0x28, 0xce, 0xec, 0x82, 0x0, 0x0, 0x0, 0x8, 0xff, 0xfb, 0xbf, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x9e, 0xc0, 0xc,
    0xe9, 0xfb, 0x0, 0x8, 0xf5, 0x4f, 0x40, 0x4, 0xf4, 0x5f, 0x80, 0x2f, 0x80, 0x9f, 0x0, 0x0, 0xf9, 0x8, 0xf2, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xdd, 0x88, 0xfc, 0x88, 0x88, 0xcf, 0x88, 0xdd, 0xf8, 0x0, 0xf8, 0x0, 0x0,
    0x8f, 0x0, 0x9f, 0xf8, 0x0, 0xf8, 0x0, 0x0, 0x8f, 0x0, 0x9f, 0xdd, 0x88, 0xfc, 0x88, 0x88, 0xcf, 0x88, 0xdd, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x2f, 0x80, 0x9f, 0x0, 0x0, 0xf9, 0x8, 0xf2, 0x8, 0xf5, 0x4f, 0x40, 0x4,
    0xf4, 0x5f, 0x80, 0x0, 0xbf, 0x9e, 0xc0, 0xc, 0xe9, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xfb, 0xbf, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x28, 0xce, 0xec, 0x82, 0x0, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0xc, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x1, 0xef, 0x61, 0x16, 0xfe, 0x10, 0x0, 0x0, 0x8, 0xf3, 0x0, 0x0, 0x3f, 0x80, 0x0, 0x0, 0xe, 0xb0,
    0x0, 0x0, 0xb, 0xd0, 0x0, 0x0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0x0, 0x0, 0xf, 0x70, 0x0, 0x0, 0x7, 0xf0, 0x0, 0x0,
    0x2f, 0x60, 0x0, 0x0, 0x6, 0xf2, 0x0, 0x0, 0x7f, 0x20, 0x0, 0x0, 0x2, 0xf6, 0x0, 0x0, 0xec, 0x0, 0x0, 0x0, 0x0,
    0xce, 0x0, 0x9, 0xfb, 0x77, 0x77, 0x77, 0x77, 0xbf, 0x90, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xe4, 0x0, 0x0, 0x0,

    /* U+F118 "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf2, 0x8f, 0x10,
    0x3, 0x0, 0x0, 0x30, 0x1, 0xf8, 0xcb, 0x0, 0x5f, 0x60, 0x5, 0xf6, 0x0, 0xbc, 0xe9, 0x0, 0x2a, 0x20, 0x2, 0xa2, 0x0,
    0x9e, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xcb, 0x0, 0x11, 0x0, 0x0, 0x11, 0x0, 0xbc, 0x8f, 0x10, 0xae, 0x61,
    0x16, 0xea, 0x1, 0xf8, 0x2f, 0x90, 0x1c, 0xff, 0xff, 0xc1, 0x9, 0xf2, 0x8, 0xf6, 0x0, 0x37, 0x73, 0x0, 0x6f, 0x80,
    0x0, 0xbf, 0x91, 0x0, 0x0, 0x19, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0x0, 0x29, 0xdf, 0xfd,
    0x92, 0x0, 0x0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x71, 0x0, 0x0, 0x0, 0x0, 0x5, 0xcf, 0xf7, 0x0, 0x0, 0x0, 0x28, 0xef, 0xdc, 0xf2, 0x0,
    0x0, 0x5b, 0xff, 0xa4, 0xd, 0xc0, 0x1, 0x7e, 0xfe, 0x71, 0x0, 0x4f, 0x50, 0x4f, 0xff, 0xc8, 0x75, 0x0, 0xae, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0x51, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xf, 0x77, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xf, 0x8e, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xcf, 0x50, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x40, 0x0, 0x0,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x48, 0xcd, 0xff, 0xdc, 0x84, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e, 0xff, 0xca, 0x88, 0xac, 0xff, 0xe7, 0x0,
    0x0, 0x0, 0x4e, 0xfb, 0x40, 0x0, 0x0, 0x0, 0x4, 0xbf, 0xe3, 0x0, 0x7, 0xfd, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xdf, 0x70, 0xc, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x15, 0x77, 0x51, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4c, 0xff, 0xff, 0xff, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x72, 0x0, 0x27, 0xdf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x70, 0x0, 0x0, 0x0, 0x7, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x55, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xdd, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F1F8 "" */
    0x0, 0x0, 0x8f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x77, 0x9f, 0x60, 0x0, 0x57, 0x8f, 0xf8, 0x77, 0x8f, 0xf8,
    0x75, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xd, 0xa0, 0x0, 0x0, 0x0, 0xa, 0xd0, 0xc, 0xb0, 0x0, 0x0, 0x0, 0xb,
    0xc0, 0xb, 0xc0, 0x0, 0x0, 0x0, 0xc, 0xb0, 0x9, 0xd0, 0x0, 0x0, 0x0, 0xe, 0x90, 0x8, 0xf0, 0x0, 0x0, 0x0, 0xf, 0x80,
    0x7, 0xf0, 0x0, 0x0, 0x0, 0xf, 0x70, 0x6, 0xf1, 0x0, 0x0, 0x0, 0x1f, 0x60, 0x5, 0xf2, 0x0, 0x0, 0x0, 0x2f, 0x50,
    0x3, 0xf3, 0x0, 0x0, 0x0, 0x3f, 0x30, 0x2, 0xf4, 0x0, 0x0, 0x0, 0x4f, 0x20, 0x0, 0xfb, 0x77, 0x77, 0x77, 0xbf, 0x0,
    0x0, 0x6e, 0xff, 0xff, 0xff, 0xe6, 0x0,

    /* U+F240 "" */
    0x2b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0xbe, 0x87, 0x77, 0x77, 0x77, 0x77, 0x77, 0x8e, 0xb0, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf0, 0xf7, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7, 0xfc, 0xf7, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x7, 0xff, 0xf7, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7, 0xff, 0xf7, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x7, 0xfc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf0, 0xbe, 0x87, 0x77, 0x77, 0x77, 0x77, 0x77, 0x8e,
    0xb0, 0x2b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20,

    /* U+F241 "" */
    0x2b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0xbe, 0x87, 0x77, 0x77, 0x77, 0x77, 0x77, 0x8e, 0xb0, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf0, 0xf7, 0xf, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x7, 0xfc, 0xf7, 0xf, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x7, 0xff, 0xf7, 0xf, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x7, 0xff, 0xf7, 0xf, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x7, 0xfc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf0, 0xbe, 0x87, 0x77, 0x77, 0x77, 0x77, 0x77, 0x8e, 0xb0,
    0x2b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20,

    /* U+F242 "" */
    0x2b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0xbe, 0x87, 0x77, 0x77, 0x77, 0x77, 0x77, 0x8e, 0xb0, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf0, 0xf7, 0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x7, 0xfc, 0xf7, 0xf, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x7, 0xff, 0xf7, 0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x7, 0xff, 0xf7, 0xf, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x7, 0xfc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf0, 0xbe, 0x87, 0x77, 0x77, 0x77, 0x77, 0x77, 0x8e, 0xb0,
    0x2b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20,

    /* U+F243 "" */
    0x2b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0xbe, 0x87, 0x77, 0x77, 0x77, 0x77, 0x77, 0x8e, 0xb0, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf0, 0xf7, 0xf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfc, 0xf7, 0xf, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xf7, 0xf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf7, 0xf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfc,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf0, 0xbe, 0x87, 0x77, 0x77, 0x77, 0x77, 0x77, 0x8e, 0xb0, 0x2b, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20,

    /* U+F244 "" */
    0x2b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0xbe, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e, 0xb0, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf0, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfc, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfc,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf0, 0xbe, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e, 0xb0, 0x2b, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xca, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfb, 0x10, 0x0, 0x0, 0x0, 0xed, 0xfd, 0x10,
    0x2, 0x0, 0xe, 0x93, 0xee, 0x21, 0xfb, 0x0, 0xe9, 0x5, 0xfb, 0x6, 0xfd, 0x2e, 0x95, 0xfd, 0x20, 0x4, 0xee, 0xfe,
    0xfc, 0x10, 0x0, 0x2, 0xdf, 0xf9, 0x0, 0x0, 0x0, 0x2d, 0xff, 0x90, 0x0, 0x0, 0x4e, 0xef, 0xef, 0xb1, 0x0, 0x6f,
    0xd2, 0xe9, 0x5f, 0xd2, 0x1f, 0xb0, 0xe, 0x90, 0x5f, 0xb0, 0x20, 0x0, 0xe9, 0x3e, 0xe2, 0x0, 0x0, 0xe, 0xdf, 0xd1,
    0x0, 0x0, 0x0, 0xef, 0xc1, 0x0, 0x0, 0x0, 0xc, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F376 "" */
    0x2b, 0xff, 0xff, 0xff, 0xa1, 0x52, 0xff, 0xfb, 0x20, 0xbe, 0x87, 0x77, 0x76, 0x1c, 0xc0, 0x77, 0x8e, 0xb0, 0xf8,
    0x0, 0x0, 0x3, 0xef, 0x40, 0x0, 0x8, 0xf0, 0xf7, 0x0, 0x0, 0x6f, 0xfc, 0x0, 0x0, 0x7, 0xfc, 0xf7, 0x0, 0x9, 0xff,
    0xfb, 0x76, 0x0, 0x7, 0xff, 0xf7, 0x0, 0x6, 0x7b, 0xff, 0xfa, 0x0, 0x7, 0xff, 0xf7, 0x0, 0x0, 0xc, 0xff, 0x70, 0x0,
    0x7, 0xfc, 0xf8, 0x0, 0x0, 0x4f, 0xe4, 0x0, 0x0, 0x8, 0xf0, 0xbe, 0x87, 0x70, 0xcd, 0x26, 0x77, 0x77, 0x8e, 0xb0,
    0x2b, 0xff, 0xf2, 0x61, 0xaf, 0xff, 0xff, 0xfb, 0x20,

    /* U+F377 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0x0, 0x0, 0x81, 0xa, 0xff, 0x87, 0x77, 0x77, 0x77, 0x8e, 0xb0,
    0x0, 0x0, 0xf8, 0x0, 0x6f, 0xd2, 0x0, 0x0, 0x0, 0x8, 0xf0, 0x0, 0x0, 0xf7, 0x0, 0x3, 0xef, 0x50, 0x0, 0x0, 0x7,
    0xfc, 0x0, 0x0, 0xf7, 0x0, 0x0, 0x1c, 0xf8, 0x0, 0x0, 0x7, 0xff, 0x0, 0x0, 0xf7, 0x0, 0x0, 0x0, 0x9f, 0xb1, 0x0,
    0x7, 0xff, 0x0, 0x0, 0xf7, 0x0, 0x0, 0x0, 0x5, 0xfe, 0x30, 0x7, 0xfc, 0x0, 0x0, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x2d,
    0xf6, 0x8, 0xf0, 0x0, 0x0, 0xbe, 0x87, 0x77, 0x77, 0x77, 0x11, 0xbf, 0xbe, 0xb0, 0x0, 0x0, 0x2b, 0xff, 0xff, 0xff,
    0xff, 0xd2, 0x7, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xcf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F3C5 "" */
    0x0, 0x6, 0xcf, 0xfc, 0x60, 0x0, 0x1, 0xdf, 0xc8, 0x8c, 0xfd, 0x10, 0xd, 0xe4, 0x0, 0x0, 0x4e, 0xd0, 0x6f, 0x40,
    0x16, 0x61, 0x4, 0xf6, 0xcc, 0x1, 0xdf, 0xfd, 0x10, 0xcc, 0xf8, 0x6, 0xf3, 0x3f, 0x60, 0x8f, 0xe9, 0x6, 0xf3, 0x3f,
    0x60, 0x9e, 0xbd, 0x1, 0xdf, 0xfd, 0x10, 0xdb, 0x5f, 0x50, 0x16, 0x61, 0x5, 0xf4, 0xd, 0xd0, 0x0, 0x0, 0xd, 0xd0,
    0x3, 0xf7, 0x0, 0x0, 0x7f, 0x30, 0x0, 0x9f, 0x20, 0x2, 0xf9, 0x0, 0x0, 0x1e, 0xc0, 0xc, 0xe1, 0x0, 0x0, 0x5, 0xf8,
    0x8f, 0x50, 0x0, 0x0, 0x0, 0x9f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xb, 0xb0, 0x0, 0x0,

    /* U+F4DA "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf2, 0x8f, 0x10,
    0x3, 0x0, 0x0, 0x10, 0x1, 0xf8, 0xcb, 0x0, 0x5f, 0x60, 0x2e, 0xfd, 0x20, 0xbc, 0xe9, 0x0, 0x2a, 0x20, 0x27, 0x27,
    0x10, 0x9e, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xcb, 0x0, 0x11, 0x0, 0x0, 0x11, 0x0, 0xbc, 0x8f, 0x10, 0xae,
    0x61, 0x16, 0xea, 0x1, 0xf8, 0x2f, 0x90, 0x1c, 0xff, 0xff, 0xc1, 0x9, 0xf2, 0x8, 0xf6, 0x0, 0x37, 0x73, 0x0, 0x6f,
    0x80, 0x0, 0xbf, 0x91, 0x0, 0x0, 0x19, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0x0, 0x29, 0xdf,
    0xfd, 0x92, 0x0, 0x0,

    /* U+F556 "" */
    0x0, 0x0, 0x28, 0xce, 0xec, 0x82, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf2, 0x9f, 0x12,
    0x50, 0x0, 0x0, 0x5, 0x21, 0xf9, 0xdb, 0x2, 0xbf, 0xa2, 0x2a, 0xfb, 0x20, 0xbd, 0xf9, 0x0, 0x6f, 0xb2, 0x2a, 0xf6,
    0x0, 0x9f, 0xf8, 0x0, 0x17, 0x10, 0x1, 0x71, 0x0, 0x9f, 0xdb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbd, 0x9f, 0x10, 0x0,
    0x5a, 0xa4, 0x0, 0x1, 0xf9, 0x2f, 0x90, 0x5, 0xfd, 0xdf, 0x50, 0x9, 0xf2, 0x8, 0xf6, 0x1, 0x50, 0x5, 0x10, 0x6f,
    0x80, 0x0, 0xbf, 0x91, 0x0, 0x0, 0x19, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb8, 0x8b, 0xff, 0x80, 0x0, 0x0, 0x0, 0x28, 0xce,
    0xec, 0x82, 0x0, 0x0,

    /* U+F579 "" */
    0x0, 0x0, 0x28, 0xce, 0xec, 0x82, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x2f, 0x90, 0x33, 0x0, 0x2, 0x75, 0x9, 0xf2, 0x9f, 0x1b,
    0xdd, 0xb0, 0x3e, 0x9c, 0xb1, 0xf9, 0xdb, 0x5c, 0x43, 0xc5, 0xa7, 0x52, 0xd3, 0xbd, 0xf8, 0x79, 0xa7, 0x97, 0xa6,
    0xc5, 0xd3, 0x9f, 0xf8, 0x2e, 0x67, 0xe2, 0x3e, 0x9c, 0xb0, 0x9f, 0xdb, 0x3, 0xaa, 0x30, 0x2, 0x75, 0x0, 0xbd, 0x9f,
    0x10, 0x4, 0x77, 0x77, 0x10, 0x1, 0xf9, 0x2f, 0x90, 0xb, 0xff, 0xff, 0x50, 0x9, 0xf2, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x80, 0x0, 0xbf, 0x91, 0x0, 0x0, 0x19, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb8, 0x9b, 0xff, 0x80, 0x0, 0x0, 0x0, 0x28,
    0xce, 0xec, 0x82, 0x0, 0x0,

    /* U+F584 "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x2f, 0x90, 0x3, 0xa2, 0x2a, 0x20, 0x9, 0xf2, 0x8f,
    0x11, 0x9d, 0xf7, 0x7f, 0xc9, 0x11, 0xf8, 0xcb, 0x6, 0xff, 0xf3, 0x3f, 0xff, 0x60, 0xbc, 0xe9, 0x0, 0x7b, 0xd0, 0xd,
    0xb7, 0x0, 0x9e, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xcb, 0x0, 0x34, 0x20, 0x2, 0x43, 0x0, 0xbc, 0x8f, 0x10,
    0xaf, 0xff, 0xff, 0xfa, 0x1, 0xf8, 0x2f, 0x90, 0xb, 0xff, 0xff, 0xb0, 0x9, 0xf2, 0x8, 0xf6, 0x0, 0x37, 0x73, 0x0,
    0x6f, 0x80, 0x0, 0xbf, 0x91, 0x0, 0x0, 0x19, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0x0, 0x29,
    0xdf, 0xfd, 0x92, 0x0, 0x0,

    /* U+F588 "" */
    0x0, 0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xb8, 0x8b, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x81, 0x0, 0x0, 0x18, 0xfb, 0x0, 0x0, 0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x90, 0x0, 0x0, 0x2f,
    0x80, 0x2, 0x0, 0x0, 0x20, 0x8, 0xf2, 0x0, 0x0, 0x9f, 0x10, 0xaf, 0xa0, 0xa, 0xfa, 0x1, 0xf8, 0x0, 0x0, 0x98, 0x2,
    0xc5, 0xc2, 0x2c, 0x5c, 0x20, 0x89, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x0, 0x7f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf7, 0xff, 0xf0, 0x0, 0x34, 0x20, 0x2, 0x43, 0x0, 0xf, 0xff, 0x8f, 0x83, 0x10, 0xaf,
    0xff, 0xff, 0xfa, 0x1, 0x48, 0xf8, 0x0, 0xd, 0x90, 0xb, 0xff, 0xff, 0xb0, 0x9, 0xd0, 0x0, 0x0, 0x8, 0xf6, 0x0, 0x37,
    0x73, 0x0, 0x6f, 0x80, 0x0, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xb8, 0x8b,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0,

    /* U+F598 "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfb, 0x88, 0xbf, 0xf8, 0x0, 0x0, 0x0, 0xbf, 0x81, 0x0,
    0x0, 0x19, 0xfb, 0x0, 0x0, 0x8f, 0x50, 0x0, 0x0, 0x0, 0x6, 0xf8, 0x0, 0x2f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf2,
    0x9, 0xf1, 0x0, 0x30, 0x0, 0x1, 0x0, 0x1f, 0x80, 0xdb, 0x0, 0x5f, 0x60, 0x2e, 0xfd, 0x20, 0xbd, 0xf, 0x80, 0x2,
    0xa2, 0x2, 0x72, 0x71, 0x9, 0xf0, 0xf8, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x8f, 0xd, 0xb0, 0x0, 0x0, 0xad, 0x10, 0x0,
    0xa, 0xd0, 0x9f, 0x10, 0x0, 0x9, 0xe1, 0x8, 0xd3, 0x67, 0x2, 0xf8, 0x0, 0x1, 0xcb, 0x0, 0xff, 0xb6, 0x20, 0x8, 0xf5,
    0x0, 0x7, 0xf1, 0xc, 0xff, 0xfe, 0x0, 0xb, 0xf8, 0x11, 0x72, 0x0, 0x8f, 0xff, 0xc0, 0x0, 0x8, 0xff, 0xb8, 0x8b,
    0xb3, 0xea, 0x50, 0x0, 0x0, 0x2, 0x9d, 0xff, 0xd9, 0x0, 0x0, 0x0,

    /* U+F59B "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x2f, 0x91, 0xc3, 0x0, 0x0, 0x2a, 0x59, 0xf2, 0x8f,
    0x10, 0x7f, 0xa0, 0xa, 0xfa, 0x1, 0xf8, 0xcb, 0x0, 0x7f, 0xa0, 0xa, 0xfa, 0x0, 0xbc, 0xe9, 0x1, 0xc3, 0x0, 0x0,
    0x2a, 0x50, 0x9e, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xcb, 0x0, 0xcf, 0xff, 0xff, 0xfe, 0x0, 0xbc, 0x8f,
    0x10, 0x9f, 0xff, 0xff, 0xfb, 0x1, 0xf8, 0x2f, 0x90, 0xc, 0xff, 0xff, 0xd1, 0x9, 0xf2, 0x8, 0xf6, 0x0, 0x6a, 0xa7,
    0x0, 0x6f, 0x80, 0x0, 0xbf, 0x91, 0x0, 0x0, 0x19, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0,

    /* U+F5A4 "" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb9, 0x9b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf2, 0x8f, 0x10,
    0x3, 0x0, 0x0, 0x30, 0x1, 0xf8, 0xcb, 0x0, 0x5f, 0x60, 0x5, 0xf6, 0x0, 0xbc, 0xe9, 0x0, 0x2a, 0x20, 0x2, 0xa2, 0x0,
    0x9e, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xcb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbc, 0x8f, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xf8, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf2, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x0, 0xbf,
    0x91, 0x0, 0x0, 0x19, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb8, 0x9b, 0xff, 0x80, 0x0, 0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0,
    0x0,

    /* U+F5B3 "" */
    0x0, 0x0, 0x28, 0xce, 0xec, 0x82, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb8, 0x8b, 0xff, 0x80, 0x0, 0x0, 0xbf, 0x91, 0x0, 0x0,
    0x19, 0xfb, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf2, 0x9f, 0x10,
    0x44, 0x0, 0x0, 0x44, 0x1, 0xf9, 0xdb, 0xb, 0xff, 0xa0, 0xb, 0xff, 0xa0, 0xbd, 0xf9, 0x3, 0x11, 0x30, 0x3, 0x11,
    0x30, 0x9f, 0xf8, 0x2, 0x60, 0x3, 0x10, 0x6, 0x20, 0x8f, 0xda, 0x7, 0xf0, 0x7f, 0xe1, 0xf, 0x70, 0xad, 0x9e, 0x7,
    0xf0, 0xbf, 0xf4, 0xf, 0x70, 0xe9, 0x2f, 0x77, 0xf0, 0xaf, 0xf3, 0xf, 0x77, 0xf2, 0x8, 0xfb, 0xf0, 0x2a, 0x70, 0xf,
    0xbf, 0x80, 0x0, 0xbf, 0xf0, 0x0, 0x0, 0xf, 0xfb, 0x0, 0x0, 0x8, 0xff, 0xb8, 0x8a, 0xff, 0x80, 0x0, 0x0, 0x0, 0x28,
    0xce, 0xec, 0x82, 0x0, 0x0,

    /* U+F68C "" */
    0xc6, 0xe9, 0xe9, 0xc6,

    /* U+F68D "" */
    0x0, 0x0, 0xc6, 0x0, 0x0, 0xe9, 0x0, 0x0, 0xe9, 0xc6, 0x0, 0xe9, 0xe9, 0x0, 0xe9, 0xe9, 0x0, 0xe9, 0xc6, 0x0, 0xc6,

    /* U+F68E "" */
    0x0, 0x0, 0x0, 0x0, 0xc6, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0xc6, 0x0, 0xe9, 0x0, 0x0,
    0xe9, 0x0, 0xe9, 0x0, 0x0, 0xe9, 0x0, 0xe9, 0xc6, 0x0, 0xe9, 0x0, 0xe9, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0xe9, 0x0, 0xe9,
    0x0, 0xe9, 0xc6, 0x0, 0xc6, 0x0, 0xc6,

    /* U+F68F "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0x0, 0x0, 0xc6, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0x0,
    0xc6, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0xc6, 0x0,
    0xe9, 0x0, 0xe9, 0x0, 0xe9, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0xc6, 0x0,
    0xc6, 0x0, 0xc6, 0x0, 0xc6,

    /* U+F695 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc6, 0x0,
    0xa, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x6f, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0x3, 0xef, 0x50, 0x0, 0x0, 0x0, 0xc6, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x1c, 0xf8, 0x0, 0x0, 0x0, 0xe9, 0x0, 0xe9,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb1, 0x0, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfe, 0xd6, 0x0, 0xe9, 0x0,
    0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xfa, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0xe9,
    0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xc6, 0x0, 0x7, 0xfd, 0xf9, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x50, 0x4e,
    0xf9, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0xe7, 0x2, 0xcf, 0x80, 0xe9, 0x0, 0x0, 0xc6, 0x0, 0xe9, 0x0, 0xe9,
    0x0, 0x9, 0xfb, 0xf9, 0x0, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0xe9, 0x0, 0x30, 0x6f, 0xf9, 0x0, 0x0, 0xe9, 0x0, 0xe9, 0x0,
    0xe9, 0x0, 0xe5, 0x3, 0xef, 0x50, 0x0, 0xc6, 0x0, 0xc6, 0x0, 0xc6, 0x0, 0xc6, 0x0, 0x1b, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F6A8 "" */
    0x0, 0x0, 0x0, 0x1, 0xbc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xee, 0xbf,
    0x0, 0x3, 0xb1, 0x0, 0x0, 0x4, 0xfd, 0x27, 0xf0, 0x0, 0x2f, 0xc0, 0x6f, 0xff, 0xfc, 0x10, 0x7f, 0x0, 0x60, 0x4f,
    0x6f, 0xc7, 0x77, 0x0, 0x7, 0xf0, 0x3f, 0x80, 0xcc, 0xf7, 0x0, 0x0, 0x0, 0x7f, 0x0, 0xae, 0x8, 0xff, 0x70, 0x0, 0x0,
    0x7, 0xf0, 0xa, 0xe0, 0x9f, 0xfc, 0x77, 0x70, 0x0, 0x7f, 0x3, 0xf8, 0xc, 0xc6, 0xff, 0xff, 0xc1, 0x7, 0xf0, 0x6,
    0x4, 0xf6, 0x0, 0x0, 0x4f, 0xd2, 0x7f, 0x0, 0x2, 0xfc, 0x0, 0x0, 0x0, 0x3e, 0xeb, 0xf0, 0x0, 0x3b, 0x10, 0x0, 0x0,
    0x0, 0x2d, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xc0, 0x0, 0x0, 0x0,

    /* U+F6A9 "" */
    0x0, 0x0, 0x0, 0x1, 0xbc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xee, 0xbf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfd, 0x27, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xfc, 0x10, 0x7f,
    0x0, 0xc8, 0x0, 0x8c, 0xf, 0xc7, 0x77, 0x0, 0x7, 0xf0, 0x8, 0xf8, 0x8f, 0x80, 0xf7, 0x0, 0x0, 0x0, 0x7f, 0x0, 0x8,
    0xff, 0x80, 0xf, 0x70, 0x0, 0x0, 0x7, 0xf0, 0x0, 0x8f, 0xf8, 0x0, 0xfc, 0x77, 0x70, 0x0, 0x7f, 0x0, 0x8f, 0x88,
    0xf8, 0x6, 0xff, 0xff, 0xc1, 0x7, 0xf0, 0xc, 0x80, 0x8, 0xc0, 0x0, 0x0, 0x4f, 0xd2, 0x7f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xeb, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xc0, 0x0, 0x0, 0x0, 0x0,

    /* U+F6AA "" */
    0x5, 0x50, 0x8f, 0xf7, 0xaf, 0xfa, 0x3d, 0xd2, 0x0, 0x0,

    /* U+F6AB "" */
    0x0, 0x0, 0x15, 0x77, 0x51, 0x0, 0x0, 0x0, 0x4c, 0xff, 0xff, 0xff, 0xc4, 0x0, 0xa, 0xfd, 0x72, 0x0, 0x27, 0xdf,
    0xa0, 0x6f, 0x70, 0x0, 0x0, 0x0, 0x7, 0xf6, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x55, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xdd, 0x30, 0x0, 0x0,

    /* U+F6AC "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfc, 0x10, 0x48, 0xce, 0xff, 0xdc, 0x84, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xee, 0xff, 0xca, 0x88, 0xac, 0xff, 0xe6, 0x0,
    0x0, 0x0, 0x2, 0xdf, 0xb0, 0x0, 0x0, 0x0, 0x5, 0xcf, 0xe3, 0x0, 0x7, 0xc1, 0xa, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x3, 0xdf,
    0x70, 0xd, 0x90, 0x0, 0x7f, 0xd2, 0x0, 0x0, 0x0, 0x0, 0xa, 0xc0, 0x0, 0x0, 0x0, 0x3, 0xef, 0x87, 0x51, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x40, 0x1c, 0xff, 0xff, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfa, 0x0, 0x9f, 0xe8, 0xdf,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x70, 0x0, 0x5, 0xfe, 0x37, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x3d,
    0xf6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x55, 0x1, 0xbf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x70,
    0x8, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xa0, 0x0, 0x4e, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xdd,
    0x20, 0x0, 0x2, 0xdf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F7C2 "" */
    0x0, 0x7, 0xef, 0xff, 0xff, 0xe4, 0x0, 0x8f, 0xb7, 0x77, 0x77, 0xde, 0x8, 0xf9, 0x20, 0x20, 0x20, 0x7f, 0x7f, 0x90,
    0xb4, 0xb4, 0xb4, 0x7f, 0xeb, 0x0, 0xb4, 0xb4, 0xb4, 0x7f, 0xf7, 0x0, 0x62, 0x62, 0x62, 0x7f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xed, 0x77, 0x77, 0x77, 0x77, 0xde, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xe4};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 112, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 240, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 368, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 496, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 624, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 752, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 880, .adv_w = 256, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1016, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1144, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1272, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1400, .adv_w = 256, .box_w = 17, .box_h = 17, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 1545, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1673, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1801, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1913, .adv_w = 224, .box_w = 16, .box_h = 11, .ofs_x = -1, .ofs_y = 1},
    {.bitmap_index = 2001, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2073, .adv_w = 256, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2209, .adv_w = 320, .box_w = 18, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2353, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2481, .adv_w = 288, .box_w = 20, .box_h = 17, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 2651, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2779, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2877, .adv_w = 320, .box_w = 20, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3037, .adv_w = 256, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3149, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3277, .adv_w = 160, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3331, .adv_w = 192, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3427, .adv_w = 160, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3487, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3559, .adv_w = 160, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3613, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3711, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3809, .adv_w = 192, .box_w = 14, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 3907, .adv_w = 192, .box_w = 14, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4005, .adv_w = 256, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4117, .adv_w = 256, .box_w = 17, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4236, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4364, .adv_w = 224, .box_w = 16, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4492, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4620, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4718, .adv_w = 320, .box_w = 22, .box_h = 15, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4883, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4995, .adv_w = 288, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5085, .adv_w = 288, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5175, .adv_w = 288, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5265, .adv_w = 288, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5355, .adv_w = 288, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5445, .adv_w = 192, .box_w = 11, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5544, .adv_w = 288, .box_w = 18, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5634, .adv_w = 320, .box_w = 22, .box_h = 18, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 5832, .adv_w = 192, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5928, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6056, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6184, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6312, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6440, .adv_w = 320, .box_w = 20, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6600, .adv_w = 256, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6736, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6864, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6992, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7120, .adv_w = 320, .box_w = 2, .box_h = 4, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7124, .adv_w = 320, .box_w = 6, .box_h = 7, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7145, .adv_w = 320, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7195, .adv_w = 320, .box_w = 14, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7286, .adv_w = 320, .box_w = 22, .box_h = 18, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 7484, .adv_w = 288, .box_w = 17, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 7603, .adv_w = 288, .box_w = 19, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7736, .adv_w = 320, .box_w = 4, .box_h = 5, .ofs_x = 8, .ofs_y = -2},
    {.bitmap_index = 7746, .adv_w = 320, .box_w = 14, .box_h = 9, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 7809, .adv_w = 320, .box_w = 22, .box_h = 18, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 8007, .adv_w = 192, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2}};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0,    0x1a1,  0x320,  0x322,  0x327,  0x32a,  0x339,  0x342,  0x347,  0x34d,  0x350,  0x359,
    0x3be,  0xfb6,  0xfbc,  0xfc1,  0xfc2,  0xfc6,  0xfc7,  0xfc8,  0xfca,  0xfce,  0xfdc,  0xfdd,
    0xff3,  0xff9,  0xffd,  0x1000, 0x1001, 0x1002, 0x1006, 0x1015, 0x1016, 0x1017, 0x1018, 0x1026,
    0x102a, 0x1061, 0x10a8, 0x10cd, 0x10d9, 0x11a0, 0x11ad, 0x11f5, 0x11f6, 0x11f7, 0x11f8, 0x11f9,
    0x1248, 0x132b, 0x132c, 0x137a, 0x148f, 0x150b, 0x152e, 0x1539, 0x153d, 0x154d, 0x1550, 0x1559,
    0x1568, 0x1641, 0x1642, 0x1643, 0x1644, 0x164a, 0x165d, 0x165e, 0x165f, 0x1660, 0x1661, 0x1777};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {{.range_start = 57419,
                                                .range_length = 6008,
                                                .glyph_id_start = 1,
                                                .unicode_list = unicode_list_0,
                                                .glyph_id_ofs_list = NULL,
                                                .list_length = 72,
                                                .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY}};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_awesome_16_4 = {
#else
lv_font_t font_awesome_16_4 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt, /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt, /*Function pointer to get glyph's bitmap*/
    .line_height = 18,                              /*The maximum line height required by the font*/
    .base_line = 3,                                 /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc, /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};

#endif /*#if FONT_AWESOME_16_4*/
