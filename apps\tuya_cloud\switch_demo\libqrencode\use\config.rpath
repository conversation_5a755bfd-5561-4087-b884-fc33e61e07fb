#! /bin/sh
# Output a system dependent set of variables, describing how to set the
# run time search path of shared libraries in an executable.
#
#   Copyright 1996-2016 Free Software Foundation, Inc.
#   Taken from GNU libtool, 2001
#   Originally by <PERSON> <<EMAIL>>, 1996
#
#   This file is free software; the Free Software Foundation gives
#   unlimited permission to copy and/or distribute it, with or without
#   modifications, as long as this notice is preserved.
#
# The first argument passed to this file is the canonical host specification,
#    CPU_TYPE-MANUFACTURER-OPERATING_SYSTEM
# or
#    CPU_TYPE-MANUFACTURER-KERNEL-OPERATING_SYSTEM
# The environment variables CC, GCC, LDFLAGS, LD, with_gnu_ld
# should be set by the caller.
#
# The set of defined variables is at the end of this script.

# Known limitations:
# - On IRIX 6.5 with CC="cc", the run time search patch must not be longer
#   than 256 bytes, otherwise the compiler driver will dump core. The only
#   known workaround is to choose shorter directory names for the build
#   directory and/or the installation directory.

# All known linkers require a '.a' archive for static linking (except MSVC,
# which needs '.lib').
libext=a
shrext=.so

host="$1"
host_cpu=`echo "$host" | sed 's/^\([^-]*\)-\([^-]*\)-\(.*\)$/\1/'`
host_vendor=`echo "$host" | sed 's/^\([^-]*\)-\([^-]*\)-\(.*\)$/\2/'`
host_os=`echo "$host" | sed 's/^\([^-]*\)-\([^-]*\)-\(.*\)$/\3/'`

# Code taken from libtool.m4's _LT_CC_BASENAME.

for cc_temp in $CC""; do
  case $cc_temp in
    compile | *[\\/]compile | ccache | *[\\/]ccache ) ;;
    distcc | *[\\/]distcc | purify | *[\\/]purify ) ;;
    \-*) ;;
    *) break;;
  esac
done
cc_basename=`echo "$cc_temp" | sed -e 's%^.*/%%'`

# Code taken from libtool.m4's _LT_COMPILER_PIC.

wl=
if test "$GCC" = yes; then
  wl='-Wl,'
else
  case "$host_os" in
    aix*)
      wl='-Wl,'
      ;;
    mingw* | cygwin* | pw32* | os2* | cegcc*)
      ;;
    hpux9* | hpux10* | hpux11*)
      wl='-Wl,'
      ;;
    irix5* | irix6* | nonstopux*)
      wl='-Wl,'
      ;;
    linux* | k*bsd*-gnu | kopensolaris*-gnu)
      case $cc_basename in
        ecc*)
          wl='-Wl,'
          ;;
        icc* | ifort*)
          wl='-Wl,'
          ;;
        lf95*)
          wl='-Wl,'
          ;;
        nagfor*)
          wl='-Wl,-Wl,,'
          ;;
        pgcc* | pgf77* | pgf90* | pgf95* | pgfortran*)
          wl='-Wl,'
          ;;
        ccc*)
          wl='-Wl,'
          ;;
        xl* | bgxl* | bgf* | mpixl*)
          wl='-Wl,'
          ;;
        como)
          wl='-lopt='
          ;;
        *)
          case `$CC -V 2>&1 | sed 5q` in
            *Sun\ F* | *Sun*Fortran*)
              wl=
              ;;
            *Sun\ C*)
              wl='-Wl,'
              ;;
          esac
          ;;
      esac
      ;;
    newsos6)
      ;;
    *nto* | *qnx*)
      ;;
    osf3* | osf4* | osf5*)
      wl='-Wl,'
      ;;
    rdos*)
      ;;
    solaris*)
      case $cc_basename in
        f77* | f90* | f95* | sunf77* | sunf90* | sunf95*)
          wl='-Qoption ld '
          ;;
        *)
          wl='-Wl,'
          ;;
      esac
      ;;
    sunos4*)
      wl='-Qoption ld '
      ;;
    sysv4 | sysv4.2uw2* | sysv4.3*)
      wl='-Wl,'
      ;;
    sysv4*MP*)
      ;;
    sysv5* | unixware* | sco3.2v5* | sco5v6* | OpenUNIX*)
      wl='-Wl,'
      ;;
    unicos*)
      wl='-Wl,'
      ;;
    uts4*)
      ;;
  esac
fi

# Code taken from libtool.m4's _LT_LINKER_SHLIBS.

hardcode_libdir_flag_spec=
hardcode_libdir_separator=
hardcode_direct=no
hardcode_minus_L=no

case "$host_os" in
  cygwin* | mingw* | pw32* | cegcc*)
    # FIXME: the MSVC++ port hasn't been tested in a loooong time
    # When not using gcc, we currently assume that we are using
    # Microsoft Visual C++.
    if test "$GCC" != yes; then
      with_gnu_ld=no
    fi
    ;;
  interix*)
    # we just hope/assume this is gcc and not c89 (= MSVC++)
    with_gnu_ld=yes
    ;;
  openbsd*)
    with_gnu_ld=no
    ;;
esac

ld_shlibs=yes
if test "$with_gnu_ld" = yes; then
  # Set some defaults for GNU ld with shared library support. These
  # are reset later if shared libraries are not supported. Putting them
  # here allows them to be overridden if necessary.
  # Unlike libtool, we use -rpath here, not --rpath, since the documented
  # option of GNU ld is called -rpath, not --rpath.
  hardcode_libdir_flag_spec='${wl}-rpath ${wl}$libdir'
  case "$host_os" in
    aix[3-9]*)
      # On AIX/PPC, the GNU linker is very broken
      if test "$host_cpu" != ia64; then
        ld_shlibs=no
      fi
      ;;
    amigaos*)
      case "$host_cpu" in
        powerpc)
          ;;
        m68k)
          hardcode_libdir_flag_spec='-L$libdir'
          hardcode_minus_L=yes
          ;;
      esac
      ;;
    beos*)
      if $LD --help 2>&1 | grep ': supported targets:.* elf' > /dev/null; then
        :
      else
        ld_shlibs=no
      fi
      ;;
    cygwin* | mingw* | pw32* | cegcc*)
      # hardcode_libdir_flag_spec is actually meaningless, as there is
      # no search path for DLLs.
      hardcode_libdir_flag_spec='-L$libdir'
      if $LD --help 2>&1 | grep 'auto-import' > /dev/null; then
        :
      else
        ld_shlibs=no
      fi
      ;;
    haiku*)
      ;;
    interix[3-9]*)
      hardcode_direct=no
      hardcode_libdir_flag_spec='${wl}-rpath,$libdir'
      ;;
    gnu* | linux* | tpf* | k*bsd*-gnu | kopensolaris*-gnu)
      if $LD --help 2>&1 | grep ': supported targets:.* elf' > /dev/null; then
        :
      else
        ld_shlibs=no
      fi
      ;;
    netbsd*)
      ;;
    solaris*)
      if $LD -v 2>&1 | grep 'BFD 2\.8' > /dev/null; then
        ld_shlibs=no
      elif $LD --help 2>&1 | grep ': supported targets:.* elf' > /dev/null; then
        :
      else
        ld_shlibs=no
      fi
      ;;
    sysv5* | sco3.2v5* | sco5v6* | unixware* | OpenUNIX*)
      case `$LD -v 2>&1` in
        *\ [01].* | *\ 2.[0-9].* | *\ 2.1[0-5].*)
          ld_shlibs=no
          ;;
        *)
          if $LD --help 2>&1 | grep ': supported targets:.* elf' > /dev/null; then
            hardcode_libdir_flag_spec='`test -z "$SCOABSPATH" && echo ${wl}-rpath,$libdir`'
          else
            ld_shlibs=no
          fi
          ;;
      esac
      ;;
    sunos4*)
      hardcode_direct=yes
      ;;
    *)
      if $LD --help 2>&1 | grep ': supported targets:.* elf' > /dev/null; then
        :
      else
        ld_shlibs=no
      fi
      ;;
  esac
  if test "$ld_shlibs" = no; then
    hardcode_libdir_flag_spec=
  fi
else
  case "$host_os" in
    aix3*)
      # Note: this linker hardcodes the directories in LIBPATH if there
      # are no directories specified by -L.
      hardcode_minus_L=yes
      if test "$GCC" = yes; then
        # Neither direct hardcoding nor static linking is supported with a
        # broken collect2.
        hardcode_direct=unsupported
      fi
      ;;
    aix[4-9]*)
      if test "$host_cpu" = ia64; then
        # On IA64, the linker does run time linking by default, so we don't
        # have to do anything special.
        aix_use_runtimelinking=no
      else
        aix_use_runtimelinking=no
        # Test if we are trying to use run time linking or normal
        # AIX style linking. If -brtl is somewhere in LDFLAGS, we
        # need to do runtime linking.
        case $host_os in aix4.[23]|aix4.[23].*|aix[5-9]*)
          for ld_flag in $LDFLAGS; do
            if (test $ld_flag = "-brtl" || test $ld_flag = "-Wl,-brtl"); then
              aix_use_runtimelinking=yes
              break
            fi
          done
          ;;
        esac
      fi
      hardcode_direct=yes
      hardcode_libdir_separator=':'
      if test "$GCC" = yes; then
        case $host_os in aix4.[012]|aix4.[012].*)
          collect2name=`${CC} -print-prog-name=collect2`
          if test -f "$collect2name" && \
            strings "$collect2name" | grep resolve_lib_name >/dev/null
          then
            # We have reworked collect2
            :
          else
            # We have old collect2
            hardcode_direct=unsupported
            hardcode_minus_L=yes
            hardcode_libdir_flag_spec='-L$libdir'
            hardcode_libdir_separator=
          fi
          ;;
        esac
      fi
      # Begin _LT_AC_SYS_LIBPATH_AIX.
      echo 'int main () { return 0; }' > conftest.c
      ${CC} ${LDFLAGS} conftest.c -o conftest
      aix_libpath=`dump -H conftest 2>/dev/null | sed -n -e '/Import File Strings/,/^$/ { /^0/ { s/^0  *\(.*\)$/\1/; p; }
}'`
      if test -z "$aix_libpath"; then
        aix_libpath=`dump -HX64 conftest 2>/dev/null | sed -n -e '/Import File Strings/,/^$/ { /^0/ { s/^0  *\(.*\)$/\1/; p; }
}'`
      fi
      if test -z "$aix_libpath"; then
        aix_libpath="/usr/lib:/lib"
      fi
      rm -f conftest.c conftest
      # End _LT_AC_SYS_LIBPATH_AIX.
      if test "$aix_use_runtimelinking" = yes; then
        hardcode_libdir_flag_spec='${wl}-blibpath:$libdir:'"$aix_libpath"
      else
        if test "$host_cpu" = ia64; then
          hardcode_libdir_flag_spec='${wl}-R $libdir:/usr/lib:/lib'
        else
          hardcode_libdir_flag_spec='${wl}-blibpath:$libdir:'"$aix_libpath"
        fi
      fi
      ;;
    amigaos*)
      case "$host_cpu" in
        powerpc)
          ;;
        m68k)
          hardcode_libdir_flag_spec='-L$libdir'
          hardcode_minus_L=yes
          ;;
      esac
      ;;
    bsdi[45]*)
      ;;
    cygwin* | mingw* | pw32* | cegcc*)
      # When not using gcc, we currently assume that we are using
      # Microsoft Visual C++.
      # hardcode_libdir_flag_spec is actually meaningless, as there is
      # no search path for DLLs.
      hardcode_libdir_flag_spec=' '
      libext=lib
      ;;
    darwin* | rhapsody*)
      hardcode_direct=no
      if { case $cc_basename in ifort*) true;; *) test "$GCC" = yes;; esac; }; then
        :
      else
        ld_shlibs=no
      fi
      ;;
    dgux*)
      hardcode_libdir_flag_spec='-L$libdir'
      ;;
    freebsd2.[01]*)
      hardcode_direct=yes
      hardcode_minus_L=yes
      ;;
    freebsd* | dragonfly*)
      hardcode_libdir_flag_spec='-R$libdir'
      hardcode_direct=yes
      ;;
    hpux9*)
      hardcode_libdir_flag_spec='${wl}+b ${wl}$libdir'
      hardcode_libdir_separator=:
      hardcode_direct=yes
      # hardcode_minus_L: Not really in the search PATH,
      # but as the default location of the library.
      hardcode_minus_L=yes
      ;;
    hpux10*)
      if test "$with_gnu_ld" = no; then
        hardcode_libdir_flag_spec='${wl}+b ${wl}$libdir'
        hardcode_libdir_separator=:
        hardcode_direct=yes
        # hardcode_minus_L: Not really in the search PATH,
        # but as the default location of the library.
        hardcode_minus_L=yes
      fi
      ;;
    hpux11*)
      if test "$with_gnu_ld" = no; then
        hardcode_libdir_flag_spec='${wl}+b ${wl}$libdir'
        hardcode_libdir_separator=:
        case $host_cpu in
          hppa*64*|ia64*)
            hardcode_direct=no
            ;;
          *)
            hardcode_direct=yes
            # hardcode_minus_L: Not really in the search PATH,
            # but as the default location of the library.
            hardcode_minus_L=yes
            ;;
        esac
      fi
      ;;
    irix5* | irix6* | nonstopux*)
      hardcode_libdir_flag_spec='${wl}-rpath ${wl}$libdir'
      hardcode_libdir_separator=:
      ;;
    netbsd*)
      hardcode_libdir_flag_spec='-R$libdir'
      hardcode_direct=yes
      ;;
    newsos6)
      hardcode_direct=yes
      hardcode_libdir_flag_spec='${wl}-rpath ${wl}$libdir'
      hardcode_libdir_separator=:
      ;;
    *nto* | *qnx*)
      ;;
    openbsd*)
      if test -f /usr/libexec/ld.so; then
        hardcode_direct=yes
        if test -z "`echo __ELF__ | $CC -E - | grep __ELF__`" || test "$host_os-$host_cpu" = "openbsd2.8-powerpc"; then
          hardcode_libdir_flag_spec='${wl}-rpath,$libdir'
        else
          case "$host_os" in
            openbsd[01].* | openbsd2.[0-7] | openbsd2.[0-7].*)
              hardcode_libdir_flag_spec='-R$libdir'
              ;;
            *)
              hardcode_libdir_flag_spec='${wl}-rpath,$libdir'
              ;;
          esac
        fi
      else
        ld_shlibs=no
      fi
      ;;
    os2*)
      hardcode_libdir_flag_spec='-L$libdir'
      hardcode_minus_L=yes
      ;;
    osf3*)
      hardcode_libdir_flag_spec='${wl}-rpath ${wl}$libdir'
      hardcode_libdir_separator=:
      ;;
    osf4* | osf5*)
      if test "$GCC" = yes; then
        hardcode_libdir_flag_spec='${wl}-rpath ${wl}$libdir'
      else
        # Both cc and cxx compiler support -rpath directly
        hardcode_libdir_flag_spec='-rpath $libdir'
      fi
      hardcode_libdir_separator=:
      ;;
    solaris*)
      hardcode_libdir_flag_spec='-R$libdir'
      ;;
    sunos4*)
      hardcode_libdir_flag_spec='-L$libdir'
      hardcode_direct=yes
      hardcode_minus_L=yes
      ;;
    sysv4)
      case $host_vendor in
        sni)
          hardcode_direct=yes # is this really true???
          ;;
        siemens)
          hardcode_direct=no
          ;;
        motorola)
          hardcode_direct=no #Motorola manual says yes, but my tests say they lie
          ;;
      esac
      ;;
    sysv4.3*)
      ;;
    sysv4*MP*)
      if test -d /usr/nec; then
        ld_shlibs=yes
      fi
      ;;
    sysv4*uw2* | sysv5OpenUNIX* | sysv5UnixWare7.[01].[10]* | unixware7* | sco3.2v5.0.[024]*)
      ;;
    sysv5* | sco3.2v5* | sco5v6*)
      hardcode_libdir_flag_spec='`test -z "$SCOABSPATH" && echo ${wl}-R,$libdir`'
      hardcode_libdir_separator=':'
      ;;
    uts4*)
      hardcode_libdir_flag_spec='-L$libdir'
      ;;
    *)
      ld_shlibs=no
      ;;
  esac
fi

# Check dynamic linker characteristics
# Code taken from libtool.m4's _LT_SYS_DYNAMIC_LINKER.
# Unlike libtool.m4, here we don't care about _all_ names of the library, but
# only about the one the linker finds when passed -lNAME. This is the last
# element of library_names_spec in libtool.m4, or possibly two of them if the
# linker has special search rules.
library_names_spec=      # the last element of library_names_spec in libtool.m4
libname_spec='lib$name'
case "$host_os" in
  aix3*)
    library_names_spec='$libname.a'
    ;;
  aix[4-9]*)
    library_names_spec='$libname$shrext'
    ;;
  amigaos*)
    case "$host_cpu" in
      powerpc*)
        library_names_spec='$libname$shrext' ;;
      m68k)
        library_names_spec='$libname.a' ;;
    esac
    ;;
  beos*)
    library_names_spec='$libname$shrext'
    ;;
  bsdi[45]*)
    library_names_spec='$libname$shrext'
    ;;
  cygwin* | mingw* | pw32* | cegcc*)
    shrext=.dll
    library_names_spec='$libname.dll.a $libname.lib'
    ;;
  darwin* | rhapsody*)
    shrext=.dylib
    library_names_spec='$libname$shrext'
    ;;
  dgux*)
    library_names_spec='$libname$shrext'
    ;;
  freebsd[23].*)
    library_names_spec='$libname$shrext$versuffix'
    ;;
  freebsd* | dragonfly*)
    library_names_spec='$libname$shrext'
    ;;
  gnu*)
    library_names_spec='$libname$shrext'
    ;;
  haiku*)
    library_names_spec='$libname$shrext'
    ;;
  hpux9* | hpux10* | hpux11*)
    case $host_cpu in
      ia64*)
        shrext=.so
        ;;
      hppa*64*)
        shrext=.sl
        ;;
      *)
        shrext=.sl
        ;;
    esac
    library_names_spec='$libname$shrext'
    ;;
  interix[3-9]*)
    library_names_spec='$libname$shrext'
    ;;
  irix5* | irix6* | nonstopux*)
    library_names_spec='$libname$shrext'
    case "$host_os" in
      irix5* | nonstopux*)
        libsuff= shlibsuff=
        ;;
      *)
        case $LD in
          *-32|*"-32 "|*-melf32bsmip|*"-melf32bsmip ") libsuff= shlibsuff= ;;
          *-n32|*"-n32 "|*-melf32bmipn32|*"-melf32bmipn32 ") libsuff=32 shlibsuff=N32 ;;
          *-64|*"-64 "|*-melf64bmip|*"-melf64bmip ") libsuff=64 shlibsuff=64 ;;
          *) libsuff= shlibsuff= ;;
        esac
        ;;
    esac
    ;;
  linux*oldld* | linux*aout* | linux*coff*)
    ;;
  linux* | k*bsd*-gnu | kopensolaris*-gnu)
    library_names_spec='$libname$shrext'
    ;;
  knetbsd*-gnu)
    library_names_spec='$libname$shrext'
    ;;
  netbsd*)
    library_names_spec='$libname$shrext'
    ;;
  newsos6)
    library_names_spec='$libname$shrext'
    ;;
  *nto* | *qnx*)
    library_names_spec='$libname$shrext'
    ;;
  openbsd*)
    library_names_spec='$libname$shrext$versuffix'
    ;;
  os2*)
    libname_spec='$name'
    shrext=.dll
    library_names_spec='$libname.a'
    ;;
  osf3* | osf4* | osf5*)
    library_names_spec='$libname$shrext'
    ;;
  rdos*)
    ;;
  solaris*)
    library_names_spec='$libname$shrext'
    ;;
  sunos4*)
    library_names_spec='$libname$shrext$versuffix'
    ;;
  sysv4 | sysv4.3*)
    library_names_spec='$libname$shrext'
    ;;
  sysv4*MP*)
    library_names_spec='$libname$shrext'
    ;;
  sysv5* | sco3.2v5* | sco5v6* | unixware* | OpenUNIX* | sysv4*uw2*)
    library_names_spec='$libname$shrext'
    ;;
  tpf*)
    library_names_spec='$libname$shrext'
    ;;
  uts4*)
    library_names_spec='$libname$shrext'
    ;;
esac

sed_quote_subst='s/\(["`$\\]\)/\\\1/g'
escaped_wl=`echo "X$wl" | sed -e 's/^X//' -e "$sed_quote_subst"`
shlibext=`echo "$shrext" | sed -e 's,^\.,,'`
escaped_libname_spec=`echo "X$libname_spec" | sed -e 's/^X//' -e "$sed_quote_subst"`
escaped_library_names_spec=`echo "X$library_names_spec" | sed -e 's/^X//' -e "$sed_quote_subst"`
escaped_hardcode_libdir_flag_spec=`echo "X$hardcode_libdir_flag_spec" | sed -e 's/^X//' -e "$sed_quote_subst"`

LC_ALL=C sed -e 's/^\([a-zA-Z0-9_]*\)=/acl_cv_\1=/' <<EOF

# How to pass a linker flag through the compiler.
wl="$escaped_wl"

# Static library suffix (normally "a").
libext="$libext"

# Shared library suffix (normally "so").
shlibext="$shlibext"

# Format of library name prefix.
libname_spec="$escaped_libname_spec"

# Library names that the linker finds when passed -lNAME.
library_names_spec="$escaped_library_names_spec"

# Flag to hardcode \$libdir into a binary during linking.
# This must work even if \$libdir does not exist.
hardcode_libdir_flag_spec="$escaped_hardcode_libdir_flag_spec"

# Whether we need a single -rpath flag with a separated argument.
hardcode_libdir_separator="$hardcode_libdir_separator"

# Set to yes if using DIR/libNAME.so during linking hardcodes DIR into the
# resulting binary.
hardcode_direct="$hardcode_direct"

# Set to yes if using the -LDIR flag during linking hardcodes DIR into the
# resulting binary.
hardcode_minus_L="$hardcode_minus_L"

EOF
