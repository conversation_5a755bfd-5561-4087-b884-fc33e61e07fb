/**
 * @file gpio_manager.h
 * @brief GPIO管理模块头文件
 * 
 * 提供LED控制系统、按键管理和GPIO状态更新功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#ifndef __GPIO_MANAGER_H__
#define __GPIO_MANAGER_H__

#include "tuya_cloud_types.h"
#include "tal_gpio.h"

#ifdef __cplusplus
extern "C" {
#endif

// ========== GPIO引脚定义 ==========

/**
 * @brief GPIO引脚定义 (利用开发板现有硬件资源)
 */
#define GPIO_LED_USER               TUYA_GPIO_NUM_1     /**< P01 - 开发板User LED */
#define GPIO_BUTTON_USER            TUYA_GPIO_NUM_12    /**< P12 - 开发板User按键 */
#define SERVO_PWM_PIN_1             TUYA_GPIO_NUM_6     /**< P06 - pwm0:0 */
#define SERVO_PWM_PIN_2             TUYA_GPIO_NUM_7     /**< P07 - pwm0:1 */
#define SERVO_PWM_PIN_3             TUYA_GPIO_NUM_8     /**< P08 - pwm0:2 */

// ========== 配置常量 ==========

#define GPIO_DEBOUNCE_TIME_MS       50      /**< 按键防抖时间(毫秒) */
#define GPIO_LONG_PRESS_TIME_MS     2000    /**< 长按检测时间(毫秒) */
#define GPIO_BLINK_SLOW_INTERVAL    1000    /**< 慢闪间隔(毫秒) */
#define GPIO_BLINK_FAST_INTERVAL    200     /**< 快闪间隔(毫秒) */

// ========== 枚举定义 ==========

/**
 * @brief LED状态枚举
 */
typedef enum {
    LED_STATE_OFF = 0,          /**< LED关闭 */
    LED_STATE_ON,               /**< LED常亮 */
    LED_STATE_SLOW_BLINK,       /**< LED慢闪(1秒间隔) */
    LED_STATE_FAST_BLINK        /**< LED快闪(0.2秒间隔) */
} led_state_e;

/**
 * @brief 按键状态枚举
 */
typedef enum {
    BUTTON_STATE_RELEASED = 0,  /**< 按键释放 */
    BUTTON_STATE_PRESSED,       /**< 按键按下 */
    BUTTON_STATE_LONG_PRESSED   /**< 按键长按 */
} button_state_e;

/**
 * @brief 按键事件枚举
 */
typedef enum {
    BUTTON_EVENT_NONE = 0,      /**< 无事件 */
    BUTTON_EVENT_CLICK,         /**< 单击事件 */
    BUTTON_EVENT_LONG_PRESS,    /**< 长按事件 */
    BUTTON_EVENT_RELEASE        /**< 释放事件 */
} button_event_e;

// ========== 回调函数类型定义 ==========

/**
 * @brief 按键事件回调函数类型
 * 
 * @param event 按键事件类型
 * @param duration 按键持续时间(毫秒)
 */
typedef void (*button_event_callback_t)(button_event_e event, uint32_t duration);

// ========== 结构体定义 ==========

/**
 * @brief GPIO状态信息结构体
 */
typedef struct {
    led_state_e led_state;              /**< 当前LED状态 */
    button_state_e button_state;        /**< 当前按键状态 */
    bool led_physical_state;            /**< LED物理状态(高/低电平) */
    uint32_t led_last_toggle_time;      /**< LED最后切换时间 */
    uint32_t button_last_change_time;   /**< 按键最后变化时间 */
    uint32_t button_press_start_time;   /**< 按键按下开始时间 */
    bool initialized;                   /**< 初始化状态 */
} gpio_status_t;

// ========== 函数声明 ==========

/**
 * @brief 初始化GPIO管理器
 * 
 * 初始化系统LED和用户按键，设置GPIO配置
 * 
 * @return OPERATE_RET 操作结果
 *         - OPRT_OK: 成功
 *         - 其他: 失败
 */
OPERATE_RET gpio_manager_init(void);

/**
 * @brief 清理GPIO管理器
 * 
 * 释放GPIO资源，重置所有状态
 */
void gpio_manager_cleanup(void);

/**
 * @brief 设置系统LED状态
 * 
 * @param state LED状态
 */
void gpio_manager_set_led_state(led_state_e state);

/**
 * @brief 获取当前LED状态
 * 
 * @return led_state_e 当前LED状态
 */
led_state_e gpio_manager_get_led_state(void);

/**
 * @brief 获取当前按键状态
 * 
 * @return button_state_e 当前按键状态
 */
button_state_e gpio_manager_get_button_state(void);

/**
 * @brief 注册按键事件回调函数
 * 
 * @param callback 回调函数指针
 */
void gpio_manager_register_button_callback(button_event_callback_t callback);

/**
 * @brief 更新GPIO管理器状态
 * 
 * 处理LED闪烁和按键检测，需要在主循环中定期调用
 */
void gpio_manager_update(void);

/**
 * @brief 获取GPIO状态信息
 * 
 * @param status 状态信息结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_get_status(gpio_status_t *status);

/**
 * @brief 强制设置LED物理状态
 * 
 * @param on true为点亮，false为熄灭
 */
void gpio_manager_force_led(bool on);

// ========== 测试函数声明 ==========

/**
 * @brief 运行GPIO管理器完整测试
 * 
 * 该函数会测试GPIO管理器的所有功能，包括：
 * - LED控制功能
 * - 按键检测功能
 * - 状态管理功能
 * - 回调机制测试
 */
void gpio_manager_run_tests(void);

#ifdef __cplusplus
}
#endif

#endif /* __GPIO_MANAGER_H__ */
