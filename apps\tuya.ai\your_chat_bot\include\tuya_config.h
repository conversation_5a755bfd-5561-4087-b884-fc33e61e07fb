
/**
 * @file tuya_config.h
 * @brief IoT specific configuration file
 */

#ifndef TUYA_CONFIG_H_
#define TUYA_CONFIG_H_

#include "tuya_cloud_types.h"

/**
 * @brief configure the product information
 *
 * TUYA_PRODUCT_ID: PID, create on the Tuya IoT platform
 * TUYA_DEVICE_UUID: UUID, the unique ID of the device, you can get 2 free from Tuya IoT platform
 * TUYA_DEVICE_AUTHKEY: AUTHKEY, the private key of the device, you can get 2 free from Tuya IoT platform
 *
 * detail please refer to: https://developer.tuya.com/cn/docs/iot-device-dev/application-creation?id=Kbxw7ket3aujc
 *
 * warning: please replace these production information with your product id and license, otherwise the demo cannot
 * work.
 *
 */

#define TUYA_PRODUCT_ID "j3wapzbxuck9uyss"
#endif

#define TUYA_OPENSDK_UUID    "uuid22f5d4ceda0bf17d"             // Please change the correct uuid
#define TUYA_OPENSDK_AUTHKEY "BZAy9KVLziJ1XgswmLayQERuvtmixrX2" // Please change the correct authkeychange th

/**
 * @brief PINCODE for AP provisioning
 *
 * TUYA_NETCFG_PINCODE: a random PINCODE for AP provisioning, PINCODE was generated BY TUYA PMS system!!!
 *
 * WARNING: PINCODE is mandatory for AP provisioning
 */
// #define TUYA_NETCFG_PINCODE   "69832860"




