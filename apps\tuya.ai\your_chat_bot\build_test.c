/**
 * @file build_test.c
 * @brief 编译测试文件 - 验证所有函数调用是否正确
 */

#include "tuya_cloud_types.h"
#include "tal_api.h"
#include "tkl_output.h"
#include "pwm_door_control.h"

// 测试函数调用
void test_function_calls(void)
{
    // 测试初始化函数
    OPERATE_RET ret = pwm_door_control_init();
    (void)ret;
    
    // 测试DP处理函数
    ret = pwm_door_control_handle_dp111(true);
    ret = pwm_door_control_handle_dp112(false);
    (void)ret;
    
    PR_INFO("✅ 所有函数调用测试通过");
}

int main(void)
{
    test_function_calls();
    return 0;
}
