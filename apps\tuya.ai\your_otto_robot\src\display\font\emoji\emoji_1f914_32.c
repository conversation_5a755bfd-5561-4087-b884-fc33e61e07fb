
#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
#include "lvgl.h"
#elif defined(LV_BUILD_TEST)
#include "../lvgl.h"
#else
#include "lvgl/lvgl.h"
#endif

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_EMOJI_1F914_32
#define LV_ATTRIBUTE_EMOJI_1F914_32
#endif

static const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_EMOJI_1F914_32 uint8_t
    emoji_1f914_32_map[] = {

        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0xaa, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0x66, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x2a, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xe0, 0xff, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0xc8, 0xed, 0x07, 0xcd, 0xa7, 0xc4,
        0xc7, 0xc4, 0x68, 0xdd, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x4a, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4a, 0xfd, 0x69, 0xfe, 0x49, 0xfe,
        0x29, 0xfe, 0x24, 0x8b, 0x43, 0x62, 0x23, 0x62, 0x23, 0x62, 0x23, 0x62, 0x23, 0x62, 0x83, 0x6a, 0xe7, 0xcc,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x69, 0xfe, 0x4a, 0xfd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x29, 0xfe, 0xa5, 0x9b, 0x46, 0xb4, 0x68, 0xdd,
        0xc8, 0xed, 0xc8, 0xed, 0x07, 0xd5, 0xe5, 0xa3, 0xe7, 0xcc, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x68, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x09, 0xf6, 0xc7, 0xc4, 0xa8, 0xe5, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x29, 0xfe,
        0x66, 0xbc, 0x65, 0x93, 0xe4, 0x7a, 0xc4, 0x7a, 0x44, 0x8b, 0x46, 0xb4, 0xa8, 0xe5, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0xe4, 0x82, 0x23, 0x62, 0x43, 0x6a, 0xa8, 0xe5,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x68, 0xdd, 0x43, 0x62, 0xc4, 0x7a, 0xe4, 0x82, 0x24, 0x8b,
        0x24, 0x83, 0x43, 0x62, 0x43, 0x62, 0x27, 0xd5, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x68, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0xa8, 0xe5, 0x23, 0x62, 0x23, 0x62, 0x23, 0x62, 0x46, 0xb4, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x09, 0xf6, 0xe4, 0x7a, 0x23, 0x62, 0x43, 0x62, 0x88, 0xe5, 0x09, 0xf6, 0xa7, 0xc4, 0xc8, 0xed,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x29, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0xc8, 0xed, 0x23, 0x62, 0x23, 0x62, 0x23, 0x62,
        0x66, 0xbc, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0xa8, 0xe5, 0x23, 0x62, 0x23, 0x62,
        0x23, 0x62, 0x46, 0xb4, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x65, 0x93, 0x23, 0x62, 0x83, 0x72, 0xe8, 0xed, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0xc8, 0xed, 0x23, 0x62, 0x23, 0x62, 0x23, 0x62, 0x66, 0xbc, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x68, 0xdd,
        0x09, 0xf6, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x85, 0x9b,
        0x23, 0x62, 0xa4, 0x72, 0xe9, 0xf5, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x00, 0x00, 0xaa, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0xa8, 0xe5, 0x29, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x66, 0xfe, 0x69, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x4a, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x29, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x69, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0xe8, 0xfd, 0x84, 0xf4,
        0xe5, 0xf4, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0xe8, 0xfd, 0x00, 0x00, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0xe8, 0xfd, 0x84, 0xf4, 0x84, 0xf4, 0x46, 0xf5, 0xc8, 0xed, 0x85, 0x9b,
        0x04, 0x83, 0x04, 0x83, 0x65, 0x8b, 0x46, 0xb4, 0xc8, 0xed, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x00, 0x00,
        0x00, 0x00, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x29, 0xfe,
        0x84, 0xf4, 0x84, 0xf4, 0xa4, 0xf4, 0x88, 0xe5, 0xe4, 0x7a, 0x65, 0x93, 0x85, 0x93, 0xe4, 0x7a, 0x43, 0x62,
        0x43, 0x62, 0xa5, 0x9b, 0x29, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x28, 0xfe, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x09, 0xf6, 0x46, 0xb4, 0x84, 0x72, 0x88, 0xe5, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x00, 0x00, 0x00, 0x00, 0x69, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x66, 0xfd, 0x84, 0xf4, 0x84, 0xf4, 0xa4, 0xf4, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x08, 0xfe, 0xa7, 0xfd, 0x46, 0xf5, 0x46, 0xf5, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0xa7, 0xfd, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x46, 0xf5,
        0x49, 0xfe, 0x49, 0xfe, 0xe8, 0xfd, 0x87, 0xfd, 0x25, 0xf5, 0xa4, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4,
        0x84, 0xf4, 0xe8, 0xfd, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0xa7, 0xfd,
        0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x26, 0xf5, 0xe5, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4,
        0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0xa4, 0xf4, 0x26, 0xf5, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x69, 0xfe, 0x49, 0xfe, 0x28, 0xfe, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4,
        0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0xa4, 0xf4, 0x25, 0xf5, 0x87, 0xfd, 0x08, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0xfe, 0x49, 0xfe, 0x46, 0xf5,
        0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4,
        0x84, 0xf4, 0xa4, 0xf4, 0x29, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0xaa, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4a, 0xfe, 0x05, 0xf5, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4,
        0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x08, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x4a, 0xfe,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4,
        0x84, 0xf4, 0x84, 0xf4, 0x05, 0xf5, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4,
        0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x29, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0xaa, 0xfe, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x64, 0xec, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4,
        0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x05, 0xf5, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x49, 0xfe, 0x49, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x84, 0xf4, 0x84, 0xf4,
        0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x66, 0xfd,
        0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4,
        0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x05, 0xf5, 0x49, 0xfe, 0xaa, 0xfe, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x84, 0xf4, 0x84, 0xf4, 0x83, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x84, 0xf4, 0x63, 0xf4,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x4c, 0x90, 0xc3, 0xe7, 0xf4, 0xf4, 0xe7, 0xc2, 0x90, 0x4a, 0x05,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x09, 0x77, 0xea, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xea, 0x76, 0x09, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x56, 0xe3, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x91, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x03, 0xae, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xae, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8f, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x5e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xec, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xeb, 0x0e, 0x00, 0x00,
        0x00, 0x00, 0x86, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x83, 0x00, 0x00, 0x00, 0x0f, 0xf5, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0d, 0x00, 0x00, 0x62, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0x5e, 0x00, 0x00, 0xa8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xa4, 0x00, 0x00, 0xde, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdc, 0x00, 0x06, 0xfd,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x05, 0x15, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x13, 0x17, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0x17, 0x0a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x08,
        0x00, 0xe8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5, 0x00, 0x00, 0xb7, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb5, 0x00, 0x00, 0x75, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0x71, 0x00, 0x00, 0x1e, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
        0x1d, 0x00, 0x00, 0x00, 0xa8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x00, 0x00, 0x00, 0x00,
        0x21, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8b, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0x89, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0xbd, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc2,
        0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdb, 0x13, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0xa3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc5, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x75, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xfd, 0x94, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xfa, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbb, 0x30, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x85, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xce, 0x88, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x98, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
        0x7b, 0x26, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x47, 0xb2, 0xeb, 0xf8, 0xd8, 0xa4, 0x6e, 0x29, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

};

const lv_image_dsc_t emoji_1f914_32 = {
    .header.magic = LV_IMAGE_HEADER_MAGIC,
    .header.cf = LV_COLOR_FORMAT_RGB565A8,
    .header.flags = 0,
    .header.w = 32,
    .header.h = 32,
    .header.stride = 64,
    .data_size = sizeof(emoji_1f914_32_map),
    .data = emoji_1f914_32_map,
};
