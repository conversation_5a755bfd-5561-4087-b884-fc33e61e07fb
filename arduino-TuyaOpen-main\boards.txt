menu.UploadSpeed=Upload Speed

# --------------------------------------------------
#                         T2                        
# --------------------------------------------------
t2.name=T2
t2.build.chip=t2
t2.build.board=T2
t2.build.core=tuya_open
t2.build.variant=t2

t2.menu.UploadSpeed.921600=921600
t2.menu.UploadSpeed.921600.upload.speed=921600
t2.menu.UploadSpeed.115200=115200
t2.menu.UploadSpeed.115200.upload.speed=115200
t2.menu.UploadSpeed.230400=230400
t2.menu.UploadSpeed.230400.upload.speed=230400
t2.menu.UploadSpeed.460800=460800
t2.menu.UploadSpeed.460800.upload.speed=460800
t2.menu.UploadSpeed.1500000=1500000
t2.menu.UploadSpeed.1500000.upload.speed=1500000
t2.menu.UploadSpeed.2000000=2000000
t2.menu.UploadSpeed.2000000.upload.speed=2000000

t2.compiler.path={runtime.tools.gcc-arm-none-eabi.path}
t2.compiler.cmd.S={compiler.path}/bin/arm-none-eabi-gcc
t2.compiler.cmd.c={compiler.path}/bin/arm-none-eabi-gcc
t2.compiler.cmd.cpp={compiler.path}/bin/arm-none-eabi-g++
t2.compiler.cmd.ar={compiler.path}/bin/arm-none-eabi-ar
t2.compiler.cmd.ld={compiler.path}/bin/arm-none-eabi-gcc

t2.vendor.path={runtime.tools.vendor-t2.path}
t2.compiler.flags.S=@{runtime.tools.vendor-t2.path}/flags/S_flags.txt
t2.compiler.flags.c=@{runtime.tools.vendor-t2.path}/flags/c_flags.txt
t2.compiler.flags.cpp=@{runtime.tools.vendor-t2.path}/flags/cpp_flags.txt
t2.compiler.flags.ar=@{runtime.tools.vendor-t2.path}/flags/ar_flags.txt

t2.appBuild.flags=@{build.path}/tuyaTmp/appConfig/appBuildDefine.txt

t2.compiler.includes.tuya_open=-iprefix {runtime.tools.vendor-t2.path}/ @{runtime.tools.vendor-t2.path}/flags/include_tuya_open.txt
t2.compiler.includes.tkl=-iprefix {runtime.tools.vendor-t2.path}/ @{runtime.tools.vendor-t2.path}/flags/include_tkl.txt
t2.compiler.includes.vendor=-iprefix {runtime.tools.vendor-t2.path}/ @{runtime.tools.vendor-t2.path}/flags/include_vendor.txt
t2.compiler.includes={compiler.includes.tuya_open} {compiler.includes.tkl} {compiler.includes.vendor}

t2.compiler.flags.ld=@{runtime.tools.vendor-t2.path}/flags/ld_flags.txt
t2.compiler.flags.libs=-L{runtime.tools.vendor-t2.path}/libs @{runtime.tools.vendor-t2.path}/flags/libs_flags.txt
t2.compiler.flags.ld_scripts=-Wl,--cref -Wl,-Map={build.path}/{build.project_name}.map -T{runtime.tools.vendor-t2.path}/packager-tools/t2.ld -o {build.path}/{build.project_name}.elf

t2.upload.tool=tyutool
t2.upload.tool.default=tyutool

t2.monitor_port.serial.baudrate=115200

# --------------------------------------------------
#                         T3                        
# --------------------------------------------------
t3.name=T3
t3.build.chip=t3
t3.build.board=T3
t3.build.core=tuya_open
t3.build.variant=t3

t3.menu.UploadSpeed.921600=921600
t3.menu.UploadSpeed.921600.upload.speed=921600
t3.menu.UploadSpeed.115200=115200
t3.menu.UploadSpeed.115200.upload.speed=115200
t3.menu.UploadSpeed.230400=230400
t3.menu.UploadSpeed.230400.upload.speed=230400
t3.menu.UploadSpeed.460800=460800
t3.menu.UploadSpeed.460800.upload.speed=460800
t3.menu.UploadSpeed.1500000=1500000
t3.menu.UploadSpeed.1500000.upload.speed=1500000
t3.menu.UploadSpeed.2000000=2000000
t3.menu.UploadSpeed.2000000.upload.speed=2000000

t3.compiler.path={runtime.tools.gcc-arm-none-eabi.path}
t3.compiler.cmd.S={compiler.path}/bin/arm-none-eabi-gcc
t3.compiler.cmd.c={compiler.path}/bin/arm-none-eabi-gcc
t3.compiler.cmd.cpp={compiler.path}/bin/arm-none-eabi-g++
t3.compiler.cmd.ar={compiler.path}/bin/arm-none-eabi-ar
t3.compiler.cmd.ld={compiler.path}/bin/arm-none-eabi-gcc

t3.vendor.path={runtime.tools.vendor-t3.path}
t3.compiler.flags.S=@{runtime.tools.vendor-t3.path}/flags/S_flags.txt
t3.compiler.flags.c=@{runtime.tools.vendor-t3.path}/flags/c_flags.txt
t3.compiler.flags.cpp=@{runtime.tools.vendor-t3.path}/flags/cpp_flags.txt
t3.compiler.flags.ar=@{runtime.tools.vendor-t3.path}/flags/ar_flags.txt

t3.appBuild.flags=@{build.path}/tuyaTmp/appConfig/appBuildDefine.txt

t3.compiler.includes.tuya_open=-iprefix {runtime.tools.vendor-t3.path}/ @{runtime.tools.vendor-t3.path}/flags/include_tuya_open.txt
t3.compiler.includes.tkl=-iprefix {runtime.tools.vendor-t3.path}/ @{runtime.tools.vendor-t3.path}/flags/include_tkl.txt
t3.compiler.includes.vendor=-iprefix {runtime.tools.vendor-t3.path}/ @{runtime.tools.vendor-t3.path}/flags/include_vendor.txt
t3.compiler.includes={compiler.includes.tuya_open} {compiler.includes.tkl} {compiler.includes.vendor}

t3.compiler.flags.ld=@{runtime.tools.vendor-t3.path}/flags/ld_flags.txt
t3.compiler.flags.libs=-L{runtime.tools.vendor-t3.path}/libs @{runtime.tools.vendor-t3.path}/flags/libs_flags.txt
t3.compiler.flags.ld_scripts=-Wl,--cref -Wl,-Map={build.path}/{build.project_name}.map -T{runtime.tools.vendor-t3.path}/packager-tools/t3.ld -o {build.path}/{build.project_name}.elf

t3.upload.tool=tyutool
t3.upload.tool.default=tyutool

t3.monitor_port.serial.baudrate=115200

# --------------------------------------------------
#                         T5                        
# --------------------------------------------------
t5.name=T5
t5.build.chip=t5
t5.build.board=T5
t5.build.core=tuya_open
t5.build.variant=t5

t5.menu.UploadSpeed.921600=921600
t5.menu.UploadSpeed.921600.upload.speed=921600
t5.menu.UploadSpeed.115200=115200
t5.menu.UploadSpeed.115200.upload.speed=115200
t5.menu.UploadSpeed.230400=230400
t5.menu.UploadSpeed.230400.upload.speed=230400
t5.menu.UploadSpeed.460800=460800
t5.menu.UploadSpeed.460800.upload.speed=460800
t5.menu.UploadSpeed.1500000=1500000
t5.menu.UploadSpeed.1500000.upload.speed=1500000
t5.menu.UploadSpeed.2000000=2000000
t5.menu.UploadSpeed.2000000.upload.speed=2000000

t5.compiler.path={runtime.tools.gcc-arm-none-eabi.path}
t5.compiler.cmd.S={compiler.path}/bin/arm-none-eabi-gcc
t5.compiler.cmd.c={compiler.path}/bin/arm-none-eabi-gcc
t5.compiler.cmd.cpp={compiler.path}/bin/arm-none-eabi-g++
t5.compiler.cmd.ar={compiler.path}/bin/arm-none-eabi-ar
t5.compiler.cmd.ld={compiler.path}/bin/arm-none-eabi-g++

t5.vendor.path={runtime.tools.vendor-t5.path}
t5.compiler.flags.S=@{runtime.tools.vendor-t5.path}/flags/S_flags.txt
t5.compiler.flags.c=@{runtime.tools.vendor-t5.path}/flags/c_flags.txt
t5.compiler.flags.cpp=@{runtime.tools.vendor-t5.path}/flags/cpp_flags.txt
t5.compiler.flags.ar=@{runtime.tools.vendor-t5.path}/flags/ar_flags.txt

t5.appBuild.flags=@{build.path}/tuyaTmp/appConfig/appBuildDefine.txt

t5.compiler.includes.tuya_open=-iprefix {runtime.tools.vendor-t5.path}/ @{runtime.tools.vendor-t5.path}/flags/include_tuya_open.txt
t5.compiler.includes.tkl=-iprefix {runtime.tools.vendor-t5.path}/ @{runtime.tools.vendor-t5.path}/flags/include_tkl.txt
t5.compiler.includes.vendor=-iprefix {runtime.tools.vendor-t5.path}/ @{runtime.tools.vendor-t5.path}/flags/include_vendor.txt
t5.compiler.includes={compiler.includes.tuya_open} {compiler.includes.tkl} {compiler.includes.vendor}

t5.compiler.flags.ld=@{runtime.tools.vendor-t5.path}/flags/ld_flags.txt
t5.compiler.flags.libs=-L{runtime.tools.vendor-t5.path}/libs @{runtime.tools.vendor-t5.path}/flags/libs_flags.txt
t5.compiler.flags.ld_scripts=-Wl,--cref -Wl,-Map={build.path}/{build.project_name}.map -T{runtime.tools.vendor-t5.path}/packager-tools/t5.ld -o {build.path}/{build.project_name}.elf

t5.upload.tool=tyutool
t5.upload.tool.default=tyutool

t5.monitor_port.serial.baudrate=115200

# --------------------------------------------------
#                      XH WB5E                      
# --------------------------------------------------
XH_WB5E.name=XH WB5E
XH_WB5E.build.chip=t5
XH_WB5E.build.board=XH_WB5E
XH_WB5E.build.core=tuya_open
XH_WB5E.build.variant=XH_WB5E

XH_WB5E.menu.UploadSpeed.921600=921600
XH_WB5E.menu.UploadSpeed.921600.upload.speed=921600
XH_WB5E.menu.UploadSpeed.115200=115200
XH_WB5E.menu.UploadSpeed.115200.upload.speed=115200
XH_WB5E.menu.UploadSpeed.230400=230400
XH_WB5E.menu.UploadSpeed.230400.upload.speed=230400
XH_WB5E.menu.UploadSpeed.460800=460800
XH_WB5E.menu.UploadSpeed.460800.upload.speed=460800
XH_WB5E.menu.UploadSpeed.1500000=1500000
XH_WB5E.menu.UploadSpeed.1500000.upload.speed=1500000
XH_WB5E.menu.UploadSpeed.2000000=2000000
XH_WB5E.menu.UploadSpeed.2000000.upload.speed=2000000

XH_WB5E.compiler.path={runtime.tools.gcc-arm-none-eabi.path}
XH_WB5E.compiler.cmd.S={compiler.path}/bin/arm-none-eabi-gcc
XH_WB5E.compiler.cmd.c={compiler.path}/bin/arm-none-eabi-gcc
XH_WB5E.compiler.cmd.cpp={compiler.path}/bin/arm-none-eabi-g++
XH_WB5E.compiler.cmd.ar={compiler.path}/bin/arm-none-eabi-ar
XH_WB5E.compiler.cmd.ld={compiler.path}/bin/arm-none-eabi-g++

XH_WB5E.vendor.path={runtime.tools.vendor-t5.path}
XH_WB5E.compiler.flags.S=@{runtime.tools.vendor-t5.path}/flags/S_flags.txt
XH_WB5E.compiler.flags.c=@{runtime.tools.vendor-t5.path}/flags/c_flags.txt
XH_WB5E.compiler.flags.cpp=@{runtime.tools.vendor-t5.path}/flags/cpp_flags.txt
XH_WB5E.compiler.flags.ar=@{runtime.tools.vendor-t5.path}/flags/ar_flags.txt

XH_WB5E.appBuild.flags=@{build.path}/tuyaTmp/appConfig/appBuildDefine.txt

XH_WB5E.compiler.includes.tuya_open=-iprefix {runtime.tools.vendor-t5.path}/ @{runtime.tools.vendor-t5.path}/flags/include_tuya_open.txt
XH_WB5E.compiler.includes.tkl=-iprefix {runtime.tools.vendor-t5.path}/ @{runtime.tools.vendor-t5.path}/flags/include_tkl.txt
XH_WB5E.compiler.includes.vendor=-iprefix {runtime.tools.vendor-t5.path}/ @{runtime.tools.vendor-t5.path}/flags/include_vendor.txt
XH_WB5E.compiler.includes={compiler.includes.tuya_open} {compiler.includes.tkl} {compiler.includes.vendor}

XH_WB5E.compiler.flags.ld=@{runtime.tools.vendor-t5.path}/flags/ld_flags.txt
XH_WB5E.compiler.flags.libs=-L{runtime.tools.vendor-t5.path}/libs @{runtime.tools.vendor-t5.path}/flags/libs_flags.txt
XH_WB5E.compiler.flags.ld_scripts=-Wl,--cref -Wl,-Map={build.path}/{build.project_name}.map -T{runtime.tools.vendor-t5.path}/packager-tools/t5.ld -o {build.path}/{build.project_name}.elf

XH_WB5E.upload.tool=tyutool
XH_WB5E.upload.tool.default=tyutool

XH_WB5E.monitor_port.serial.baudrate=115200

# --------------------------------------------------
#                       LN882H                      
# --------------------------------------------------
ln882h.name=LN882H
ln882h.build.chip=ln882h
ln882h.build.board=LN882H
ln882h.build.core=tuya_open
ln882h.build.variant=ln882h

ln882h.menu.UploadSpeed.921600=921600
ln882h.menu.UploadSpeed.921600.upload.speed=921600
ln882h.menu.UploadSpeed.115200=115200
ln882h.menu.UploadSpeed.115200.upload.speed=115200
ln882h.menu.UploadSpeed.230400=230400
ln882h.menu.UploadSpeed.230400.upload.speed=230400
ln882h.menu.UploadSpeed.460800=460800
ln882h.menu.UploadSpeed.460800.upload.speed=460800
ln882h.menu.UploadSpeed.1500000=1500000
ln882h.menu.UploadSpeed.1500000.upload.speed=1500000
ln882h.menu.UploadSpeed.2000000=2000000
ln882h.menu.UploadSpeed.2000000.upload.speed=2000000

ln882h.compiler.path={runtime.tools.gcc-arm-none-eabi.path}
ln882h.compiler.cmd.S={compiler.path}/bin/arm-none-eabi-gcc
ln882h.compiler.cmd.c={compiler.path}/bin/arm-none-eabi-gcc
ln882h.compiler.cmd.cpp={compiler.path}/bin/arm-none-eabi-g++
ln882h.compiler.cmd.ar={compiler.path}/bin/arm-none-eabi-ar
ln882h.compiler.cmd.ld={compiler.path}/bin/arm-none-eabi-g++

ln882h.vendor.path={runtime.tools.vendor-ln882h.path}
ln882h.compiler.flags.S=@{runtime.tools.vendor-ln882h.path}/flags/S_flags.txt
ln882h.compiler.flags.c=@{runtime.tools.vendor-ln882h.path}/flags/c_flags.txt
ln882h.compiler.flags.cpp=@{runtime.tools.vendor-ln882h.path}/flags/cpp_flags.txt
ln882h.compiler.flags.ar=@{runtime.tools.vendor-ln882h.path}/flags/ar_flags.txt

ln882h.appBuild.flags=@{build.path}/tuyaTmp/appConfig/appBuildDefine.txt

ln882h.compiler.includes.tuya_open=-iprefix {runtime.tools.vendor-ln882h.path}/ @{runtime.tools.vendor-ln882h.path}/flags/include_tuya_open.txt
ln882h.compiler.includes.tkl=-iprefix {runtime.tools.vendor-ln882h.path}/ @{runtime.tools.vendor-ln882h.path}/flags/include_tkl.txt
ln882h.compiler.includes.vendor=-iprefix {runtime.tools.vendor-ln882h.path}/ @{runtime.tools.vendor-ln882h.path}/flags/include_vendor.txt
ln882h.compiler.includes={compiler.includes.tuya_open} {compiler.includes.tkl} {compiler.includes.vendor}

ln882h.compiler.flags.ld=@{runtime.tools.vendor-ln882h.path}/flags/ld_flags.txt
ln882h.compiler.flags.libs=-L{runtime.tools.vendor-ln882h.path}/libs @{runtime.tools.vendor-ln882h.path}/flags/libs_flags.txt
ln882h.compiler.flags.ld_scripts=-Wl,--cref -Wl,-Map={build.path}/{build.project_name}.map -T{runtime.tools.vendor-ln882h.path}/packager-tools/ln882h.ld -o {build.path}/{build.project_name}.elf

ln882h.upload.tool=tyutool
ln882h.upload.tool.default=tyutool

ln882h.monitor_port.serial.baudrate=115200

# --------------------------------------------------
#                       ESP32                       
# --------------------------------------------------
esp32.name=ESP32
esp32.build.chip=esp32
esp32.build.board=ESP32
esp32.build.core=tuya_open
esp32.build.variant=esp32

esp32.menu.UploadSpeed.921600=921600
esp32.menu.UploadSpeed.921600.upload.speed=921600
esp32.menu.UploadSpeed.115200=115200
esp32.menu.UploadSpeed.115200.upload.speed=115200
esp32.menu.UploadSpeed.230400=230400
esp32.menu.UploadSpeed.230400.upload.speed=230400
esp32.menu.UploadSpeed.460800=460800
esp32.menu.UploadSpeed.460800.upload.speed=460800
esp32.menu.UploadSpeed.1500000=1500000
esp32.menu.UploadSpeed.1500000.upload.speed=1500000
esp32.menu.UploadSpeed.2000000=2000000
esp32.menu.UploadSpeed.2000000.upload.speed=2000000

esp32.compiler.path={runtime.tools.xtensa-esp-elf.path}
esp32.compiler.cmd.S={compiler.path}/bin/xtensa-esp32-elf-gcc
esp32.compiler.cmd.c={compiler.path}/bin/xtensa-esp32-elf-gcc
esp32.compiler.cmd.cpp={compiler.path}/bin/xtensa-esp32-elf-g++
esp32.compiler.cmd.ar={compiler.path}/bin/xtensa-esp32-elf-ar
esp32.compiler.cmd.ld={compiler.path}/bin/xtensa-esp32-elf-g++

esp32.vendor.path={runtime.tools.vendor-esp32.path}
esp32.compiler.flags.S=@{runtime.tools.vendor-esp32.path}/flags/S_flags.txt
esp32.compiler.flags.c=@{runtime.tools.vendor-esp32.path}/flags/c_flags.txt
esp32.compiler.flags.cpp=@{runtime.tools.vendor-esp32.path}/flags/cpp_flags.txt
esp32.compiler.flags.ar=@{runtime.tools.vendor-esp32.path}/flags/ar_flags.txt

esp32.appBuild.flags=@{build.path}/tuyaTmp/appConfig/appBuildDefine.txt

esp32.compiler.includes.tuya_open=-iprefix {runtime.tools.vendor-esp32.path}/ @{runtime.tools.vendor-esp32.path}/flags/include_tuya_open.txt
esp32.compiler.includes.tkl=-iprefix {runtime.tools.vendor-esp32.path}/ @{runtime.tools.vendor-esp32.path}/flags/include_tkl.txt
esp32.compiler.includes.vendor=-iprefix {runtime.tools.vendor-esp32.path}/ @{runtime.tools.vendor-esp32.path}/flags/include_vendor.txt
esp32.compiler.includes={compiler.includes.tuya_open} {compiler.includes.tkl} {compiler.includes.vendor}

esp32.compiler.flags.ld=@{runtime.tools.vendor-esp32.path}/flags/ld_flags.txt
esp32.compiler.flags.libs=-L{runtime.tools.vendor-esp32.path}/libs -L{runtime.tools.vendor-esp32.path}/link_path/tuya_open_sdk/build/esp-idf/esp_system/ld -L{runtime.tools.vendor-esp32.path}/link_path/esp-idf/components/bt/controller/lib_esp32/esp32 -L{runtime.tools.vendor-esp32.path}/link_path/esp-idf/components/esp_coex/lib/esp32 -L{runtime.tools.vendor-esp32.path}/link_path/esp-idf/components/esp_phy/lib/esp32 -L{runtime.tools.vendor-esp32.path}/link_path/esp-idf/components/esp_rom/esp32/ld -L{runtime.tools.vendor-esp32.path}/link_path/esp-idf/components/esp_wifi/lib/esp32 -L{runtime.tools.vendor-esp32.path}/link_path/esp-idf/components/soc/esp32/ld @{runtime.tools.vendor-esp32.path}/flags/libs_flags.txt
esp32.compiler.flags.ld_scripts=-Wl,--cref -Wl,-Map={build.path}/{build.project_name}.map -o {build.path}/{build.project_name}.elf

esp32.upload.tool=tyutool
esp32.upload.tool.default=tyutool

esp32.monitor_port.serial.baudrate=115200
