#!/bin/bash

# 清理编译缓存并重新编译

echo "🧹 清理编译缓存..."

# 删除编译目录
if [ -d ".build" ]; then
    rm -rf .build
    echo "✅ 删除 .build 目录"
fi

# 删除可能的缓存文件
find . -name "*.o" -delete
find . -name "*.d" -delete
echo "✅ 删除编译缓存文件"

echo ""
echo "🔍 验证关键文件..."

# 检查关键文件
if [ -f "src/tuya_main.c" ]; then
    echo "✅ tuya_main.c 存在"
    
    # 检查头文件包含
    if grep -q "#include \"pwm_door_control.h\"" src/tuya_main.c; then
        echo "✅ pwm_door_control.h 已包含"
    else
        echo "❌ pwm_door_control.h 未包含"
    fi
    
    # 检查函数调用
    if grep -q "pwm_door_control_init" src/tuya_main.c; then
        echo "✅ pwm_door_control_init 调用存在"
    else
        echo "❌ pwm_door_control_init 调用不存在"
    fi
    
    # 检查未使用函数是否已删除
    if grep -q "open_door1\|close_door1" src/tuya_main.c; then
        echo "❌ 仍有未使用的函数"
    else
        echo "✅ 未使用函数已删除"
    fi
else
    echo "❌ tuya_main.c 不存在"
fi

if [ -f "include/pwm_door_control.h" ]; then
    echo "✅ pwm_door_control.h 存在"
else
    echo "❌ pwm_door_control.h 不存在"
fi

if [ -f "src/pwm_door_control.c" ]; then
    echo "✅ pwm_door_control.c 存在"
else
    echo "❌ pwm_door_control.c 不存在"
fi

echo ""
echo "🚀 开始重新编译..."

# 重新编译
python ../../../tos.py build

echo ""
echo "✅ 编译完成!"
