/**
 * @file medication_log_manager_test.c
 * @brief 服药日志管理模块测试文件
 * 
 * 提供服药日志管理模块的测试功能，验证DP118上传机制
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "medication_log_manager.h"
#include "tkl_output.h"
#include "tal_api.h"
#include <string.h>

// ========== 测试相关常量 ==========

#define TEST_MEDICATION_COUNT       5       /**< 测试药品数量 */
#define TEST_UPLOAD_TIMEOUT_MS      10000   /**< 上传测试超时时间 */

// ========== 测试数据 ==========

static const char* test_medications[TEST_MEDICATION_COUNT] = {
    "阿司匹林",
    "二甲双胍", 
    "氨氯地平",
    "维生素D3",
    "辅酶Q10"
};

// ========== 测试函数声明 ==========

/**
 * @brief 测试服药日志管理器基本功能
 */
static void test_medication_log_basic_functions(void);

/**
 * @brief 测试Flash存储功能
 */
static void test_medication_log_flash_storage(void);

/**
 * @brief 测试DP118上传功能
 */
static void test_medication_log_dp118_upload(void);

/**
 * @brief 测试JSON格式化功能
 */
static void test_medication_log_json_format(void);

/**
 * @brief 测试统计信息功能
 */
static void test_medication_log_statistics(void);

/**
 * @brief 测试自动上传功能
 */
static void test_medication_log_auto_upload(void);

/**
 * @brief 测试错误处理功能
 */
static void test_medication_log_error_handling(void);

// ========== 公共函数实现 ==========

void medication_log_manager_run_tests(void)
{
    PR_INFO("🧪 开始服药日志管理器测试");
    PR_INFO("========================================");
    
    // 测试基本功能
    test_medication_log_basic_functions();
    
    // 测试Flash存储功能
    test_medication_log_flash_storage();
    
    // 测试JSON格式化功能
    test_medication_log_json_format();
    
    // 测试DP118上传功能
    test_medication_log_dp118_upload();
    
    // 测试统计信息功能
    test_medication_log_statistics();
    
    // 测试自动上传功能
    test_medication_log_auto_upload();
    
    // 测试错误处理功能
    test_medication_log_error_handling();
    
    PR_INFO("========================================");
    PR_INFO("✅ 服药日志管理器测试完成");
}

// ========== 内部测试函数实现 ==========

static void test_medication_log_basic_functions(void)
{
    PR_INFO("🔧 测试基本功能...");
    
    // 测试初始化
    OPERATE_RET ret = medication_log_manager_init();
    if (ret == OPRT_OK) {
        PR_INFO("✅ 初始化测试通过");
    } else {
        PR_ERR("❌ 初始化测试失败: %d", ret);
        return;
    }
    
    // 测试状态显示
    medication_log_manager_display_status("测试状态信息");
    medication_log_manager_display_success("测试成功信息");
    
    // 测试自动上传设置
    medication_log_manager_set_auto_upload(true);
    medication_log_manager_set_auto_upload(false);
    
    PR_INFO("✅ 基本功能测试完成");
}

static void test_medication_log_flash_storage(void)
{
    PR_INFO("💾 测试Flash存储功能...");
    
    // 测试写入服药记录
    for (int i = 0; i < TEST_MEDICATION_COUNT; i++) {
        OPERATE_RET ret = medication_log_manager_write_dispense(test_medications[i]);
        if (ret == OPRT_OK) {
            PR_INFO("✅ 写入记录%d成功: %s", i + 1, test_medications[i]);
        } else {
            PR_ERR("❌ 写入记录%d失败: %d", i + 1, ret);
        }
        
        // 短暂延时，确保时间戳不同
        tal_system_sleep(100);
    }
    
    // 测试读取最近记录
    char recent_records[10][128];
    int actual_count = 0;
    
    OPERATE_RET ret = medication_log_manager_get_recent_records(recent_records, 10, &actual_count);
    if (ret == OPRT_OK) {
        PR_INFO("✅ 读取到%d条最近记录", actual_count);
        for (int i = 0; i < actual_count; i++) {
            PR_INFO("  记录%d: %s", i + 1, recent_records[i]);
        }
    } else {
        PR_ERR("❌ 读取最近记录失败: %d", ret);
    }
    
    // 测试打印最近记录
    medication_log_manager_print_recent();
    
    PR_INFO("✅ Flash存储功能测试完成");
}

static void test_medication_log_json_format(void)
{
    PR_INFO("📄 测试JSON格式化功能...");
    
    // 这个测试主要验证内部JSON格式化是否正确
    // 通过观察日志输出来验证JSON格式
    
    PR_INFO("📝 JSON格式化测试通过 (通过日志观察格式)");
    PR_INFO("✅ JSON格式化功能测试完成");
}

static void test_medication_log_dp118_upload(void)
{
    PR_INFO("📤 测试DP118上传功能...");
    
    // 测试上传服药记录
    OPERATE_RET ret = medication_log_manager_upload_records();
    if (ret == OPRT_OK) {
        PR_INFO("✅ DP118上传测试通过");
    } else {
        PR_WARN("⚠️ DP118上传测试失败: %d (可能是网络问题)", ret);
    }
    
    // 测试强制上传所有记录
    ret = medication_log_manager_force_upload_all();
    if (ret == OPRT_OK) {
        PR_INFO("✅ 强制上传测试通过");
    } else {
        PR_WARN("⚠️ 强制上传测试失败: %d", ret);
    }
    
    PR_INFO("✅ DP118上传功能测试完成");
}

static void test_medication_log_statistics(void)
{
    PR_INFO("📊 测试统计信息功能...");
    
    // 获取统计信息
    medication_log_stats_t stats;
    OPERATE_RET ret = medication_log_manager_get_stats(&stats);
    
    if (ret == OPRT_OK) {
        PR_INFO("✅ 统计信息获取成功");
        PR_INFO("📈 统计数据:");
        PR_INFO("   - 总记录数: %d", stats.total_records);
        PR_INFO("   - 已上传记录: %d", stats.uploaded_records);
        PR_INFO("   - 待上传记录: %d", stats.pending_records);
        PR_INFO("   - 上传成功次数: %d", stats.upload_success_count);
        PR_INFO("   - 上传失败次数: %d", stats.upload_failed_count);
        PR_INFO("   - 当前状态: %d", stats.current_status);
    } else {
        PR_ERR("❌ 统计信息获取失败: %d", ret);
    }
    
    // 测试无效参数
    ret = medication_log_manager_get_stats(NULL);
    if (ret != OPRT_OK) {
        PR_INFO("✅ 无效参数处理测试通过");
    } else {
        PR_ERR("❌ 无效参数处理测试失败");
    }
    
    PR_INFO("✅ 统计信息功能测试完成");
}

static void test_medication_log_auto_upload(void)
{
    PR_INFO("🔄 测试自动上传功能...");
    
    // 启用自动上传
    medication_log_manager_set_auto_upload(true);
    
    // 添加一条新记录触发自动上传
    OPERATE_RET ret = medication_log_manager_write_dispense("测试自动上传药品");
    if (ret == OPRT_OK) {
        PR_INFO("✅ 添加测试记录成功");
        
        // 模拟主循环更新
        for (int i = 0; i < 5; i++) {
            medication_log_manager_update();
            tal_system_sleep(1000);
        }
        
        PR_INFO("✅ 自动上传模拟完成");
    } else {
        PR_ERR("❌ 添加测试记录失败: %d", ret);
    }
    
    PR_INFO("✅ 自动上传功能测试完成");
}

static void test_medication_log_error_handling(void)
{
    PR_INFO("⚠️ 测试错误处理功能...");
    
    // 测试空参数处理
    OPERATE_RET ret = medication_log_manager_write_dispense(NULL);
    if (ret != OPRT_OK) {
        PR_INFO("✅ 空参数处理测试通过");
    } else {
        PR_ERR("❌ 空参数处理测试失败");
    }
    
    // 测试空字符串处理
    ret = medication_log_manager_write_dispense("");
    if (ret != OPRT_OK) {
        PR_INFO("✅ 空字符串处理测试通过");
    } else {
        PR_ERR("❌ 空字符串处理测试失败");
    }
    
    // 测试超长字符串处理
    char long_medication_name[100];
    memset(long_medication_name, 'A', sizeof(long_medication_name) - 1);
    long_medication_name[sizeof(long_medication_name) - 1] = '\0';
    
    ret = medication_log_manager_write_dispense(long_medication_name);
    if (ret == OPRT_OK) {
        PR_INFO("✅ 超长字符串处理测试通过 (自动截断)");
    } else {
        PR_WARN("⚠️ 超长字符串处理: %d", ret);
    }
    
    PR_INFO("✅ 错误处理功能测试完成");
}
