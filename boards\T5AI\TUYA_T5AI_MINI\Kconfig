config CHIP_CHOICE
    string
    default "T5AI"

config <PERSON><PERSON><PERSON>_CHOICE
    string
    default "TUYA_T5AI_MINI"

config BOARD_CONFIG
    bool
    default y
    select ENABLE_AUDIO_CODECS
    select ENABLE_AUDIO_AEC
    select <PERSON>NABL<PERSON>_LED
    select <PERSON>NABLE_BUTTON

choice TUYA_T5AI_MINI_EX_MODULE
    prompt "choose expansion module for TUYA_T5AI_MINI"
    default TUYA_T5AI_MINI_EX_MODULE_NONE

    config TUYA_T5AI_MINI_EX_MODULE_NONE
        bool "no expansion module"

    config TUYA_T5AI_MINI_EX_MODULE_13565LCD
        bool "1.3 inch 240x240 spi LCD"
        select ENABLE_DISPLAY
        select LVGL_COLOR_16_SWAP if(ENABLE_LIBLVGL)
endchoice