
#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
#include "lvgl.h"
#elif defined(LV_BUILD_TEST)
#include "../lvgl.h"
#else
#include "lvgl/lvgl.h"
#endif

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_EMOJI_1F633_32
#define LV_ATTRIBUTE_EMOJI_1F633_32
#endif

static const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_EMOJI_1F633_32 uint8_t
    emoji_1f633_32_map[] = {

        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x49, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0x29, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0xe8, 0xfd,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x6a, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0xc8, 0xed, 0xa5, 0xc4, 0x44, 0xb4, 0x08, 0xf6, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x08, 0xf6, 0x44, 0xb4, 0xa5, 0xc4, 0xc8, 0xed, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x47, 0xd5, 0x02, 0x83, 0x20, 0x62, 0x20, 0x62,
        0x20, 0x62, 0x06, 0xcd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x06, 0xcd,
        0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x02, 0x83, 0x47, 0xdd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe8, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe,
        0x04, 0xac, 0x20, 0x62, 0x20, 0x62, 0x42, 0x8b, 0xa5, 0xc4, 0x87, 0xe5, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x87, 0xe5, 0xa5, 0xc4, 0x42, 0x8b, 0x20, 0x62,
        0x20, 0x62, 0x04, 0xac, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x2a, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0xa3, 0x9b, 0x20, 0x62, 0xc1, 0x7a, 0x47, 0xd5, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x47, 0xd5, 0xc1, 0x7a, 0x20, 0x62, 0xa3, 0x9b, 0x49, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xc6, 0xc4,
        0x20, 0x62, 0x01, 0x83, 0x09, 0xf6, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x09, 0xf6, 0x01, 0x83, 0x20, 0x62, 0xc6, 0xc4, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0x00, 0x00,
        0x00, 0x00, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x67, 0xdd, 0x83, 0x93, 0xe8, 0xf5, 0x69, 0xfe, 0x69, 0xfe,
        0x8d, 0xfe, 0xad, 0xfe, 0x6a, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x6a, 0xfe, 0xad, 0xfe, 0x8d, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xe8, 0xf5, 0x83, 0x93,
        0x67, 0xdd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x00, 0x00, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x13, 0xff, 0xbd, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xbf, 0xf7, 0x58, 0xff,
        0x8c, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x8c, 0xfe, 0x58, 0xff, 0xbf, 0xf7, 0xdf, 0xf7,
        0xdf, 0xf7, 0xbd, 0xf7, 0x13, 0xff, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x29, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x36, 0xff, 0xdf, 0xf7,
        0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xbd, 0xf7, 0x8c, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x8c, 0xfe, 0xbd, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0x36, 0xff,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0xae, 0xfe, 0xdf, 0xf7, 0xdf, 0xf7, 0x9e, 0xef, 0x35, 0xa5, 0x92, 0x8c, 0xbb, 0xd6,
        0xdf, 0xf7, 0xdf, 0xf7, 0x57, 0xff, 0x69, 0xfe, 0x69, 0xfe, 0x57, 0xff, 0xdf, 0xf7, 0xdf, 0xf7, 0xbb, 0xd6,
        0x92, 0x8c, 0x35, 0xa5, 0x9e, 0xef, 0xdf, 0xf7, 0xdf, 0xf7, 0xae, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x15, 0xff, 0xdf, 0xf7,
        0xdf, 0xf7, 0xaf, 0x73, 0x66, 0x29, 0x66, 0x29, 0xc7, 0x31, 0xfc, 0xde, 0xdf, 0xf7, 0xbe, 0xf7, 0x69, 0xfe,
        0x69, 0xfe, 0xbe, 0xf7, 0xdf, 0xf7, 0xfb, 0xde, 0xa7, 0x31, 0x66, 0x29, 0x66, 0x29, 0xaf, 0x73, 0xdf, 0xf7,
        0xdf, 0xf7, 0x15, 0xff, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x57, 0xff, 0xdf, 0xf7, 0xdf, 0xf7, 0xc7, 0x31, 0x66, 0x29, 0x66, 0x29,
        0x66, 0x29, 0x55, 0xa5, 0xdf, 0xf7, 0xdf, 0xf7, 0x6b, 0xfe, 0x6b, 0xfe, 0xdf, 0xf7, 0xdf, 0xf7, 0x55, 0xa5,
        0x66, 0x29, 0x66, 0x29, 0x66, 0x29, 0xc7, 0x31, 0xdf, 0xf7, 0xdf, 0xf7, 0x57, 0xff, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x36, 0xff,
        0xdf, 0xf7, 0xdf, 0xf7, 0xab, 0x52, 0x66, 0x29, 0x66, 0x29, 0x66, 0x29, 0x39, 0xc6, 0xdf, 0xf7, 0xdf, 0xf7,
        0x6a, 0xfe, 0x6a, 0xfe, 0xdf, 0xf7, 0xdf, 0xf7, 0x39, 0xc6, 0x66, 0x29, 0x66, 0x29, 0x66, 0x29, 0xcb, 0x52,
        0xdf, 0xf7, 0xdf, 0xf7, 0x36, 0xff, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0xeb, 0xfd, 0xaf, 0xfc, 0x55, 0xfd, 0xdf, 0xf7, 0xdf, 0xf7, 0xbb, 0xd6, 0x2d, 0x63,
        0x8a, 0x4a, 0xf4, 0x9c, 0xdf, 0xf7, 0xdf, 0xf7, 0x7a, 0xf7, 0x69, 0xfe, 0x69, 0xfe, 0x7a, 0xf7, 0xdf, 0xf7,
        0xdf, 0xf7, 0xf4, 0x9c, 0x8a, 0x4a, 0x2d, 0x63, 0xbb, 0xd6, 0xdf, 0xf7, 0xdf, 0xf7, 0x55, 0xfd, 0xaf, 0xfc,
        0xeb, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xcb, 0xfd, 0xd1, 0xfb, 0xd2, 0xfb,
        0xd2, 0xfb, 0x1c, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7,
        0xcf, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xcf, 0xfe, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7,
        0xdf, 0xf7, 0xdf, 0xf7, 0xfc, 0xf6, 0xd2, 0xfb, 0xd2, 0xfb, 0xf1, 0xfb, 0xcb, 0xfd, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x50, 0xfc, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0x33, 0xfc, 0xdc, 0xf6, 0xdf, 0xf7,
        0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xbe, 0xf7, 0xd1, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0xf1, 0xfe, 0xbe, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdf, 0xf7, 0xdc, 0xf6, 0x33, 0xfc, 0xd2, 0xfb,
        0xd2, 0xfb, 0xd2, 0xfb, 0x50, 0xfc, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x0a, 0xfe, 0xd2, 0xfb, 0xd2, 0xfb,
        0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xb5, 0xfc, 0xb8, 0xfd, 0x77, 0xfe, 0xf1, 0xfe, 0x6b, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x8b, 0xfe, 0xf1, 0xfe, 0x77, 0xfe,
        0xb8, 0xfd, 0xb5, 0xfc, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0x0a, 0xfe,
        0x69, 0xfe, 0x49, 0xfe, 0xcb, 0xfd, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb,
        0xd2, 0xfb, 0xd2, 0xfb, 0xaf, 0xfc, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xaf, 0xfc, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb,
        0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xcb, 0xfd, 0x69, 0xfe, 0xaa, 0xfe, 0x2a, 0xfe, 0xd2, 0xfb,
        0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xee, 0xfc, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0xee, 0xfc, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb,
        0x2a, 0xfe, 0xaa, 0xfe, 0x00, 0x00, 0x69, 0xfe, 0x70, 0xfc, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb,
        0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xcb, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xcb, 0xfd, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb,
        0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0x70, 0xfc, 0x69, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x69, 0xfe,
        0xea, 0xfd, 0x11, 0xfc, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0x0e, 0xfd, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x0e, 0xfd, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xd2, 0xfb, 0xf1, 0xfb,
        0xeb, 0xfd, 0x49, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x2a, 0xfe, 0xee, 0xfc, 0x70, 0xfc,
        0x30, 0xfc, 0xaf, 0xfc, 0xac, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x08, 0xf6, 0xc8, 0xed, 0xc8, 0xed,
        0xc8, 0xed, 0xc8, 0xed, 0xc8, 0xed, 0xc8, 0xed, 0x08, 0xf6, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xac, 0xfd,
        0xaf, 0xfc, 0x30, 0xfc, 0x70, 0xfc, 0xee, 0xfc, 0x2a, 0xfe, 0x69, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0xe8, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x26, 0xd5, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62,
        0x20, 0x62, 0x26, 0xd5, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x49, 0xfe, 0xc9, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xe8, 0xed, 0xa3, 0x9b, 0x83, 0x9b,
        0x83, 0x9b, 0x83, 0x9b, 0x83, 0x9b, 0x83, 0x9b, 0x83, 0x9b, 0xa3, 0x9b, 0xe8, 0xed, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x89, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x69, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc9, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xc9, 0xfe,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xaa, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xaa, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x59, 0x9b, 0xca, 0xea, 0xfa, 0xfa, 0xea, 0xca, 0x9a, 0x58, 0x0d,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x26, 0x9d, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x9c, 0x25, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x8c, 0xfb, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x8a, 0x08, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0xd3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc7, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x31, 0xea, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0xea, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xe3, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xd0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xcc, 0x09, 0x00, 0x00, 0x00, 0x00, 0x8c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x81, 0x00, 0x00,
        0x00, 0x26, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x18, 0x00, 0x00, 0x9d, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x96, 0x00, 0x0e, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf4, 0x0d, 0x59, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0x57, 0x9b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x99, 0xca, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc7, 0xeb, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xf8, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
        0xeb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xca, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc7, 0x9a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0x98, 0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0x55, 0x0c, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0c, 0x00, 0x9c,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9a, 0x00, 0x00, 0x25, 0xfb, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x22, 0x00, 0x00, 0x00, 0x8a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0x87, 0x00, 0x00, 0x00, 0x00, 0x08, 0xc7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc6, 0x07, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x18, 0xe2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x2e, 0xe3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xe2, 0x2d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xc1, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc6,
        0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x81, 0xf4, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x86, 0x07, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x96, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x99, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x57, 0x98, 0xc8, 0xe9, 0xf9, 0xf9, 0xe9, 0xc8, 0x97,
        0x56, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

};

const lv_image_dsc_t emoji_1f633_32 = {
    .header.magic = LV_IMAGE_HEADER_MAGIC,
    .header.cf = LV_COLOR_FORMAT_RGB565A8,
    .header.flags = 0,
    .header.w = 32,
    .header.h = 32,
    .header.stride = 64,
    .data_size = sizeof(emoji_1f633_32_map),
    .data = emoji_1f633_32_map,
};
