# Ktuyaconf
config PLATFORM_CHOICE
    string
    default "T2"

config PLATFORM_T2
    bool
    default y

config OPERATING_SYSTEM
    int
    default 98
    ---help---
        100     /* LINUX */
        98      /* RTOS */
        3       /* Non-OS */

rsource "./TKL_Kconfig"
rsource "./OS_SERVICE_Kconfig"

choice
    prompt "Choice a board"

    config BOARD_CHOICE_T2_U
        bool "T2-U"
        if (BOARD_CHOICE_T2_U)
            rsource "./T2-U/Kconfig"
        endif

endchoice
