/**
 * @file touch_ft5x06.h
 * @brief touch_ft5x06 module is used to
 * @version 0.1
 * @date 2025-05-22
 */

#ifndef __TOUCH_FT5X06_H__
#define __TOUCH_FT5X06_H__

#include "tuya_cloud_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
************************macro define************************
***********************************************************/

/***********************************************************
***********************typedef define***********************
***********************************************************/

/***********************************************************
********************function declaration********************
***********************************************************/

int touch_ft5x06_init(void);

void *touch_ft5x06_get_handle(void);

#ifdef __cplusplus
}
#endif

#endif /* __TOUCH_FT5X06_H__ */
