name=Tuya
version=1.1.6

#
## Arduino build
#
## prebuild
recipe.hooks.prebuild.1.pattern="{runtime.tools.env-python.path}/python3" -I "{runtime.platform.path}/tools/prebuild.py" --chip "{build.chip}" --sketch-path "{sketch_path}" --build-path "{build.path}"
recipe.hooks.prebuild.1.pattern.windows="{runtime.tools.env-python.path}/python.exe" -I "{runtime.platform.path}/tools/prebuild.py" --chip "{build.chip}" --sketch-path "{sketch_path}" --build-path "{build.path}"

## Compile c files
recipe.c.o.pattern="{compiler.cmd.c}" {compiler.flags.c} {appBuild.flags} -DARDUINO={runtime.ide.version} -DARDUINO_{build.board} -DBOARD_NAME="{build.board}" {compiler.includes} {includes} {source_file} -o {object_file}

## Compile cpp files
recipe.cpp.o.pattern="{compiler.cmd.cpp}" {compiler.flags.cpp} {appBuild.flags} -DARDUINO={runtime.ide.version} -DARDUINO_{build.board} -DBOARD_NAME="{build.board}" {compiler.includes} {includes} {source_file} -o {object_file}

## Compile S files
recipe.S.o.pattern="{compiler.cmd.S}" {compiler.flags.S} {appBuild.flags} -DARDUINO={runtime.ide.version} -DARDUINO_{build.board} -DBOARD_NAME="{build.board}" {compiler.includes} {includes} {source_file} -o {object_file}

## ar
archive_file_path={build.path}/{archive_file}
recipe.ar.pattern="{compiler.cmd.ar}" {compiler.flags.ar} "{archive_file_path}" "{object_file}"

## combine
recipe.c.combine.pattern="{compiler.cmd.ld}" "{compiler.flags.ld}" {object_files} -Wl,--start-group {archive_file_path} {compiler.flags.libs} -Wl,--end-group {compiler.flags.ld_scripts}

## objcopy
recipe.objcopy.bin.1.pattern="{runtime.tools.env-python.path}/python3" -I "{runtime.platform.path}/tools/objcopy.py" --compiler-path "{compiler.path}" --chip "{build.chip}" --vendor-path "{vendor.path}" --build-path "{build.path}" --sketch-name "{build.project_name}"
recipe.objcopy.bin.1.pattern.windows="{runtime.tools.env-python.path}/python.exe" -I "{runtime.platform.path}/tools/objcopy.py" --compiler-path "{compiler.path}" --chip "{build.chip}" --vendor-path "{vendor.path}" --build-path "{build.path}" --sketch-name "{build.project_name}"

## upload
tools.tyutool.upload.protocol=serial
tools.tyutool.upload.pattern={runtime.tools.tyutool.path}/tyutool_cli -n write -d {build.chip} -p {upload.port.address} -b {upload.speed} -s 0x000000 -f {build.path}/{build.project_name}_QIO.bin --tqdm
tools.tyutool.upload.pattern.windows={runtime.tools.tyutool.path}/tyutool_cli.exe -n write -d {build.chip} -p {upload.port.address} -b {upload.speed} -s 0x000000 -f {build.path}/{build.project_name}_QIO.bin --tqdm

## export compiled binary
recipe.hooks.savehex.postsavehex.1.pattern="{runtime.tools.env-python.path}/python3" -I "{runtime.platform.path}/tools/export_binary.py" --build-path {build.path} --sketch-path {sketch_path}
recipe.hooks.savehex.postsavehex.1.pattern.windows="{runtime.tools.env-python.path}/python.exe" -I "{runtime.platform.path}/tools/export_binary.py" --build-path {build.path} --sketch-path {sketch_path}
