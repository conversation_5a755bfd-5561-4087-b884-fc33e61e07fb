/**
 * @file pwm_door_test.h
 * @brief PWM门控制系统测试程序头文件
 * 
 * 提供PWM门控制系统的测试功能声明
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#ifndef __PWM_DOOR_TEST_H__
#define __PWM_DOOR_TEST_H__

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 运行完整的PWM门控制系统测试
 * 
 * 包含以下测试项目：
 * - 系统初始化测试
 * - 单个舵机开关测试
 * - 角度控制测试
 * - DP命令处理测试
 * - 系统状态查询测试
 * - 内置测试程序
 * - 系统清理测试
 */
void pwm_door_run_full_test(void);

/**
 * @brief 运行简单的PWM门控制测试
 * 
 * 包含基本的开关功能测试，适合快速验证系统是否正常工作
 */
void pwm_door_run_simple_test(void);

#ifdef __cplusplus
}
#endif

#endif /* __PWM_DOOR_TEST_H__ */
