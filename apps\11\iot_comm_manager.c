/**
 * @file iot_comm_manager.c
 * @brief IoT通信管理模块实现
 * 
 * 提供IoT数据包管理、网络状态管理和DP数据发送功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "iot_comm_manager.h"
#include "tuya_iot_dp.h"
#include "tal_api.h"
#include "tkl_output.h"
#include <string.h>

// ========== 内部结构体定义 ==========

/**
 * @brief IoT通信管理器内部状态结构体
 */
typedef struct {
    bool initialized;                               /**< 初始化状态 */
    bool network_connected;                         /**< 网络连接状态 */
    iot_data_packet_t queue[IOT_COMM_QUEUE_SIZE];   /**< 数据包队列 */
    uint32_t queue_head;                            /**< 队列头指针 */
    uint32_t queue_tail;                            /**< 队列尾指针 */
    uint32_t queue_count;                           /**< 队列中数据包数量 */
    uint32_t next_packet_id;                        /**< 下一个数据包ID */
    uint32_t total_sent;                            /**< 总发送数量 */
    uint32_t total_success;                         /**< 成功发送数量 */
    uint32_t total_failed;                          /**< 失败发送数量 */
} iot_comm_manager_t;

// ========== 全局变量 ==========

/**
 * @brief 全局IoT通信管理器实例
 */
static iot_comm_manager_t g_iot_comm = {0};

// ========== 内部函数声明 ==========

/**
 * @brief 获取优先级名称字符串
 * 
 * @param priority 优先级枚举值
 * @return const char* 优先级名称
 */
static const char* get_priority_name(iot_priority_e priority);

// ========== 公共函数实现 ==========

OPERATE_RET iot_comm_manager_init(void)
{
    PR_INFO("🔧 初始化IoT通信管理器...");
    
    // 清空管理器状态
    memset(&g_iot_comm, 0, sizeof(iot_comm_manager_t));
    
    // 初始化基本参数
    g_iot_comm.next_packet_id = 1;
    g_iot_comm.initialized = true;
    g_iot_comm.network_connected = false;
    
    PR_INFO("✅ IoT通信管理器初始化完成");
    PR_INFO("📊 配置参数:");
    PR_INFO("   - 最大重试次数: %d", IOT_COMM_MAX_RETRY_COUNT);
    PR_INFO("   - 重试间隔: %d ms", IOT_COMM_RETRY_INTERVAL_MS);
    PR_INFO("   - 超时时间: %d ms", IOT_COMM_TIMEOUT_MS);
    PR_INFO("   - 队列大小: %d", IOT_COMM_QUEUE_SIZE);
    PR_INFO("   - 最大载荷: %d bytes", IOT_COMM_MAX_PAYLOAD_SIZE);
    
    return OPRT_OK;
}

void iot_comm_manager_set_network_status(bool connected)
{
    if (!g_iot_comm.initialized) {
        PR_WARN("⚠️ IoT通信管理器未初始化");
        return;
    }
    
    bool status_changed = (g_iot_comm.network_connected != connected);
    g_iot_comm.network_connected = connected;
    
    if (status_changed) {
        PR_INFO("🌐 网络状态更新: %s", connected ? "已连接" : "断开");
        
        if (connected) {
            PR_INFO("📡 网络连接已建立，可以发送数据");
        } else {
            PR_WARN("📡 网络连接已断开，数据发送将失败");
        }
    }
}

uint32_t iot_comm_manager_send_dp_data(const char *dp_json, iot_priority_e priority)
{
    if (!g_iot_comm.initialized) {
        PR_ERR("❌ IoT通信管理器未初始化");
        return 0;
    }
    
    if (!dp_json) {
        PR_ERR("❌ DP数据为空");
        return 0;
    }
    
    size_t json_len = strlen(dp_json);
    if (json_len == 0 || json_len >= IOT_COMM_MAX_PAYLOAD_SIZE) {
        PR_ERR("❌ DP数据长度无效: %d", json_len);
        return 0;
    }
    
    PR_DEBUG("📤 准备发送DP数据 (优先级: %s, 长度: %d)", 
             get_priority_name(priority), json_len);
    PR_DEBUG("📄 数据内容: %s", dp_json);
    
    // 直接发送，不使用队列（简化实现）
    OPERATE_RET ret = tuya_iot_dp_report_json_async(
        tuya_iot_client_get(), 
        dp_json, 
        NULL, 
        NULL, 
        NULL, 
        IOT_COMM_TIMEOUT_MS
    );
    
    // 更新统计信息
    g_iot_comm.total_sent++;
    uint32_t packet_id = g_iot_comm.next_packet_id++;
    
    if (ret == OPRT_OK) {
        g_iot_comm.total_success++;
        PR_INFO("✅ DP数据发送成功 (ID: %d, 优先级: %s)", 
                packet_id, get_priority_name(priority));
    } else {
        g_iot_comm.total_failed++;
        PR_ERR("❌ DP数据发送失败 (ID: %d, 错误码: %d)", packet_id, ret);
    }
    
    return packet_id;
}

void iot_comm_manager_get_stats(uint32_t *total_sent, uint32_t *total_success, uint32_t *total_failed)
{
    if (!g_iot_comm.initialized) {
        if (total_sent) *total_sent = 0;
        if (total_success) *total_success = 0;
        if (total_failed) *total_failed = 0;
        return;
    }
    
    if (total_sent) *total_sent = g_iot_comm.total_sent;
    if (total_success) *total_success = g_iot_comm.total_success;
    if (total_failed) *total_failed = g_iot_comm.total_failed;
}

bool iot_comm_manager_is_network_connected(void)
{
    return g_iot_comm.initialized && g_iot_comm.network_connected;
}

void iot_comm_manager_reset_stats(void)
{
    if (!g_iot_comm.initialized) {
        PR_WARN("⚠️ IoT通信管理器未初始化");
        return;
    }
    
    PR_INFO("🔄 重置IoT通信统计信息");
    PR_INFO("📊 重置前统计: 发送=%d, 成功=%d, 失败=%d", 
            g_iot_comm.total_sent, g_iot_comm.total_success, g_iot_comm.total_failed);
    
    g_iot_comm.total_sent = 0;
    g_iot_comm.total_success = 0;
    g_iot_comm.total_failed = 0;
    
    PR_INFO("✅ 统计信息已重置");
}

// ========== 内部函数实现 ==========

static const char* get_priority_name(iot_priority_e priority)
{
    switch (priority) {
        case IOT_PRIORITY_LOW:    return "低优先级";
        case IOT_PRIORITY_NORMAL: return "普通优先级";
        case IOT_PRIORITY_HIGH:   return "高优先级";
        case IOT_PRIORITY_URGENT: return "紧急优先级";
        default:                  return "未知优先级";
    }
}
