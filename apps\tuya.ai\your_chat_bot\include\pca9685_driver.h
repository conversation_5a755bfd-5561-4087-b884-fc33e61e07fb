/**
 * @file pca9685_driver.h
 * @brief PCA9685 16通道PWM驱动器头文件
 * 
 * 基于TuyaOS I2C接口实现PCA9685驱动，用于控制MG90S舵机
 * 参考：https://gitee.com/qi-zezhong/pca9685-stm32
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#ifndef __PCA9685_DRIVER_H__
#define __PCA9685_DRIVER_H__

#include "tuya_cloud_types.h"
#include "tkl_i2c.h"

#ifdef __cplusplus
extern "C" {
#endif

// ========== PCA9685寄存器定义 ==========

/**
 * @brief PCA9685寄存器地址定义
 */
#define PCA9685_MODE1           0x00    /**< 模式寄存器1 */
#define PCA9685_MODE2           0x01    /**< 模式寄存器2 */
#define PCA9685_SUBADR1         0x02    /**< 子地址1 */
#define PCA9685_SUBADR2         0x03    /**< 子地址2 */
#define PCA9685_SUBADR3         0x04    /**< 子地址3 */
#define PCA9685_ALLCALLADR      0x05    /**< 全呼地址 */

#define PCA9685_LED0_ON_L       0x06    /**< LED0开启时间低字节 */
#define PCA9685_LED0_ON_H       0x07    /**< LED0开启时间高字节 */
#define PCA9685_LED0_OFF_L      0x08    /**< LED0关闭时间低字节 */
#define PCA9685_LED0_OFF_H      0x09    /**< LED0关闭时间高字节 */

#define PCA9685_ALL_LED_ON_L    0xFA    /**< 所有LED开启时间低字节 */
#define PCA9685_ALL_LED_ON_H    0xFB    /**< 所有LED开启时间高字节 */
#define PCA9685_ALL_LED_OFF_L   0xFC    /**< 所有LED关闭时间低字节 */
#define PCA9685_ALL_LED_OFF_H   0xFD    /**< 所有LED关闭时间高字节 */

#define PCA9685_PRESCALE        0xFE    /**< 预分频寄存器 */

/**
 * @brief PCA9685模式1寄存器位定义
 */
#define PCA9685_MODE1_RESTART   0x80    /**< 重启位 */
#define PCA9685_MODE1_EXTCLK    0x40    /**< 外部时钟位 */
#define PCA9685_MODE1_AI        0x20    /**< 自动递增位 */
#define PCA9685_MODE1_SLEEP     0x10    /**< 睡眠位 */
#define PCA9685_MODE1_SUB1      0x08    /**< 子地址1响应位 */
#define PCA9685_MODE1_SUB2      0x04    /**< 子地址2响应位 */
#define PCA9685_MODE1_SUB3      0x02    /**< 子地址3响应位 */
#define PCA9685_MODE1_ALLCALL   0x01    /**< 全呼响应位 */

/**
 * @brief PCA9685模式2寄存器位定义
 */
#define PCA9685_MODE2_INVRT     0x10    /**< 输出逻辑状态反转 */
#define PCA9685_MODE2_OCH       0x08    /**< 输出变化方式 */
#define PCA9685_MODE2_OUTDRV    0x04    /**< 输出驱动器结构 */
#define PCA9685_MODE2_OUTNE1    0x02    /**< 输出使能位1 */
#define PCA9685_MODE2_OUTNE0    0x01    /**< 输出使能位0 */

// ========== PCA9685配置常量 ==========

/**
 * @brief PCA9685设备地址和配置
 */
#define PCA9685_I2C_ADDR        0x40    /**< PCA9685默认I2C地址 */
#define PCA9685_I2C_PORT        TUYA_I2C_NUM_0  /**< 使用I2C端口0 */

/**
 * @brief PCA9685时钟和频率配置
 */
#define PCA9685_INTERNAL_FREQ   25000000    /**< 内部振荡器频率 25MHz */
#define PCA9685_PWM_FREQ        50          /**< PWM频率 50Hz (舵机标准) */
#define PCA9685_PWM_RESOLUTION  4096        /**< PWM分辨率 12位 (0-4095) */

/**
 * @brief MG90S舵机PWM参数 (基于PCA9685)
 */
#define MG90S_MIN_PULSE_WIDTH   500     /**< 最小脉宽 0.5ms (0度) */
#define MG90S_MAX_PULSE_WIDTH   2500    /**< 最大脉宽 2.5ms (180度) */
#define MG90S_CENTER_PULSE_WIDTH 1500   /**< 中心脉宽 1.5ms (90度) */

/**
 * @brief 舵机通道定义
 */
#define SERVO_CHANNEL_0         0       /**< 舵机通道0 (最左边接口) */
#define SERVO_CHANNEL_MAX       16      /**< PCA9685最大通道数 */

// ========== 数据结构定义 ==========

/**
 * @brief PCA9685设备状态
 */
typedef struct {
    bool initialized;                   /**< 初始化状态 */
    uint8_t device_addr;               /**< 设备I2C地址 */
    TUYA_I2C_NUM_E i2c_port;          /**< I2C端口 */
    uint16_t pwm_freq;                 /**< PWM频率 */
    uint8_t prescale_value;            /**< 预分频值 */
} pca9685_device_t;

/**
 * @brief 舵机控制参数
 */
typedef struct {
    uint8_t channel;                   /**< 舵机通道 */
    uint16_t angle;                    /**< 目标角度 (0-180度) */
    uint16_t pulse_width;              /**< 脉宽 (微秒) */
    uint16_t pwm_value;                /**< PWM值 (0-4095) */
} servo_control_t;

// ========== 函数声明 ==========

/**
 * @brief 初始化PCA9685驱动
 * 
 * @return OPERATE_RET 
 *         - OPRT_OK: 成功
 *         - 其他: 失败错误码
 */
OPERATE_RET pca9685_init(void);

/**
 * @brief 反初始化PCA9685驱动
 * 
 * @return OPERATE_RET 
 *         - OPRT_OK: 成功
 *         - 其他: 失败错误码
 */
OPERATE_RET pca9685_deinit(void);

/**
 * @brief 设置PCA9685 PWM频率
 * 
 * @param freq PWM频率 (Hz)
 * @return OPERATE_RET 
 *         - OPRT_OK: 成功
 *         - 其他: 失败错误码
 */
OPERATE_RET pca9685_set_pwm_freq(uint16_t freq);

/**
 * @brief 设置指定通道的PWM输出
 * 
 * @param channel 通道号 (0-15)
 * @param on_time 开启时间 (0-4095)
 * @param off_time 关闭时间 (0-4095)
 * @return OPERATE_RET 
 *         - OPRT_OK: 成功
 *         - 其他: 失败错误码
 */
OPERATE_RET pca9685_set_pwm(uint8_t channel, uint16_t on_time, uint16_t off_time);

/**
 * @brief 设置舵机角度
 * 
 * @param channel 舵机通道 (0-15)
 * @param angle 角度 (0-180度)
 * @return OPERATE_RET 
 *         - OPRT_OK: 成功
 *         - 其他: 失败错误码
 */
OPERATE_RET pca9685_set_servo_angle(uint8_t channel, uint16_t angle);

/**
 * @brief 设置舵机脉宽
 * 
 * @param channel 舵机通道 (0-15)
 * @param pulse_width 脉宽 (微秒)
 * @return OPERATE_RET 
 *         - OPRT_OK: 成功
 *         - 其他: 失败错误码
 */
OPERATE_RET pca9685_set_servo_pulse_width(uint8_t channel, uint16_t pulse_width);

/**
 * @brief 停止指定通道输出
 * 
 * @param channel 通道号 (0-15)
 * @return OPERATE_RET 
 *         - OPRT_OK: 成功
 *         - 其他: 失败错误码
 */
OPERATE_RET pca9685_stop_channel(uint8_t channel);

/**
 * @brief 停止所有通道输出
 * 
 * @return OPERATE_RET 
 *         - OPRT_OK: 成功
 *         - 其他: 失败错误码
 */
OPERATE_RET pca9685_stop_all_channels(void);

/**
 * @brief 获取PCA9685设备状态
 * 
 * @return const pca9685_device_t* 设备状态指针
 */
const pca9685_device_t* pca9685_get_device_status(void);

/**
 * @brief 角度转换为脉宽
 * 
 * @param angle 角度 (0-180度)
 * @return uint16_t 脉宽 (微秒)
 */
uint16_t pca9685_angle_to_pulse_width(uint16_t angle);

/**
 * @brief 脉宽转换为PWM值
 * 
 * @param pulse_width 脉宽 (微秒)
 * @return uint16_t PWM值 (0-4095)
 */
uint16_t pca9685_pulse_width_to_pwm(uint16_t pulse_width);

#ifdef __cplusplus
}
#endif

#endif /* __PCA9685_DRIVER_H__ */
