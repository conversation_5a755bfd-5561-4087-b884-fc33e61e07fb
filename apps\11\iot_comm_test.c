/**
 * @file iot_comm_test.c
 * @brief IoT通信管理模块测试文件
 * 
 * 提供IoT通信管理模块的测试功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "iot_comm_manager.h"
#include "tkl_output.h"
#include "tal_api.h"
#include <string.h>

// ========== 测试函数声明 ==========

/**
 * @brief 测试IoT通信管理器基本功能
 */
static void test_iot_comm_basic_functions(void);

/**
 * @brief 测试DP数据发送功能
 */
static void test_iot_comm_dp_sending(void);

/**
 * @brief 测试网络状态管理
 */
static void test_iot_comm_network_status(void);

/**
 * @brief 测试统计信息功能
 */
static void test_iot_comm_statistics(void);

// ========== 公共函数实现 ==========

/**
 * @brief 运行IoT通信管理器完整测试
 */
void iot_comm_manager_run_tests(void)
{
    PR_INFO("🧪 开始IoT通信管理器测试");
    PR_INFO("========================================");
    
    // 测试基本功能
    test_iot_comm_basic_functions();
    
    // 测试网络状态管理
    test_iot_comm_network_status();
    
    // 测试DP数据发送
    test_iot_comm_dp_sending();
    
    // 测试统计信息
    test_iot_comm_statistics();
    
    PR_INFO("========================================");
    PR_INFO("✅ IoT通信管理器测试完成");
}

// ========== 内部测试函数实现 ==========

static void test_iot_comm_basic_functions(void)
{
    PR_INFO("🔧 测试基本功能...");
    
    // 测试初始化
    OPERATE_RET ret = iot_comm_manager_init();
    if (ret == OPRT_OK) {
        PR_INFO("✅ 初始化测试通过");
    } else {
        PR_ERR("❌ 初始化测试失败: %d", ret);
        return;
    }
    
    // 测试网络连接状态检查
    bool connected = iot_comm_manager_is_network_connected();
    PR_INFO("📡 初始网络状态: %s", connected ? "已连接" : "未连接");
    
    PR_INFO("✅ 基本功能测试完成");
}

static void test_iot_comm_network_status(void)
{
    PR_INFO("🌐 测试网络状态管理...");
    
    // 测试设置网络连接状态
    iot_comm_manager_set_network_status(true);
    bool connected = iot_comm_manager_is_network_connected();
    if (connected) {
        PR_INFO("✅ 网络连接状态设置测试通过");
    } else {
        PR_ERR("❌ 网络连接状态设置测试失败");
    }
    
    // 测试断开网络
    iot_comm_manager_set_network_status(false);
    connected = iot_comm_manager_is_network_connected();
    if (!connected) {
        PR_INFO("✅ 网络断开状态设置测试通过");
    } else {
        PR_ERR("❌ 网络断开状态设置测试失败");
    }
    
    // 恢复网络连接用于后续测试
    iot_comm_manager_set_network_status(true);
    
    PR_INFO("✅ 网络状态管理测试完成");
}

static void test_iot_comm_dp_sending(void)
{
    PR_INFO("📤 测试DP数据发送...");
    
    // 测试发送不同优先级的DP数据
    const char* test_dp_data[] = {
        "{\"101\":\"测试药品1 1 1 无副作用 无记录 false false\"}",
        "{\"102\":\"测试药品2 2 2 饭后服用 无记录 true false\"}",
        "{\"108\":\"系统测试消息\"}"
    };
    
    iot_priority_e priorities[] = {
        IOT_PRIORITY_LOW,
        IOT_PRIORITY_NORMAL,
        IOT_PRIORITY_HIGH
    };
    
    for (int i = 0; i < 3; i++) {
        uint32_t packet_id = iot_comm_manager_send_dp_data(test_dp_data[i], priorities[i]);
        if (packet_id > 0) {
            PR_INFO("✅ DP数据发送测试%d通过 (ID: %d)", i + 1, packet_id);
        } else {
            PR_ERR("❌ DP数据发送测试%d失败", i + 1);
        }
        
        // 短暂延时
        tal_system_sleep(100);
    }
    
    // 测试无效数据
    uint32_t invalid_id = iot_comm_manager_send_dp_data(NULL, IOT_PRIORITY_NORMAL);
    if (invalid_id == 0) {
        PR_INFO("✅ 无效数据处理测试通过");
    } else {
        PR_ERR("❌ 无效数据处理测试失败");
    }
    
    PR_INFO("✅ DP数据发送测试完成");
}

static void test_iot_comm_statistics(void)
{
    PR_INFO("📊 测试统计信息功能...");
    
    // 获取当前统计信息
    uint32_t total_sent, total_success, total_failed;
    iot_comm_manager_get_stats(&total_sent, &total_success, &total_failed);
    
    PR_INFO("📈 当前统计信息:");
    PR_INFO("   - 总发送: %d", total_sent);
    PR_INFO("   - 成功: %d", total_success);
    PR_INFO("   - 失败: %d", total_failed);
    
    if (total_sent > 0) {
        PR_INFO("✅ 统计信息获取测试通过");
    } else {
        PR_WARN("⚠️ 统计信息为空，可能是因为没有实际发送数据");
    }
    
    // 测试统计重置
    iot_comm_manager_reset_stats();
    iot_comm_manager_get_stats(&total_sent, &total_success, &total_failed);
    
    if (total_sent == 0 && total_success == 0 && total_failed == 0) {
        PR_INFO("✅ 统计重置测试通过");
    } else {
        PR_ERR("❌ 统计重置测试失败");
    }
    
    PR_INFO("✅ 统计信息功能测试完成");
}
