/*******************************************************************************
 * Size: 20 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --force-fast-kern-format --font ../../tmp/fa-regular-400.ttf --format lvgl --lv-include lvgl.h --bpp 4 -o font_awesome_20_4.c --size 20 -r 0xf5a4,0xf118,0xf59b,0xf588,0xe384,0xf556,0xf5b3,0xf584,0xf579,0xe36b,0xe375,0xe39b,0xf4da,0xe398,0xe392,0xe372,0xf598,0xe409,0xe38d,0xe3a4,0xe36d,0xf240,0xf241,0xf242,0xf243,0xf244,0xf377,0xf376,0xf1eb,0xf6ab,0xf6aa,0xf6ac,0xf012,0xf68f,0xf68e,0xf68d,0xf68c,0xf695,0xf028,0xf6a8,0xf027,0xf6a9,0xf001,0xf00c,0xf00d,0xf011,0xf013,0xf1f8,0xf015,0xf03e,0xf044,0xf048,0xf051,0xf04b,0xf04c,0xf04d,0xf060,0xf061,0xf062,0xf063,0xf071,0xf0f3,0xf3c5,0xf0ac,0xf124,0xf7c2,0xf293,0xf075,0xe1ec,0xf007,0xe04b,0xf019
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef FONT_AWESOME_20_4
#define FONT_AWESOME_20_4 1
#endif

#if FONT_AWESOME_20_4

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+E04B "" */
    0x0, 0x0, 0x0, 0x0, 0x51, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xcf, 0xff, 0xff,
    0xff, 0xd7, 0x0, 0x0, 0x0, 0x3f, 0xfe, 0xdd,
    0xdd, 0xde, 0xff, 0x70, 0x0, 0x50, 0x9f, 0x60,
    0x0, 0x0, 0x0, 0x2f, 0xd0, 0x51, 0xf3, 0xbf,
    0x21, 0xca, 0x4, 0xd7, 0xe, 0xf0, 0xe5, 0xf3,
    0xbf, 0x25, 0xff, 0x19, 0xfd, 0xd, 0xf0, 0xe5,
    0xf3, 0xbf, 0x20, 0x86, 0x1, 0x94, 0xd, 0xf0,
    0xe5, 0xf3, 0xbf, 0x20, 0x0, 0x0, 0x0, 0xe,
    0xf0, 0xe5, 0x50, 0x9f, 0x65, 0xd0, 0xd5, 0x5d,
    0x2f, 0xd0, 0x51, 0x0, 0x3f, 0xff, 0xfd, 0xfe,
    0xef, 0xff, 0x70, 0x0, 0x0, 0x4, 0xcf, 0xff,
    0xff, 0xff, 0xd7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x9d,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xb4, 0x0, 0x1e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0xaf, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf0, 0xef, 0x0, 0x4, 0xc0, 0xc4, 0x4c, 0x0,
    0xa, 0xf4, 0xfe, 0x22, 0x27, 0xe2, 0xe7, 0x7e,
    0x22, 0x29, 0xf5, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x2a, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0x50,

    /* U+E1EC "" */
    0x0, 0x0, 0x0, 0x34, 0x0, 0x52, 0x0, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xe0, 0xf,
    0xc0, 0x6f, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x1, 0xfc, 0x7, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x20, 0x0, 0x0, 0x6, 0xff, 0xdd, 0xdd, 0xdd,
    0xdd, 0xde, 0xfd, 0x0, 0x0, 0x0, 0xbf, 0x30,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf1, 0x0, 0x1e,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0x60, 0xbd, 0xff, 0x20, 0x3, 0x80, 0x0,
    0x63, 0xc, 0xfd, 0xc4, 0x0, 0xb, 0xf2, 0x0,
    0xcf, 0x40, 0xe, 0xa0, 0xcf, 0x10, 0x0, 0x34,
    0xcf, 0x20, 0x3f, 0xfb, 0x0, 0xea, 0xc, 0xf6,
    0x40, 0x1f, 0xff, 0xf2, 0xa, 0xfa, 0xf2, 0xe,
    0xa0, 0xcf, 0xff, 0x70, 0x78, 0xdf, 0x21, 0xfe,
    0xbf, 0x90, 0xea, 0xc, 0xf9, 0x82, 0x0, 0xb,
    0xf2, 0x8f, 0xff, 0xff, 0x1e, 0xa0, 0xcf, 0x10,
    0x0, 0x79, 0xef, 0x27, 0x90, 0x2, 0xc1, 0x96,
    0xc, 0xfa, 0x92, 0x1f, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0x70, 0x13, 0xcf,
    0x30, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x30,
    0x0, 0x7, 0xff, 0xdd, 0xdd, 0xdd, 0xdd, 0xde,
    0xfd, 0x0, 0x0, 0x0, 0x9, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x1, 0xfc, 0x7, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xe0, 0xf, 0xc0, 0x6f, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0x0, 0x52,
    0x0, 0x60, 0x0, 0x0, 0x0,

    /* U+E36B "" */
    0x0, 0x0, 0x0, 0x15, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0,
    0x2, 0xff, 0x60, 0x1, 0x0, 0x0, 0x10, 0x6,
    0xff, 0x20, 0xb, 0xf9, 0x2a, 0xff, 0x0, 0x0,
    0xff, 0xa2, 0x9f, 0xb0, 0x3f, 0xd2, 0xfb, 0x51,
    0x0, 0x0, 0x14, 0xbf, 0x2d, 0xf3, 0x8f, 0x60,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x4, 0x6, 0xf8,
    0xcf, 0x10, 0x1, 0xec, 0x0, 0x0, 0xce, 0x20,
    0x1, 0xfc, 0xef, 0x0, 0x4, 0xff, 0x10, 0x0,
    0xff, 0x40, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x64,
    0x0, 0x0, 0x46, 0x0, 0x0, 0xef, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xcf, 0x10, 0x0, 0x0, 0x2b, 0xb2, 0x0, 0x0,
    0x1, 0xfc, 0x8f, 0x60, 0x0, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0x6, 0xf8, 0x3f, 0xd0, 0x0, 0x0,
    0xdf, 0xfd, 0x0, 0x0, 0xd, 0xf3, 0xb, 0xf9,
    0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x9f, 0xb0,
    0x2, 0xff, 0x70, 0x0, 0x5f, 0xf5, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9b, 0xb9, 0x61, 0x0,
    0x0, 0x0,

    /* U+E36D "" */
    0x0, 0x0, 0x0, 0x15, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0xb, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x3f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf3, 0x8f, 0x60,
    0x0, 0x86, 0x0, 0x0, 0x68, 0x0, 0x6, 0xf8,
    0xcf, 0x10, 0x4, 0xff, 0x10, 0x0, 0xff, 0x50,
    0x1, 0xfc, 0xef, 0x0, 0x1, 0xca, 0x0, 0x0,
    0x9c, 0x10, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xcf, 0x10, 0x0, 0x0, 0x17, 0xac, 0xdc, 0x10,
    0x1, 0xfc, 0x8f, 0x60, 0x0, 0x8, 0xff, 0xff,
    0xff, 0x30, 0x6, 0xf8, 0x3f, 0xd0, 0x0, 0xcf,
    0xf9, 0x31, 0x0, 0x0, 0xd, 0xf3, 0xb, 0xf9,
    0x0, 0xdc, 0x20, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x2, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9b, 0xb9, 0x61, 0x0,
    0x0, 0x0,

    /* U+E372 "" */
    0x0, 0x0, 0x0, 0x16, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x3, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x30, 0x0, 0x0, 0x3f,
    0xf9, 0x10, 0x0, 0x0, 0x0, 0x8f, 0xf4, 0x0,
    0x1, 0xef, 0x60, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfe, 0x10, 0xb, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xb0, 0x2f, 0xe0, 0x1, 0x9c,
    0x50, 0x8, 0xc7, 0x0, 0xd, 0xf3, 0x8f, 0x70,
    0x1e, 0xff, 0x60, 0xa, 0xff, 0xb0, 0x6, 0xf8,
    0xcf, 0x20, 0x9f, 0x90, 0x0, 0x0, 0x1c, 0xf5,
    0x1, 0xfc, 0xef, 0x0, 0x6b, 0x0, 0x0, 0x0,
    0x3, 0xc2, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xcf, 0x20, 0x3c, 0x30, 0x0, 0x0, 0x6, 0xc1,
    0x3, 0xfc, 0x8f, 0x60, 0x5f, 0xf5, 0x0, 0x0,
    0x8f, 0xf2, 0x8, 0xf8, 0x3f, 0xd0, 0x7, 0xff,
    0xeb, 0xbf, 0xff, 0xf0, 0x1f, 0xf2, 0xb, 0xf8,
    0x0, 0x3b, 0xff, 0xff, 0xa5, 0xf0, 0xaf, 0xa0,
    0x1, 0xef, 0x60, 0x0, 0x2, 0x2e, 0x54, 0xf0,
    0xbf, 0x10, 0x0, 0x4f, 0xf8, 0x0, 0x0, 0xe,
    0x54, 0xf0, 0xb4, 0x0, 0x0, 0x3, 0xef, 0xe9,
    0x42, 0xe, 0x44, 0xf0, 0x10, 0x0, 0x0, 0x0,
    0x1a, 0xff, 0xfd, 0x2, 0x4, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x99, 0x0, 0x1, 0x90,
    0x0, 0x0,

    /* U+E375 "" */
    0x0, 0x0, 0x0, 0x15, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xfa,
    0x53, 0x35, 0x9f, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xfe, 0x71, 0x0, 0x0, 0x17, 0xef, 0xf4, 0x0,
    0x2, 0xff, 0xff, 0xe5, 0x0, 0x0, 0x5e, 0xff,
    0xff, 0x20, 0xb, 0xff, 0xb3, 0x0, 0x0, 0x0,
    0x0, 0x2b, 0xff, 0xb0, 0x3f, 0xe1, 0x4b, 0xd9,
    0x0, 0x0, 0x9d, 0xc4, 0x1e, 0xf3, 0x8f, 0x74,
    0xf9, 0x7d, 0xc0, 0xc, 0xd7, 0x9f, 0x47, 0xf8,
    0xcf, 0x2b, 0x93, 0x81, 0xf4, 0x3f, 0x28, 0x49,
    0xb2, 0xfc, 0xef, 0xd, 0x78, 0xf1, 0xf5, 0x5f,
    0x1f, 0x97, 0xd0, 0xfe, 0xfe, 0x9, 0xd2, 0x17,
    0xf1, 0x1f, 0x71, 0x1d, 0x90, 0xef, 0xef, 0x0,
    0xcf, 0xff, 0x50, 0x5, 0xff, 0xfc, 0x0, 0xfe,
    0xcf, 0x20, 0x3, 0x51, 0x0, 0x0, 0x15, 0x30,
    0x2, 0xfc, 0x8f, 0x60, 0x0, 0x0, 0x9e, 0xe9,
    0x0, 0x0, 0x6, 0xf8, 0x3f, 0xd0, 0x0, 0x9,
    0xff, 0xff, 0x90, 0x0, 0xd, 0xf3, 0xb, 0xf8,
    0x0, 0xf, 0xff, 0xff, 0xf0, 0x0, 0x8f, 0xb0,
    0x2, 0xff, 0x60, 0xb, 0xcc, 0xcc, 0xb0, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xf9, 0x10, 0x0, 0x0,
    0x1, 0x9f, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9b, 0xb9, 0x61, 0x0,
    0x0, 0x0,

    /* U+E384 "" */
    0x0, 0x0, 0x0, 0x15, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0xb, 0xf9, 0x0, 0x42, 0x0, 0x0,
    0x24, 0x0, 0x9f, 0xb0, 0x3f, 0xd0, 0x4, 0xf5,
    0x0, 0x0, 0x4f, 0x40, 0xd, 0xf3, 0x8f, 0x60,
    0x7f, 0x80, 0x0, 0x0, 0x8, 0xf7, 0x6, 0xf8,
    0xcf, 0x1b, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x5e,
    0xb1, 0xfc, 0xef, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x10, 0xfe, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xef, 0x0,
    0xea, 0x7e, 0x60, 0x7, 0xe7, 0xae, 0x0, 0xfe,
    0xcf, 0x10, 0x8f, 0xfd, 0x20, 0x2, 0xdf, 0xf8,
    0x1, 0xfc, 0x8f, 0x60, 0x0, 0x10, 0x0, 0x0,
    0x1, 0x0, 0x6, 0xf8, 0x3f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf3, 0xb, 0xf9,
    0x0, 0x9, 0xff, 0xff, 0x90, 0x0, 0x9f, 0xb0,
    0x2, 0xff, 0x70, 0x6, 0xdd, 0xdd, 0x60, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9b, 0xb9, 0x61, 0x0,
    0x0, 0x0,

    /* U+E38D "" */
    0x0, 0x0, 0x0, 0x16, 0x9a, 0xa8, 0x61, 0x0,
    0x6b, 0xba, 0x0, 0x0, 0x1, 0x9f, 0xff, 0xff,
    0xff, 0xf6, 0x4, 0xbf, 0xa0, 0x0, 0x3, 0xef,
    0xe9, 0x53, 0x35, 0x9e, 0x70, 0x2e, 0xd2, 0x0,
    0x3, 0xff, 0x91, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xd0, 0x1, 0xef, 0x60, 0x0, 0x0, 0x0,
    0xef, 0xf8, 0x0, 0x0, 0x0, 0xbf, 0x80, 0x0,
    0x0, 0x0, 0x3, 0xdf, 0x30, 0x0, 0x0, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x81, 0x7,
    0xd1, 0x8, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0x70, 0x8f, 0x60, 0xcf, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfb, 0xe, 0xf0,
    0x2, 0x0, 0x20, 0x0, 0x2, 0x0, 0x20, 0xf,
    0xd0, 0xfe, 0x0, 0xfd, 0xbf, 0x70, 0x8, 0xfb,
    0xdf, 0x0, 0xef, 0xe, 0xf0, 0x4, 0xcd, 0x90,
    0x0, 0x19, 0xdc, 0x40, 0xf, 0xf0, 0xcf, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfd,
    0x8, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0x90, 0x2f, 0xe0, 0x0, 0x0, 0x2e,
    0xe2, 0x0, 0x0, 0xd, 0xf3, 0x0, 0xaf, 0x80,
    0x0, 0x7, 0xff, 0x80, 0x0, 0x7, 0xfc, 0x0,
    0x1, 0xef, 0x60, 0x0, 0x2e, 0xe2, 0x0, 0x5,
    0xfe, 0x20, 0x0, 0x3, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x40, 0x0, 0x0, 0x3, 0xef,
    0xe9, 0x53, 0x35, 0x9e, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x1, 0x9f, 0xff, 0xff, 0xff, 0xfa, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0x9a, 0xa9,
    0x61, 0x0, 0x0, 0x0, 0x0,

    /* U+E392 "" */
    0x0, 0x0, 0x0, 0x15, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0xb, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x3f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf3, 0x8f, 0x60,
    0x1, 0x65, 0x0, 0x0, 0x56, 0x10, 0x6, 0xf8,
    0xcf, 0x10, 0x4f, 0xff, 0xe0, 0x1e, 0xff, 0xf3,
    0x1, 0xfc, 0xef, 0x0, 0x39, 0x24, 0x90, 0x9,
    0x43, 0x92, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xcf, 0x10, 0x1, 0x40, 0x0, 0x0, 0x4, 0x10,
    0x1, 0xfc, 0x8f, 0x60, 0x9, 0xf9, 0x10, 0x1,
    0x9f, 0x90, 0x6, 0xf8, 0x3f, 0xd0, 0x3, 0xef,
    0xfd, 0xdf, 0xfe, 0x30, 0xd, 0xf3, 0xb, 0xf9,
    0x0, 0x18, 0xef, 0xfe, 0x81, 0x0, 0x9f, 0xb0,
    0x2, 0xff, 0x70, 0x0, 0x1, 0x10, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9b, 0xb9, 0x61, 0x0,
    0x0, 0x0,

    /* U+E398 "" */
    0x0, 0x0, 0x0, 0x16, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xf9,
    0x53, 0x35, 0x9f, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xfa, 0x10, 0x0, 0x0, 0x0, 0x9f, 0xf4, 0x0,
    0x2, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x10, 0xb, 0xfe, 0x99, 0x98, 0x30, 0x3,
    0x89, 0x99, 0xef, 0xb0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xf2, 0x2f, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xbf, 0xff, 0xff, 0xf0, 0xf,
    0xff, 0xff, 0xfc, 0xfe, 0xfe, 0x3e, 0xff, 0xff,
    0x60, 0x6, 0xff, 0xff, 0xe3, 0xef, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xcf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xfc, 0x8f, 0x70, 0x4, 0xa1, 0x0, 0x0,
    0x1a, 0x40, 0x7, 0xf8, 0x3f, 0xe0, 0x9, 0xfe,
    0x72, 0x37, 0xef, 0x90, 0xe, 0xf2, 0xb, 0xf8,
    0x0, 0x9f, 0xff, 0xff, 0xf9, 0x0, 0x8f, 0xb0,
    0x2, 0xff, 0x60, 0x2, 0x8b, 0xb8, 0x30, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xf9, 0x10, 0x0, 0x0,
    0x1, 0x9f, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0,

    /* U+E39B "" */
    0x0, 0x0, 0x0, 0x16, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x3, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x30, 0x0, 0x0, 0x3f,
    0xf9, 0x10, 0x0, 0x0, 0x1, 0x9f, 0xf4, 0x0,
    0x1, 0xef, 0x76, 0xa9, 0x30, 0x0, 0x0, 0x6,
    0xfe, 0x10, 0xa, 0xf8, 0x8e, 0xab, 0xf8, 0x0,
    0x0, 0x0, 0x8f, 0xb0, 0x2f, 0xe0, 0x0, 0x21,
    0x39, 0x0, 0x0, 0x0, 0xe, 0xf3, 0x8f, 0x70,
    0x3, 0xff, 0x0, 0x0, 0x68, 0x0, 0x7, 0xf8,
    0xcf, 0x20, 0x3, 0xfe, 0x0, 0x0, 0xff, 0x50,
    0x2, 0xfc, 0xef, 0x0, 0x0, 0x21, 0x0, 0x0,
    0x9c, 0x10, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xef, 0x0,
    0x0, 0x7a, 0x63, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xcf, 0x0, 0x0, 0x4a, 0xdf, 0x91, 0x0, 0x0,
    0x2, 0xfc, 0x87, 0x17, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x7, 0xf8, 0x34, 0x7f, 0x50, 0x0,
    0x39, 0xff, 0x10, 0x0, 0xe, 0xf3, 0x0, 0x8f,
    0x62, 0x8d, 0xff, 0xf9, 0x0, 0x0, 0x8f, 0xb0,
    0x0, 0x8f, 0xef, 0xff, 0xf6, 0x0, 0x0, 0x6,
    0xfe, 0x10, 0x0, 0x8f, 0xff, 0xff, 0xb0, 0x0,
    0x1, 0x9f, 0xf4, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0x51, 0x35, 0x9e, 0xfe, 0x30, 0x0, 0x0, 0x4f,
    0xff, 0xfe, 0xb, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x6, 0xbb, 0xa3, 0xa, 0xa9, 0x61, 0x0,
    0x0, 0x0,

    /* U+E3A4 "" */
    0x0, 0x0, 0x0, 0x0, 0x26, 0x77, 0x52, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xff,
    0xff, 0xff, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2c, 0xff, 0xc8, 0x55, 0x7b, 0xff, 0xc2, 0x0,
    0x0, 0x0, 0x3, 0xef, 0xb2, 0x0, 0x0, 0x45,
    0x3b, 0xfe, 0x20, 0x0, 0x0, 0x1e, 0xf8, 0x0,
    0x0, 0xc, 0xff, 0xf3, 0x8f, 0xe1, 0x0, 0x0,
    0xaf, 0x90, 0x0, 0x0, 0x6f, 0xf5, 0xac, 0xa,
    0xfa, 0x0, 0x2, 0xfe, 0x0, 0x31, 0x0, 0x9c,
    0xa2, 0x5f, 0x0, 0xef, 0x20, 0x8, 0xf7, 0x2e,
    0xff, 0x50, 0x5f, 0x31, 0xbb, 0x0, 0x7f, 0x80,
    0xc, 0xf2, 0xba, 0x47, 0xf0, 0xa, 0xff, 0xd2,
    0x20, 0x2f, 0xc0, 0xe, 0xf0, 0xd7, 0xb5, 0xf1,
    0x0, 0x23, 0x2a, 0xf8, 0xf, 0xe0, 0xf, 0xe0,
    0x7e, 0x8d, 0xb0, 0x0, 0x8, 0xff, 0xfa, 0xe,
    0xf0, 0xf, 0xf0, 0x6, 0xb8, 0x0, 0x5, 0xef,
    0xff, 0xf9, 0xf, 0xe0, 0xc, 0xf3, 0x0, 0x0,
    0x3, 0xcf, 0xff, 0xff, 0xf7, 0x2f, 0xb0, 0x8,
    0xf8, 0x0, 0x1, 0x9f, 0xff, 0xff, 0xff, 0xf1,
    0x8f, 0x70, 0x2, 0xfe, 0x10, 0x3f, 0xfa, 0x5f,
    0x87, 0xff, 0x71, 0xef, 0x10, 0x0, 0x9f, 0xa0,
    0x3f, 0xf2, 0x3, 0x0, 0xea, 0xb, 0xf8, 0x0,
    0x0, 0xd, 0xf9, 0x3, 0xf2, 0x0, 0x0, 0xe6,
    0xaf, 0xd0, 0x0, 0x0, 0x2, 0xef, 0xc4, 0xf5,
    0x0, 0x1, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x1b, 0xff, 0xfe, 0x30, 0x1b, 0xff, 0xb1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4b, 0xff, 0xfe, 0xff,
    0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x14, 0x56, 0x40, 0x0, 0x0, 0x0, 0x0,

    /* U+E409 "" */
    0x0, 0x0, 0x0, 0x15, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0xb, 0xf9, 0x4, 0x41, 0x0, 0x0,
    0x45, 0x10, 0x9f, 0xb0, 0x3f, 0xd2, 0xef, 0xfe,
    0x30, 0x2d, 0xfe, 0xf5, 0xd, 0xf3, 0x8f, 0x6b,
    0xb0, 0xcf, 0xc0, 0xbb, 0x7, 0xff, 0x6, 0xf8,
    0xcf, 0x1d, 0x60, 0x28, 0xf0, 0xf4, 0x1, 0x5f,
    0x31, 0xfc, 0xef, 0xb, 0xff, 0xff, 0xd0, 0xef,
    0xff, 0xff, 0x20, 0xfe, 0xfe, 0x0, 0x44, 0x44,
    0x10, 0x14, 0x44, 0x42, 0x0, 0xef, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xcf, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1, 0xfc, 0x8f, 0x60, 0xcf, 0xff, 0xff, 0xff,
    0xfe, 0xfd, 0x6, 0xf8, 0x3f, 0xd0, 0x4f, 0xff,
    0xff, 0xf5, 0x4, 0xf4, 0xd, 0xf3, 0xb, 0xf9,
    0x7, 0xff, 0xff, 0x62, 0x8f, 0x70, 0x9f, 0xb0,
    0x2, 0xff, 0x70, 0x4d, 0xff, 0xff, 0xd4, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xfa, 0x0, 0x25, 0x52,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9b, 0xb9, 0x61, 0x0,
    0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x73, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0xdf, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x9e, 0xff, 0xfc, 0xff, 0x0, 0x0,
    0x0, 0x1, 0x5a, 0xff, 0xff, 0xb6, 0x10, 0xdf,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfa, 0x51, 0x0,
    0x0, 0xdf, 0x0, 0x0, 0x2, 0xff, 0xa5, 0x0,
    0x0, 0x0, 0x16, 0xff, 0x0, 0x0, 0x2, 0xfb,
    0x0, 0x0, 0x2, 0x7c, 0xff, 0xff, 0x0, 0x0,
    0x2, 0xfb, 0x0, 0x28, 0xdf, 0xff, 0xe9, 0xef,
    0x0, 0x0, 0x2, 0xfd, 0x8d, 0xff, 0xfd, 0x83,
    0x0, 0xdf, 0x0, 0x0, 0x2, 0xff, 0xff, 0xc7,
    0x20, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x2, 0xfe,
    0x61, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0,
    0x2, 0xfb, 0x0, 0x0, 0x0, 0x2, 0x31, 0xdf,
    0x0, 0x0, 0x2, 0xfb, 0x0, 0x0, 0x4, 0xef,
    0xff, 0xff, 0x0, 0x0, 0x2, 0xfb, 0x0, 0x0,
    0x3f, 0xfb, 0xae, 0xff, 0x1, 0x8b, 0xb9, 0xfb,
    0x0, 0x0, 0x9f, 0x60, 0x1, 0xff, 0x3f, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x8f, 0xa0, 0x4, 0xfe,
    0xcf, 0x92, 0x3c, 0xfb, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xf8, 0xff, 0x0, 0x3, 0xfb, 0x0, 0x0,
    0x3, 0xcf, 0xfe, 0x70, 0xcf, 0x93, 0x4c, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x3e, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x7a, 0x96, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F007 "" */
    0x0, 0x0, 0x0, 0x1, 0x32, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xbf, 0xff, 0xe6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xfa, 0x68, 0xef,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x60, 0x0,
    0x1d, 0xf3, 0x0, 0x0, 0x0, 0x1, 0xfd, 0x0,
    0x0, 0x5, 0xf9, 0x0, 0x0, 0x0, 0x3, 0xfa,
    0x0, 0x0, 0x2, 0xfb, 0x0, 0x0, 0x0, 0x2,
    0xfd, 0x0, 0x0, 0x5, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x60, 0x0, 0x1d, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xfb, 0x68, 0xef, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xfd, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x28, 0xaa, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xcd,
    0xdd, 0xdb, 0x82, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x2, 0xff,
    0xc4, 0x10, 0x0, 0x2, 0x7f, 0xf9, 0x0, 0xd,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x50,
    0x5f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xd0, 0xbf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf3, 0xef, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x29, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x6b, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xa1,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x90,
    0x0, 0x8, 0x80, 0x0, 0x0, 0x0, 0x2, 0xef,
    0x90, 0x0, 0x0, 0xdf, 0x90, 0x0, 0x0, 0x2,
    0xef, 0x90, 0x0, 0x0, 0x2, 0xef, 0x90, 0x0,
    0x2, 0xef, 0x90, 0x0, 0x0, 0x0, 0x2, 0xef,
    0x90, 0x2, 0xef, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0x92, 0xef, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x30, 0x0, 0x0, 0x0, 0x3f, 0xa0, 0x6,
    0xff, 0x30, 0x0, 0x0, 0x3f, 0xf6, 0x0, 0x7,
    0xff, 0x30, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x8,
    0xfe, 0x20, 0x2e, 0xf8, 0x0, 0x0, 0x0, 0x8,
    0xfe, 0x5e, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xfe,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x1d, 0xfa, 0xa,
    0xfd, 0x10, 0x0, 0x0, 0x1d, 0xfb, 0x0, 0xa,
    0xfd, 0x10, 0x0, 0x1d, 0xfb, 0x0, 0x0, 0xb,
    0xfd, 0x10, 0xa, 0xfb, 0x0, 0x0, 0x0, 0xb,
    0xfa, 0x0, 0x59, 0x0, 0x0, 0x0, 0x0, 0x9,
    0x50,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xe6, 0x0, 0xe, 0xe0, 0x1, 0xe8, 0x0, 0x0,
    0x0, 0x4f, 0xf5, 0x0, 0xe, 0xe0, 0x1, 0xef,
    0x90, 0x0, 0x1, 0xef, 0x50, 0x0, 0xe, 0xe0,
    0x0, 0x1d, 0xf6, 0x0, 0x9, 0xf8, 0x0, 0x0,
    0xe, 0xe0, 0x0, 0x3, 0xff, 0x10, 0xf, 0xe0,
    0x0, 0x0, 0xe, 0xe0, 0x0, 0x0, 0x9f, 0x60,
    0x5f, 0x80, 0x0, 0x0, 0xe, 0xe0, 0x0, 0x0,
    0x3f, 0xc0, 0x8f, 0x50, 0x0, 0x0, 0xe, 0xe0,
    0x0, 0x0, 0xf, 0xe0, 0xaf, 0x30, 0x0, 0x0,
    0xe, 0xe0, 0x0, 0x0, 0xd, 0xf0, 0xaf, 0x40,
    0x0, 0x0, 0x7, 0x70, 0x0, 0x0, 0xe, 0xf0,
    0x7f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xd0, 0x3f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0x90, 0xd, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x30, 0x5, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfb, 0x0,
    0x0, 0x9f, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xe1, 0x0, 0x0, 0xa, 0xff, 0x72, 0x0, 0x0,
    0x5d, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xec, 0xdf, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x8d, 0xff, 0xfe, 0xa4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F012 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x85, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xc0, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x1f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xc0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0x80, 0x1, 0xfc, 0x0, 0x1f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x1f,
    0xc0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xc0, 0x1, 0xfc, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x1, 0x0, 0x1, 0xfc, 0x0, 0x1f, 0xc0,
    0x1, 0xfc, 0x0, 0x0, 0x0, 0xfb, 0x0, 0x1f,
    0xc0, 0x1, 0xfc, 0x0, 0x1f, 0xc0, 0x0, 0x0,
    0x1f, 0xc0, 0x1, 0xfc, 0x0, 0x1f, 0xc0, 0x1,
    0xfc, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x1f, 0xc0,
    0x1, 0xfc, 0x0, 0x1f, 0xc0, 0x52, 0x0, 0x1f,
    0xc0, 0x1, 0xfc, 0x0, 0x1f, 0xc0, 0x1, 0xfc,
    0x2f, 0x90, 0x1, 0xfc, 0x0, 0x1f, 0xc0, 0x1,
    0xfc, 0x0, 0x1f, 0xc3, 0xfa, 0x0, 0x1f, 0xc0,
    0x1, 0xfc, 0x0, 0x1f, 0xc0, 0x1, 0xfc, 0x3f,
    0xa0, 0x1, 0xfc, 0x0, 0x1f, 0xc0, 0x1, 0xfc,
    0x0, 0x1f, 0xc2, 0xf9, 0x0, 0xf, 0xc0, 0x0,
    0xfc, 0x0, 0xf, 0xc0, 0x0, 0xfc, 0x5, 0x20,
    0x0, 0x52, 0x0, 0x5, 0x20, 0x0, 0x52, 0x0,
    0x5, 0x20,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x56, 0x75, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xe9, 0x9b, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x6,
    0x62, 0x7f, 0x80, 0x1, 0xfd, 0x14, 0x72, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0x20, 0x0, 0xbf, 0xff,
    0xfe, 0x10, 0x5, 0xfe, 0x8c, 0xb4, 0x0, 0x0,
    0x1a, 0xda, 0xaf, 0xb0, 0xd, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf3, 0x3f, 0xc0,
    0x0, 0x1, 0xaf, 0xfd, 0x40, 0x0, 0x5, 0xf9,
    0x4f, 0xe2, 0x0, 0xc, 0xfd, 0xbf, 0xf3, 0x0,
    0xa, 0xfa, 0x8, 0xfe, 0x0, 0x5f, 0xb0, 0x5,
    0xfb, 0x0, 0x8f, 0xd1, 0x0, 0xbf, 0x30, 0x8f,
    0x60, 0x0, 0xfe, 0x0, 0xcf, 0x20, 0x4, 0xff,
    0x10, 0x6f, 0xb0, 0x5, 0xfd, 0x0, 0xaf, 0x90,
    0x2f, 0xf7, 0x0, 0x1e, 0xfd, 0xbf, 0xf6, 0x0,
    0x1e, 0xf7, 0x5f, 0xb0, 0x0, 0x4, 0xef, 0xff,
    0x90, 0x0, 0x4, 0xfb, 0xf, 0xf1, 0x0, 0x0,
    0x5, 0x62, 0x0, 0x0, 0xa, 0xf5, 0x8, 0xfb,
    0x37, 0x61, 0x0, 0x0, 0x4, 0x75, 0x5f, 0xe0,
    0x0, 0xdf, 0xff, 0xfd, 0x10, 0x0, 0x8f, 0xff,
    0xff, 0x40, 0x0, 0x2b, 0xb7, 0xbf, 0x70, 0x0,
    0xfe, 0x6a, 0xc6, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xc4, 0x37, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x57, 0x76, 0x30, 0x0,
    0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xd2, 0x2d, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xfc, 0x10, 0x1, 0xcf,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xa0, 0x0, 0x0, 0xa, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x0, 0x8, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x90, 0x0,
    0x0, 0xbf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xfc, 0x10, 0xc, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xd1,
    0x1f, 0xbc, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xda, 0xf2, 0x0, 0xb, 0xf2, 0x0,
    0x3c, 0xdd, 0xdd, 0xc3, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0xb, 0xf2, 0x0, 0xbf, 0xff, 0xff, 0xfb,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0xb, 0xf2, 0x0,
    0xcf, 0x10, 0x1, 0xfc, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0xb, 0xf2, 0x0, 0xcf, 0x10, 0x1, 0xfc,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0xb, 0xf2, 0x0,
    0xcf, 0x10, 0x1, 0xfc, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0xb, 0xf2, 0x0, 0xcf, 0x10, 0x1, 0xfc,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0xb, 0xf2, 0x0,
    0xcf, 0x10, 0x1, 0xfc, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x8, 0xf9, 0x22, 0xdf, 0x32, 0x23, 0xfd,
    0x22, 0x8f, 0x90, 0x0, 0x0, 0x1, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x18, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0x82, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xee,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xee, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0xee, 0x0,
    0x0, 0x20, 0x0, 0x0, 0x0, 0x2, 0xfc, 0x0,
    0xe, 0xe0, 0x0, 0xcf, 0x20, 0x0, 0x0, 0x0,
    0xc, 0xfc, 0x0, 0xee, 0x0, 0xcf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfc, 0xe, 0xe0, 0xcf,
    0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfc,
    0xff, 0xcf, 0xc1, 0x0, 0x0, 0x0, 0x8, 0xdd,
    0xd6, 0xc, 0xff, 0xff, 0xc1, 0x5d, 0xdd, 0x90,
    0x9, 0xff, 0xff, 0xf6, 0xc, 0xff, 0xc1, 0x6f,
    0xff, 0xff, 0xa0, 0xef, 0x10, 0x0, 0x0, 0xb,
    0xb1, 0x0, 0x0, 0x1, 0xef, 0xf, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x54, 0xd, 0xf0,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xc0, 0xdf, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x33, 0xd, 0xf0, 0xef, 0x32, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x23, 0xff, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x6, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0x70, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xef,
    0xcf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf8, 0x2f, 0xb0, 0x0, 0x0, 0x3, 0x55, 0x58,
    0xff, 0x60, 0x2f, 0xb0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xf4, 0x0, 0x2f, 0xb0, 0x8, 0x30, 0xef,
    0x88, 0x88, 0x20, 0x0, 0x2f, 0xb0, 0x2f, 0xf1,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x9,
    0xf7, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0,
    0x4, 0xfa, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xb0, 0x9, 0xf7, 0xef, 0xcb, 0xbb, 0x50, 0x0,
    0x2f, 0xb0, 0x2f, 0xf1, 0x5f, 0xff, 0xff, 0xf8,
    0x0, 0x2f, 0xb0, 0x8, 0x30, 0x0, 0x22, 0x25,
    0xff, 0xa0, 0x2f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xfb, 0x3f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xef, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x0, 0x0,
    0x0, 0x2, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xfa, 0x0, 0x0, 0x0, 0x9f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xb0,
    0x0, 0x0, 0x2, 0xef, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x4e, 0xfc, 0xfb, 0x0, 0x0, 0x54, 0x2,
    0xef, 0x30, 0x0, 0x0, 0x0, 0x5f, 0xf8, 0x2f,
    0xb0, 0x0, 0xf, 0xf4, 0x6, 0xfc, 0x0, 0x35,
    0x55, 0x8f, 0xf6, 0x2, 0xfb, 0x0, 0x0, 0x5f,
    0xf1, 0xc, 0xf4, 0x7f, 0xff, 0xff, 0xf4, 0x0,
    0x2f, 0xb0, 0x8, 0x30, 0x8f, 0x90, 0x6f, 0x9e,
    0xf8, 0x88, 0x82, 0x0, 0x2, 0xfb, 0x2, 0xff,
    0x11, 0xfe, 0x1, 0xfc, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xb0, 0x9, 0xf7, 0xc, 0xf1, 0xf,
    0xef, 0xd0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0x0,
    0x4f, 0xa0, 0xaf, 0x30, 0xef, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xb0, 0x9, 0xf7, 0xc, 0xf1,
    0xf, 0xee, 0xfc, 0xbb, 0xb5, 0x0, 0x2, 0xfb,
    0x2, 0xff, 0x11, 0xfe, 0x1, 0xfc, 0x5f, 0xff,
    0xff, 0xf8, 0x0, 0x2f, 0xb0, 0x8, 0x30, 0x8f,
    0x90, 0x6f, 0x90, 0x2, 0x22, 0x5f, 0xfa, 0x2,
    0xfb, 0x0, 0x0, 0x5f, 0xf1, 0xc, 0xf4, 0x0,
    0x0, 0x0, 0x3e, 0xfb, 0x3f, 0xb0, 0x0, 0xf,
    0xf4, 0x6, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xfe, 0xfb, 0x0, 0x0, 0x63, 0x2, 0xef, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xb0, 0x0,
    0x0, 0x2, 0xef, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xf8, 0x0, 0x0, 0x0, 0x9f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x2, 0x50, 0x0, 0x0,

    /* U+F03E "" */
    0x0, 0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x10, 0x0, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc2, 0xcf, 0xdc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcd, 0xfc, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xfd, 0x0, 0x38, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfd, 0x1, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xfd, 0x2, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0,
    0xaf, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xfd, 0x0, 0x1, 0x0, 0x0, 0x1c, 0xa0, 0x0,
    0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf7, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x4, 0x30,
    0x7, 0xff, 0xff, 0x40, 0x0, 0xdf, 0xfd, 0x0,
    0x3f, 0xf2, 0x4f, 0xff, 0xff, 0xe1, 0x0, 0xdf,
    0xfd, 0x0, 0xdf, 0xfc, 0xef, 0xff, 0xff, 0xfc,
    0x0, 0xdf, 0xfd, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0xdf, 0xfd, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0xdf, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F044 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3d, 0xfe, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xad, 0xf4, 0x4, 0xcf,
    0xff, 0xff, 0x90, 0x0, 0x3f, 0xf6, 0x1, 0xeb,
    0x4f, 0xfe, 0xdd, 0xdd, 0x70, 0x3, 0xff, 0xf8,
    0x1, 0xfc, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x3f,
    0xf8, 0xef, 0x9d, 0xf6, 0xfe, 0x0, 0x0, 0x0,
    0x3, 0xef, 0x60, 0x3e, 0xff, 0xa0, 0xfd, 0x0,
    0x0, 0x0, 0x3e, 0xf6, 0x0, 0x1d, 0xfa, 0x0,
    0xfd, 0x0, 0x0, 0x3, 0xef, 0x60, 0x1, 0xdf,
    0xa0, 0x0, 0xfd, 0x0, 0x0, 0x2e, 0xf6, 0x0,
    0x1d, 0xfb, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x9f,
    0x60, 0x1, 0xdf, 0xb0, 0x0, 0x0, 0xfd, 0x0,
    0x0, 0xee, 0x0, 0x1d, 0xfb, 0x0, 0x0, 0x0,
    0xfd, 0x0, 0x3, 0xfa, 0x4, 0xdf, 0xb0, 0x0,
    0xf9, 0x0, 0xfd, 0x0, 0x7, 0xfd, 0xff, 0xfb,
    0x0, 0x2, 0xfb, 0x0, 0xfd, 0x0, 0xb, 0xff,
    0xfb, 0x60, 0x0, 0x2, 0xfb, 0x0, 0xfd, 0x0,
    0x5, 0x84, 0x0, 0x0, 0x0, 0x2, 0xfb, 0x0,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xfb, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xfa, 0x0, 0xbf, 0x82, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x3c, 0xf6, 0x0, 0x2e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x1, 0x8b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa6,
    0x0, 0x0,

    /* U+F048 "" */
    0x8c, 0x0, 0x0, 0x0, 0x7e, 0x8c, 0xf1, 0x0,
    0x1, 0xcf, 0xfd, 0xcf, 0x10, 0x5, 0xff, 0xdf,
    0xdc, 0xf1, 0x1a, 0xff, 0x81, 0xfd, 0xcf, 0x5e,
    0xfe, 0x40, 0xf, 0xdc, 0xff, 0xfa, 0x10, 0x0,
    0xfd, 0xcf, 0xe5, 0x0, 0x0, 0xf, 0xdc, 0xf3,
    0x0, 0x0, 0x0, 0xfd, 0xcf, 0xe5, 0x0, 0x0,
    0xf, 0xdc, 0xff, 0xfa, 0x0, 0x0, 0xfd, 0xcf,
    0x5e, 0xfd, 0x30, 0xf, 0xdc, 0xf1, 0x1a, 0xff,
    0x81, 0xfd, 0xcf, 0x10, 0x5, 0xef, 0xdf, 0xdc,
    0xf1, 0x0, 0x1, 0xbf, 0xfd, 0x8c, 0x0, 0x0,
    0x0, 0x7e, 0x80,

    /* U+F04B "" */
    0x2, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xef, 0xe5, 0x0, 0x0, 0x0, 0x0, 0xf, 0xe1,
    0x9f, 0xfc, 0x20, 0x0, 0x0, 0x0, 0xfe, 0x0,
    0x3d, 0xff, 0x80, 0x0, 0x0, 0xf, 0xe0, 0x0,
    0x7, 0xff, 0xe5, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0x1, 0xaf, 0xfb, 0x20, 0xf, 0xe0, 0x0, 0x0,
    0x0, 0x4d, 0xff, 0x70, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x9f, 0xe0, 0x0, 0x0, 0x0, 0x4d,
    0xff, 0x70, 0xfe, 0x0, 0x0, 0x1, 0xaf, 0xfb,
    0x10, 0xf, 0xe0, 0x0, 0x7, 0xff, 0xe4, 0x0,
    0x0, 0xfe, 0x0, 0x3d, 0xff, 0x80, 0x0, 0x0,
    0xf, 0xe1, 0x9f, 0xfc, 0x20, 0x0, 0x0, 0x0,
    0xff, 0xef, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x5e, 0xff, 0xd3, 0x6, 0xef, 0xfc, 0x2e, 0xfe,
    0xef, 0xb0, 0xff, 0xee, 0xfa, 0xfe, 0x1, 0xfc,
    0x1f, 0xc0, 0x2f, 0xbf, 0xe0, 0x1f, 0xc1, 0xfc,
    0x2, 0xfb, 0xfe, 0x1, 0xfc, 0x1f, 0xc0, 0x2f,
    0xbf, 0xe0, 0x1f, 0xc1, 0xfc, 0x2, 0xfb, 0xfe,
    0x1, 0xfc, 0x1f, 0xc0, 0x2f, 0xbf, 0xe0, 0x1f,
    0xc1, 0xfc, 0x2, 0xfb, 0xfe, 0x1, 0xfc, 0x1f,
    0xc0, 0x2f, 0xbf, 0xe0, 0x1f, 0xc1, 0xfc, 0x2,
    0xfb, 0xfe, 0x1, 0xfc, 0x1f, 0xc0, 0x2f, 0xbf,
    0xe0, 0x1f, 0xc1, 0xfc, 0x2, 0xfb, 0xfe, 0x1,
    0xfc, 0x1f, 0xc0, 0x2f, 0xbe, 0xfe, 0xef, 0xb0,
    0xff, 0xee, 0xfa, 0x5e, 0xff, 0xd3, 0x6, 0xef,
    0xfc, 0x20,

    /* U+F04D "" */
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x1b,
    0xfe, 0xdd, 0xdd, 0xdd, 0xdd, 0xde, 0xfb, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xfb, 0xfe, 0xdd, 0xdd, 0xdd, 0xdd, 0xde,
    0xfb, 0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x10,

    /* U+F051 "" */
    0xc, 0xd4, 0x0, 0x0, 0x0, 0xd7, 0x2f, 0xff,
    0x90, 0x0, 0x3, 0xfb, 0x2f, 0xef, 0xfd, 0x30,
    0x3, 0xfb, 0x2f, 0xb1, 0xbf, 0xf8, 0x3, 0xfb,
    0x2f, 0xb0, 0x6, 0xff, 0xd6, 0xfb, 0x2f, 0xb0,
    0x0, 0x2c, 0xff, 0xfb, 0x2f, 0xb0, 0x0, 0x0,
    0x7f, 0xfb, 0x2f, 0xb0, 0x0, 0x0, 0x5, 0xfb,
    0x2f, 0xb0, 0x0, 0x0, 0x7f, 0xfb, 0x2f, 0xb0,
    0x0, 0x2c, 0xff, 0xfb, 0x2f, 0xb0, 0x6, 0xff,
    0xd6, 0xfb, 0x2f, 0xb1, 0xbf, 0xf8, 0x3, 0xfb,
    0x2f, 0xee, 0xfd, 0x30, 0x3, 0xfb, 0x2f, 0xff,
    0x90, 0x0, 0x3, 0xfb, 0xc, 0xd4, 0x0, 0x0,
    0x0, 0xd7,

    /* U+F060 "" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfe,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfd, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x92,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x2e, 0xfc, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x20, 0x2, 0xef, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F061 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfe, 0x20, 0x6a,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xff, 0xe2,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x13, 0x33, 0x33, 0x33, 0x33, 0x33, 0x36,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F062 "" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8, 0xfb, 0xdf,
    0xa0, 0x0, 0x0, 0x0, 0x4f, 0xf6, 0x3f, 0xa1,
    0xdf, 0x90, 0x0, 0x0, 0x3f, 0xf6, 0x3, 0xfa,
    0x1, 0xdf, 0x90, 0x0, 0x3e, 0xf7, 0x0, 0x3f,
    0xa0, 0x2, 0xef, 0x80, 0xb, 0xf7, 0x0, 0x3,
    0xfa, 0x0, 0x2, 0xef, 0x20, 0x35, 0x0, 0x0,
    0x3f, 0xa0, 0x0, 0x2, 0x60, 0x0, 0x0, 0x0,
    0x3, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+F063 "" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x36, 0x0, 0x0,
    0x3f, 0xa0, 0x0, 0x2, 0x60, 0xb, 0xf8, 0x0,
    0x3, 0xfa, 0x0, 0x2, 0xef, 0x20, 0x2e, 0xf7,
    0x0, 0x3f, 0xa0, 0x1, 0xef, 0x90, 0x0, 0x3f,
    0xf6, 0x3, 0xfa, 0x1, 0xdf, 0x90, 0x0, 0x0,
    0x3f, 0xf6, 0x3f, 0xa1, 0xdf, 0xa0, 0x0, 0x0,
    0x0, 0x4f, 0xf9, 0xfb, 0xdf, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfe, 0xef, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf4, 0x4f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xa0, 0xa, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x10, 0x1,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf7,
    0xd, 0xd0, 0x6f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xc0, 0xe, 0xe0, 0xc, 0xf6, 0x0, 0x0,
    0x0, 0x1, 0xef, 0x30, 0xe, 0xe0, 0x3, 0xfe,
    0x10, 0x0, 0x0, 0x9, 0xf9, 0x0, 0xe, 0xe0,
    0x0, 0x9f, 0xa0, 0x0, 0x0, 0x3f, 0xe1, 0x0,
    0xe, 0xe0, 0x0, 0xe, 0xf4, 0x0, 0x0, 0xdf,
    0x50, 0x0, 0x2, 0x20, 0x0, 0x5, 0xfd, 0x0,
    0x7, 0xfb, 0x0, 0x0, 0x3, 0x30, 0x0, 0x0,
    0xbf, 0x70, 0x2f, 0xf2, 0x0, 0x0, 0x2f, 0xf2,
    0x0, 0x0, 0x2f, 0xf2, 0xaf, 0x80, 0x0, 0x0,
    0x1e, 0xe1, 0x0, 0x0, 0x7, 0xfb, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xcf, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xfc, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F075 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xae, 0xff,
    0xff, 0xea, 0x40, 0x0, 0x0, 0x0, 0x0, 0x2c,
    0xff, 0xfd, 0xcc, 0xdf, 0xff, 0xc2, 0x0, 0x0,
    0x0, 0x5f, 0xfc, 0x50, 0x0, 0x0, 0x5, 0xcf,
    0xf6, 0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf5, 0x0, 0x1f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf1, 0x8,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0x80, 0xdf, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xfd, 0xf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf0, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xc, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xc0, 0x7f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf7, 0x0, 0xef,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfe,
    0x0, 0x4, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x30, 0x0, 0x5f, 0x91, 0x77, 0x30,
    0x0, 0x3, 0x8e, 0xfe, 0x30, 0x0, 0xb, 0xfb,
    0xef, 0xff, 0xfe, 0xef, 0xff, 0xf9, 0x10, 0x0,
    0x4, 0xff, 0xff, 0x97, 0xbe, 0xff, 0xdb, 0x61,
    0x0, 0x0, 0x0, 0xef, 0xe9, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0AC "" */
    0x0, 0x0, 0x0, 0x0, 0x24, 0x42, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xaf, 0xff, 0xff,
    0xfa, 0x30, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xfc, 0xcf, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x1d,
    0xfe, 0xcf, 0xb0, 0xb, 0xfc, 0xef, 0xd1, 0x0,
    0x0, 0xcf, 0xb1, 0xef, 0x20, 0x2, 0xfd, 0x1b,
    0xfc, 0x0, 0x9, 0xfc, 0x4, 0xfb, 0x0, 0x0,
    0xbf, 0x30, 0xcf, 0x80, 0x2f, 0xf2, 0x8, 0xf6,
    0x0, 0x0, 0x6f, 0x80, 0x2f, 0xf2, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xcf, 0xdd, 0xdf, 0xfd, 0xdd, 0xdd, 0xdf, 0xfd,
    0xdd, 0xfc, 0xef, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0xe, 0xf0, 0x0, 0xfe, 0xfe, 0x0, 0xf, 0xe0,
    0x0, 0x0, 0xe, 0xf0, 0x0, 0xef, 0xef, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0xe, 0xf0, 0x0, 0xfe,
    0xcf, 0xdd, 0xdf, 0xfd, 0xdd, 0xdd, 0xdf, 0xfd,
    0xdd, 0xfc, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x2f, 0xf2, 0x8, 0xf6,
    0x0, 0x0, 0x6f, 0x80, 0x2f, 0xf2, 0x9, 0xfc,
    0x3, 0xfb, 0x0, 0x0, 0xbf, 0x30, 0xcf, 0x80,
    0x0, 0xcf, 0xb1, 0xdf, 0x20, 0x2, 0xfd, 0x1b,
    0xfc, 0x0, 0x0, 0x1d, 0xfe, 0xcf, 0xb0, 0xb,
    0xfc, 0xef, 0xd1, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xfc, 0xcf, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xaf, 0xff, 0xff, 0xfa, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x24, 0x42, 0x0, 0x0,
    0x0, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x29, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xdf,
    0xf7, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfa, 0x42, 0x39, 0xff, 0x50, 0x0, 0x0,
    0x0, 0xd, 0xf7, 0x0, 0x0, 0x4, 0xff, 0x10,
    0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0x0, 0x8,
    0xf7, 0x0, 0x0, 0x0, 0x8f, 0x60, 0x0, 0x0,
    0x0, 0x2f, 0xb0, 0x0, 0x0, 0xa, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0xaf,
    0x30, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0,
    0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0xef, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf2, 0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x70, 0x0, 0xb, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xfe, 0x0, 0x5, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xb, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x37, 0x40, 0x0, 0x0, 0x0, 0x0,

    /* U+F118 "" */
    0x0, 0x0, 0x0, 0x15, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0xb, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x3f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf3, 0x8f, 0x60,
    0x0, 0x86, 0x0, 0x0, 0x68, 0x0, 0x6, 0xf8,
    0xcf, 0x10, 0x4, 0xff, 0x10, 0x0, 0xff, 0x50,
    0x1, 0xfc, 0xef, 0x0, 0x1, 0xca, 0x0, 0x0,
    0x9c, 0x10, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xcf, 0x10, 0x1, 0x40, 0x0, 0x0, 0x4, 0x10,
    0x1, 0xfc, 0x8f, 0x60, 0x9, 0xf9, 0x10, 0x1,
    0x9f, 0x90, 0x6, 0xf8, 0x3f, 0xd0, 0x3, 0xef,
    0xfd, 0xdf, 0xfe, 0x30, 0xd, 0xf3, 0xb, 0xf9,
    0x0, 0x18, 0xef, 0xfe, 0x81, 0x0, 0x9f, 0xb0,
    0x2, 0xff, 0x70, 0x0, 0x1, 0x10, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9b, 0xb9, 0x61, 0x0,
    0x0, 0x0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x2, 0x8e,
    0xff, 0xd7, 0x9f, 0x70, 0x0, 0x0, 0x4, 0xbf,
    0xff, 0xa4, 0x0, 0xef, 0x10, 0x0, 0x17, 0xef,
    0xfe, 0x81, 0x0, 0x5, 0xfa, 0x0, 0x9, 0xff,
    0xfe, 0x72, 0x20, 0x0, 0xc, 0xf4, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x2f, 0xd0, 0x0,
    0x7, 0xbb, 0xbb, 0xbb, 0xbf, 0xb0, 0x9f, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc6,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xcd, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xc1, 0x0, 0x0,
    0x0,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x27, 0xbe, 0xff, 0xff, 0xfe, 0xb7, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xcf, 0xff,
    0xfd, 0xcb, 0xcd, 0xff, 0xff, 0xc4, 0x0, 0x0,
    0x0, 0x0, 0x2c, 0xff, 0xd8, 0x30, 0x0, 0x0,
    0x0, 0x38, 0xdf, 0xfc, 0x30, 0x0, 0x0, 0x6f,
    0xfd, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5d, 0xff, 0x60, 0x0, 0xaf, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xa0, 0xd, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xdd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xbf, 0xff, 0xff, 0xb6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7e, 0xff, 0xfd,
    0xbd, 0xff, 0xfe, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xbf, 0xf9, 0x30, 0x0, 0x0, 0x39,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xc2, 0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x62, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F1F8 "" */
    0x0, 0x0, 0x2, 0xab, 0xbb, 0xb7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xa2, 0x22, 0x3f,
    0xf1, 0x0, 0x0, 0x2, 0x23, 0xff, 0x42, 0x22,
    0x2a, 0xfa, 0x22, 0x10, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x7e, 0xfc, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xff, 0xb2, 0x8, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x7,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x0,
    0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0x0, 0x5, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xfd, 0x0, 0x3, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfb, 0x0, 0x2, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xfa, 0x0, 0x1, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xf9, 0x0, 0x0, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xf8, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf5, 0x0,
    0x0, 0xcf, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf4,
    0x0, 0x0, 0xbf, 0x20, 0x0, 0x0, 0x0, 0x9,
    0xf3, 0x0, 0x0, 0x9f, 0x62, 0x22, 0x22, 0x22,
    0x2d, 0xf1, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x4, 0xab, 0xbb,
    0xbb, 0xbb, 0xb8, 0x10, 0x0,

    /* U+F240 "" */
    0x5, 0xbd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xb6, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xdf,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x10, 0xfd, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xc,
    0xfe, 0x2f, 0xd0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0xcf, 0xf5, 0xfd, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xc, 0xff,
    0x5f, 0xd0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0xcf, 0xf5, 0xfd, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xc, 0xfe, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x10, 0xdf, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x4, 0xbd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xb6, 0x0, 0x0,

    /* U+F241 "" */
    0x5, 0xbd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xb6, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xdf,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x10, 0xfd, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0xc,
    0xfe, 0x2f, 0xd0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0xcf, 0xf5, 0xfd, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0xc, 0xff,
    0x5f, 0xd0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0xcf, 0xf5, 0xfd, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0xc, 0xfe, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x10, 0xdf, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x4, 0xbd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xb6, 0x0, 0x0,

    /* U+F242 "" */
    0x5, 0xbd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xb6, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xdf,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x10, 0xfd, 0x1,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xc,
    0xfe, 0x2f, 0xd0, 0x1f, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0xcf, 0xf5, 0xfd, 0x1, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x5f, 0xd0, 0x1f, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0xcf, 0xf5, 0xfd, 0x1, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0xc, 0xfe, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x10, 0xdf, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x4, 0xbd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xb6, 0x0, 0x0,

    /* U+F243 "" */
    0x5, 0xbd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xb6, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xdf,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x10, 0xfd, 0x1,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xfe, 0x2f, 0xd0, 0x1f, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf5, 0xfd, 0x1, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x5f, 0xd0, 0x1f, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf5, 0xfd, 0x1, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfe, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x10, 0xdf, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x4, 0xbd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xb6, 0x0, 0x0,

    /* U+F244 "" */
    0x5, 0xbd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xb6, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xdf,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x10, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xfe, 0x2f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf5, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x5f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf5, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfe, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x10, 0xdf, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x4, 0xbd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xb6, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x62, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xef,
    0xf7, 0x0, 0x0, 0x0, 0x3, 0xfa, 0x4f, 0xf9,
    0x3, 0xc3, 0x0, 0x3f, 0xa0, 0x2e, 0xfa, 0x5f,
    0xf6, 0x3, 0xfa, 0x1, 0xcf, 0xd0, 0x6f, 0xf8,
    0x3f, 0xa3, 0xef, 0xc1, 0x0, 0x4e, 0xfd, 0xfe,
    0xff, 0x90, 0x0, 0x0, 0x2d, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x2d, 0xff, 0xff, 0x60, 0x0, 0x0, 0x4e,
    0xfd, 0xfd, 0xff, 0x90, 0x0, 0x6f, 0xf8, 0x3f,
    0xa3, 0xef, 0xb1, 0x5f, 0xf5, 0x3, 0xfa, 0x1,
    0xcf, 0xc3, 0xb3, 0x0, 0x3f, 0xa0, 0x2e, 0xfa,
    0x0, 0x0, 0x3, 0xfa, 0x4f, 0xf9, 0x0, 0x0,
    0x0, 0x3f, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x62, 0x0, 0x0,
    0x0,

    /* U+F376 "" */
    0x5, 0xbd, 0xdd, 0xdd, 0xdd, 0xc2, 0x0, 0x4d,
    0xdd, 0xb6, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xd2, 0x1b, 0x82, 0xff, 0xff, 0xf7, 0x0, 0xdf,
    0x40, 0x0, 0x0, 0x0, 0x3e, 0xf4, 0x0, 0x0,
    0x3f, 0xf0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x5f,
    0xfc, 0x0, 0x0, 0x0, 0xcf, 0x10, 0xfd, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0x40, 0x0, 0x0, 0xc,
    0xfe, 0x2f, 0xd0, 0x0, 0x1, 0xbf, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0xcf, 0xf5, 0xfd, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xc0, 0x0, 0xc, 0xff,
    0x5f, 0xd0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xd2,
    0x0, 0x0, 0xcf, 0xf5, 0xfd, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xa0, 0x0, 0x0, 0xc, 0xfe, 0x2f,
    0xe0, 0x0, 0x0, 0xa, 0xff, 0x70, 0x0, 0x0,
    0x0, 0xcf, 0x10, 0xdf, 0x40, 0x0, 0x2, 0xfe,
    0x40, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x5, 0xff,
    0xff, 0xf3, 0x7d, 0x21, 0xcf, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x4, 0xbd, 0xdd, 0x50, 0x1, 0xbd,
    0xdd, 0xdd, 0xdd, 0xb6, 0x0, 0x0,

    /* U+F377 "" */
    0x6, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xc9, 0x20, 0x0, 0x0, 0x2, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0xac, 0x0, 0x2d, 0xfe, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xf8, 0x0, 0x0, 0xc,
    0xf1, 0x0, 0x1b, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xa0, 0x0, 0x0, 0xcf, 0x10, 0x0,
    0x7, 0xff, 0x80, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x90, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x4, 0xef,
    0xb1, 0x0, 0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0,
    0xcf, 0x10, 0x0, 0x0, 0x2, 0xdf, 0xe3, 0x0,
    0x0, 0x3, 0xff, 0xe0, 0x0, 0xc, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf6, 0x0, 0x0, 0x3f,
    0xfe, 0x0, 0x0, 0xcf, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf9, 0x0, 0x3, 0xff, 0x90, 0x0,
    0xc, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xfc, 0x10, 0x3f, 0xa0, 0x0, 0x0, 0x9f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfe, 0x4a,
    0xf8, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x8, 0xff, 0xfe, 0x10, 0x0,
    0x0, 0x2, 0xad, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0x50, 0x5, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x47, 0x0,

    /* U+F3C5 "" */
    0x0, 0x0, 0x16, 0x9b, 0x96, 0x10, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0xbf, 0xf9, 0x42, 0x48, 0xff, 0xb0, 0x0, 0xaf,
    0xc1, 0x0, 0x0, 0x1, 0xcf, 0xa0, 0x3f, 0xe1,
    0x0, 0x1, 0x0, 0x1, 0xef, 0x39, 0xf6, 0x0,
    0x5e, 0xfe, 0x50, 0x6, 0xf9, 0xdf, 0x10, 0x3f,
    0xe9, 0xef, 0x30, 0x1f, 0xdf, 0xe0, 0x9, 0xf5,
    0x5, 0xf9, 0x0, 0xef, 0xef, 0x0, 0x9f, 0x70,
    0x7f, 0x90, 0xf, 0xeb, 0xf4, 0x3, 0xff, 0xef,
    0xf3, 0x4, 0xfb, 0x5f, 0xb0, 0x5, 0xef, 0xe5,
    0x0, 0xaf, 0x50, 0xef, 0x20, 0x0, 0x10, 0x0,
    0x2f, 0xe0, 0x6, 0xfb, 0x0, 0x0, 0x0, 0xb,
    0xf6, 0x0, 0xd, 0xf5, 0x0, 0x0, 0x5, 0xfd,
    0x0, 0x0, 0x4f, 0xe1, 0x0, 0x1, 0xef, 0x40,
    0x0, 0x0, 0x9f, 0xa0, 0x0, 0xaf, 0x90, 0x0,
    0x0, 0x0, 0xef, 0x50, 0x5f, 0xe0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x4f, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x0, 0x0, 0x0, 0x0,

    /* U+F4DA "" */
    0x0, 0x0, 0x0, 0x15, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0xb, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x3f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf3, 0x8f, 0x60,
    0x0, 0x86, 0x0, 0x0, 0x56, 0x10, 0x6, 0xf8,
    0xcf, 0x10, 0x4, 0xff, 0x10, 0x1e, 0xff, 0xf3,
    0x1, 0xfc, 0xef, 0x0, 0x1, 0xca, 0x0, 0x9,
    0x43, 0x92, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xcf, 0x10, 0x1, 0x40, 0x0, 0x0, 0x4, 0x10,
    0x1, 0xfc, 0x8f, 0x60, 0x9, 0xf9, 0x10, 0x1,
    0x9f, 0x90, 0x6, 0xf8, 0x3f, 0xd0, 0x3, 0xef,
    0xfd, 0xdf, 0xfe, 0x30, 0xd, 0xf3, 0xb, 0xf9,
    0x0, 0x18, 0xef, 0xfe, 0x81, 0x0, 0x9f, 0xb0,
    0x2, 0xff, 0x70, 0x0, 0x1, 0x10, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9b, 0xb9, 0x61, 0x0,
    0x0, 0x0,

    /* U+F556 "" */
    0x0, 0x0, 0x0, 0x16, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x43, 0x34, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf4, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x10, 0xb, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x3f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf2, 0x8f, 0x60,
    0x6b, 0x61, 0x0, 0x0, 0x15, 0xb6, 0x7, 0xf8,
    0xcf, 0x20, 0x29, 0xff, 0xb2, 0x1b, 0xff, 0x92,
    0x2, 0xfc, 0xff, 0x0, 0x4, 0xff, 0x92, 0x29,
    0xff, 0x50, 0x0, 0xfe, 0xfe, 0x0, 0x1, 0xca,
    0x0, 0x0, 0xac, 0x10, 0x0, 0xef, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xcf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xfc, 0x8f, 0x60, 0x0, 0x0, 0x26, 0x62,
    0x0, 0x0, 0x7, 0xf8, 0x3f, 0xd0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0xd, 0xf2, 0xb, 0xf9,
    0x0, 0xf, 0xe8, 0x8e, 0xf1, 0x0, 0x9f, 0xb0,
    0x2, 0xff, 0x60, 0x3, 0x10, 0x1, 0x30, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x42, 0x24, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0,

    /* U+F579 "" */
    0x0, 0x0, 0x0, 0x16, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x10, 0xb, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x3f, 0xd0, 0x4c, 0xd9,
    0x10, 0x0, 0x9d, 0xb4, 0xd, 0xf2, 0x8f, 0x64,
    0xf9, 0x7d, 0xc0, 0xc, 0xd7, 0x9f, 0x46, 0xf8,
    0xcf, 0x1b, 0x94, 0x91, 0xf4, 0x3f, 0x29, 0x49,
    0xc1, 0xfc, 0xff, 0xd, 0x78, 0xf1, 0xf5, 0x5f,
    0x1f, 0x87, 0xd0, 0xfe, 0xfe, 0x8, 0xd2, 0x17,
    0xf1, 0x1f, 0x71, 0x1d, 0x90, 0xef, 0xff, 0x0,
    0xcf, 0xff, 0x50, 0x5, 0xff, 0xfc, 0x0, 0xfe,
    0xcf, 0x10, 0x3, 0x51, 0x0, 0x0, 0x15, 0x30,
    0x1, 0xfc, 0x8f, 0x60, 0x0, 0x1, 0x22, 0x22,
    0x10, 0x0, 0x6, 0xf8, 0x3f, 0xd0, 0x0, 0x4f,
    0xff, 0xff, 0xf4, 0x0, 0xd, 0xf2, 0xb, 0xf9,
    0x0, 0x1a, 0xbb, 0xbb, 0xa1, 0x0, 0x9f, 0xb0,
    0x2, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x52, 0x25, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0,

    /* U+F584 "" */
    0x0, 0x0, 0x0, 0x15, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0xb, 0xf9, 0x0, 0x2, 0x20, 0x2,
    0x20, 0x0, 0x9f, 0xb0, 0x3f, 0xd0, 0x0, 0x1f,
    0xf3, 0x3f, 0xf1, 0x0, 0xd, 0xf3, 0x8f, 0x60,
    0x3d, 0xdf, 0xf4, 0x4f, 0xfd, 0xd3, 0x6, 0xf8,
    0xcf, 0x10, 0x8f, 0xff, 0xf0, 0xf, 0xff, 0xf9,
    0x1, 0xfc, 0xef, 0x0, 0x1a, 0xef, 0xc0, 0xc,
    0xfe, 0xa1, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x3,
    0x20, 0x2, 0x30, 0x0, 0x0, 0xef, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xcf, 0x10, 0x3, 0x64, 0x21, 0x12, 0x46, 0x30,
    0x1, 0xfc, 0x8f, 0x60, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x6, 0xf8, 0x3f, 0xd0, 0x1, 0xdf,
    0xff, 0xff, 0xfd, 0x10, 0xd, 0xf3, 0xb, 0xf9,
    0x0, 0x8, 0xef, 0xfe, 0x80, 0x0, 0x9f, 0xb0,
    0x2, 0xff, 0x70, 0x0, 0x1, 0x10, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9b, 0xb9, 0x61, 0x0,
    0x0, 0x0,

    /* U+F588 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x79, 0xb9, 0x73,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5d, 0xff, 0xff, 0xff, 0xfd, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xfc, 0x74, 0x24,
    0x7c, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xd4, 0x0, 0x0, 0x0, 0x3, 0xdf, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0x90, 0x0, 0x0, 0x0,
    0x3f, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0x30, 0x0, 0x0, 0xa, 0xf6, 0x0, 0x59,
    0x30, 0x0, 0x39, 0x50, 0x5, 0xfa, 0x0, 0x0,
    0x0, 0xfe, 0x0, 0x4f, 0xff, 0x10, 0x1f, 0xff,
    0x40, 0xe, 0xf0, 0x0, 0x0, 0x3c, 0x80, 0xb,
    0x94, 0xc7, 0x7, 0xc4, 0x9b, 0x0, 0x8c, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x30, 0x0, 0x30, 0x30,
    0x0, 0x30, 0x0, 0x0, 0x0, 0x17, 0xce, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xb7, 0x1b, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xfb, 0xff, 0xfb,
    0x0, 0x0, 0x65, 0x21, 0x1, 0x25, 0x60, 0x0,
    0xb, 0xff, 0xf8, 0xff, 0x43, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x3, 0x4f, 0xf8, 0x2,
    0x11, 0xd6, 0x0, 0x6f, 0xff, 0xff, 0xff, 0x60,
    0x6, 0xd1, 0x12, 0x0, 0x0, 0x2f, 0xe1, 0x0,
    0x3b, 0xff, 0xfb, 0x30, 0x1, 0xef, 0x20, 0x0,
    0x0, 0x0, 0x8f, 0xd1, 0x0, 0x0, 0x10, 0x0,
    0x1, 0xdf, 0x80, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xe4, 0x0, 0x0, 0x0, 0x4, 0xef, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xfc, 0x73, 0x23,
    0x6c, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5d, 0xff, 0xff, 0xff, 0xfd, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x7a,
    0xba, 0x73, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F598 "" */
    0x0, 0x0, 0x0, 0x16, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x9f, 0xff, 0xff,
    0xff, 0xf9, 0x10, 0x0, 0x0, 0x0, 0x3, 0xef,
    0xe9, 0x53, 0x35, 0x9e, 0xfe, 0x30, 0x0, 0x0,
    0x3, 0xff, 0x91, 0x0, 0x0, 0x0, 0x19, 0xff,
    0x40, 0x0, 0x1, 0xef, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xfe, 0x10, 0x0, 0xbf, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf3, 0x8, 0xf7, 0x0, 0x8, 0x60, 0x0, 0x5,
    0x61, 0x0, 0x6f, 0x80, 0xcf, 0x20, 0x4, 0xff,
    0x10, 0x1e, 0xff, 0xf3, 0x2, 0xfc, 0xe, 0xf0,
    0x0, 0x1c, 0xa0, 0x0, 0x94, 0x39, 0x20, 0xf,
    0xe0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xe, 0xf0, 0x0, 0x0, 0x4,
    0xc7, 0x0, 0x0, 0x0, 0xf, 0xe0, 0xcf, 0x20,
    0x0, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x2, 0xfc,
    0x8, 0xf7, 0x0, 0x0, 0x1, 0xbf, 0x30, 0xa,
    0xf7, 0x9, 0x60, 0x2f, 0xe0, 0x0, 0x0, 0x4f,
    0xb0, 0x3, 0xff, 0xf1, 0x20, 0x0, 0xaf, 0x80,
    0x0, 0x0, 0x7f, 0x70, 0x1f, 0xff, 0xff, 0x90,
    0x1, 0xef, 0x60, 0x0, 0x2d, 0xe2, 0x0, 0xdf,
    0xff, 0xff, 0x0, 0x3, 0xff, 0x91, 0x2, 0x30,
    0x0, 0x9, 0xff, 0xff, 0x90, 0x0, 0x3, 0xef,
    0xe9, 0x53, 0x35, 0x94, 0x4f, 0xc8, 0x30, 0x0,
    0x0, 0x1, 0x9f, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0x9a, 0xa9,
    0x51, 0x0, 0x0, 0x0, 0x0,

    /* U+F59B "" */
    0x0, 0x0, 0x0, 0x15, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0xb, 0xf9, 0x4, 0x0, 0x0, 0x0,
    0x0, 0x24, 0x9f, 0xb0, 0x3f, 0xd0, 0x1e, 0xc3,
    0x0, 0x0, 0x2a, 0xf5, 0xd, 0xf3, 0x8f, 0x60,
    0x3, 0xff, 0x90, 0x9, 0xff, 0x80, 0x6, 0xf8,
    0xcf, 0x10, 0x3, 0xff, 0xa0, 0x9, 0xff, 0x80,
    0x1, 0xfc, 0xef, 0x0, 0x1e, 0xc3, 0x0, 0x0,
    0x2a, 0xf5, 0x0, 0xfe, 0xfe, 0x0, 0x5, 0x0,
    0x0, 0x0, 0x0, 0x23, 0x0, 0xef, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xcf, 0x10, 0xc, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x1, 0xfc, 0x8f, 0x60, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x6, 0xf8, 0x3f, 0xd0, 0x2, 0xef,
    0xff, 0xff, 0xff, 0x40, 0xd, 0xf3, 0xb, 0xf9,
    0x0, 0x3d, 0xff, 0xff, 0xe4, 0x0, 0x9f, 0xb0,
    0x2, 0xff, 0x70, 0x0, 0x58, 0x96, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9b, 0xb9, 0x61, 0x0,
    0x0, 0x0,

    /* U+F5A4 "" */
    0x0, 0x0, 0x0, 0x15, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x43, 0x34, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf4, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0xb, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x3f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf3, 0x8f, 0x60,
    0x0, 0x86, 0x0, 0x0, 0x68, 0x0, 0x7, 0xf8,
    0xcf, 0x20, 0x4, 0xff, 0x10, 0x0, 0xff, 0x50,
    0x2, 0xfc, 0xef, 0x0, 0x1, 0xca, 0x0, 0x0,
    0x9c, 0x10, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xcf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xfc, 0x8f, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf8, 0x3f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf3, 0xb, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x42, 0x34, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9b, 0xb9, 0x61, 0x0,
    0x0, 0x0,

    /* U+F5B3 "" */
    0x0, 0x0, 0x0, 0x16, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x4f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf4, 0x0,
    0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x10, 0xb, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xb0, 0x3f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf2, 0x8f, 0x60,
    0x5, 0x62, 0x0, 0x0, 0x26, 0x50, 0x6, 0xf8,
    0xcf, 0x20, 0xdf, 0xff, 0x50, 0x5, 0xff, 0xfc,
    0x2, 0xfc, 0xff, 0x0, 0x95, 0x29, 0x40, 0x4,
    0x92, 0x59, 0x0, 0xfe, 0xfe, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0xef, 0xff, 0x0,
    0xae, 0x0, 0x2c, 0xc2, 0x0, 0xea, 0x0, 0xfe,
    0xcf, 0x20, 0xcf, 0x10, 0xbf, 0xfc, 0x1, 0xfc,
    0x2, 0xfc, 0x8f, 0x70, 0xcf, 0x10, 0xdf, 0xfd,
    0x1, 0xfc, 0x6, 0xf8, 0x3f, 0xe0, 0xcf, 0x10,
    0xdf, 0xfd, 0x1, 0xfc, 0xe, 0xf2, 0xb, 0xf8,
    0xcf, 0x10, 0x5f, 0xf5, 0x1, 0xfc, 0x8f, 0xb0,
    0x2, 0xff, 0xff, 0x10, 0x0, 0x0, 0x1, 0xff,
    0xff, 0x20, 0x0, 0x4f, 0xff, 0x10, 0x0, 0x0,
    0x1, 0xff, 0xf4, 0x0, 0x0, 0x4, 0xef, 0xf9,
    0x53, 0x35, 0x9e, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x19, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x9a, 0xa9, 0x61, 0x0,
    0x0, 0x0,

    /* U+F68C "" */
    0x5, 0x22, 0xf9, 0x3f, 0xa3, 0xfa, 0x2f, 0x90,
    0x52,

    /* U+F68D "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0xfb,
    0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x1, 0xfc,
    0x5, 0x20, 0x1, 0xfc, 0x2f, 0x90, 0x1, 0xfc,
    0x3f, 0xa0, 0x1, 0xfc, 0x3f, 0xa0, 0x1, 0xfc,
    0x2f, 0x90, 0x0, 0xfc, 0x5, 0x20, 0x0, 0x52,

    /* U+F68E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0xff, 0x0, 0x0, 0x0, 0xfb, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0xff, 0x0,
    0x0, 0x1, 0xfc, 0x0, 0xf, 0xf0, 0x52, 0x0,
    0x1f, 0xc0, 0x0, 0xff, 0x2f, 0x90, 0x1, 0xfc,
    0x0, 0xf, 0xf3, 0xfa, 0x0, 0x1f, 0xc0, 0x0,
    0xff, 0x3f, 0xa0, 0x1, 0xfc, 0x0, 0xf, 0xf2,
    0xf9, 0x0, 0xf, 0xc0, 0x0, 0xee, 0x5, 0x20,
    0x0, 0x52, 0x0, 0x4, 0x40,

    /* U+F68F "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x85, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0x80, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xc0, 0x1, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xc0, 0x1, 0xfc, 0x0,
    0x0, 0x0, 0x10, 0x0, 0x1f, 0xc0, 0x1, 0xfc,
    0x0, 0x0, 0x0, 0xfb, 0x0, 0x1f, 0xc0, 0x1,
    0xfc, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x1f, 0xc0,
    0x1, 0xfc, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x1f,
    0xc0, 0x1, 0xfc, 0x5, 0x20, 0x1, 0xfc, 0x0,
    0x1f, 0xc0, 0x1, 0xfc, 0x2f, 0x90, 0x1, 0xfc,
    0x0, 0x1f, 0xc0, 0x1, 0xfc, 0x3f, 0xa0, 0x1,
    0xfc, 0x0, 0x1f, 0xc0, 0x1, 0xfc, 0x3f, 0xa0,
    0x1, 0xfc, 0x0, 0x1f, 0xc0, 0x1, 0xfc, 0x2f,
    0x90, 0x0, 0xfc, 0x0, 0xf, 0xc0, 0x0, 0xfc,
    0x5, 0x20, 0x0, 0x52, 0x0, 0x5, 0x20, 0x0,
    0x52,

    /* U+F695 "" */
    0x26, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x52, 0x0, 0x9f, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xfc, 0x0, 0x1c, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0,
    0x9f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x5, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0x50, 0x1, 0xfc,
    0x0, 0x0, 0x0, 0x2d, 0xfd, 0x20, 0x0, 0x0,
    0x0, 0x1f, 0xc0, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x1, 0xbf, 0xf5, 0x0, 0x0, 0x0, 0x1f, 0xc0,
    0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x80, 0x0, 0x0, 0x1f, 0xc0, 0x1, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xfb, 0xd8, 0x0,
    0x1f, 0xc0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xcf, 0xfc, 0x0, 0x1f, 0xc0, 0x1,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0x60, 0x1f, 0xc0, 0x1, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x6f, 0xfa, 0x1f,
    0xc0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xb0, 0x0, 0x3, 0xef, 0xdf, 0xc0, 0x1, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x30,
    0x1c, 0xff, 0xc0, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xc0, 0x1, 0xf6, 0x0, 0x8f, 0xf7,
    0x1, 0xfc, 0x0, 0x0, 0x52, 0x0, 0x1f, 0xc0,
    0x1, 0xfc, 0x0, 0x5, 0xff, 0xb2, 0xfc, 0x0,
    0x2, 0xf9, 0x0, 0x1f, 0xc0, 0x1, 0xfc, 0x0,
    0x0, 0x2d, 0xfe, 0xfc, 0x0, 0x3, 0xfa, 0x0,
    0x1f, 0xc0, 0x1, 0xfc, 0x0, 0x4, 0x0, 0xaf,
    0xfc, 0x0, 0x3, 0xfa, 0x0, 0x1f, 0xc0, 0x1,
    0xfc, 0x0, 0x1f, 0x60, 0x7, 0xff, 0x90, 0x2,
    0xf9, 0x0, 0xf, 0xc0, 0x0, 0xfc, 0x0, 0xf,
    0xc0, 0x0, 0x4e, 0xf4, 0x0, 0x52, 0x0, 0x5,
    0x20, 0x0, 0x52, 0x0, 0x5, 0x20, 0x0, 0x1,
    0x70,

    /* U+F6A8 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xcf, 0xc0,
    0x0, 0x2, 0x70, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xfa, 0x1f, 0xc0, 0x0, 0x8, 0xfb, 0x0, 0x2,
    0x55, 0x57, 0xff, 0x80, 0x1f, 0xc0, 0x0, 0x1,
    0xdf, 0x70, 0x4f, 0xff, 0xff, 0xf6, 0x0, 0x1f,
    0xc0, 0x8, 0x30, 0x2f, 0xf0, 0xbf, 0x98, 0x88,
    0x30, 0x0, 0x1f, 0xc0, 0x1f, 0xf2, 0x9, 0xf6,
    0xcf, 0x10, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x8,
    0xf8, 0x5, 0xf8, 0xcf, 0x10, 0x0, 0x0, 0x0,
    0x1f, 0xc0, 0x3, 0xfa, 0x3, 0xfa, 0xcf, 0x10,
    0x0, 0x0, 0x0, 0x1f, 0xc0, 0x8, 0xf8, 0x5,
    0xf8, 0xaf, 0xcb, 0xbb, 0x60, 0x0, 0x1f, 0xc0,
    0x1f, 0xf2, 0x9, 0xf6, 0x2e, 0xff, 0xff, 0xf9,
    0x0, 0x1f, 0xc0, 0x8, 0x40, 0x2f, 0xf1, 0x0,
    0x12, 0x23, 0xef, 0xb0, 0x1f, 0xc0, 0x0, 0x1,
    0xdf, 0x70, 0x0, 0x0, 0x0, 0x1d, 0xfc, 0x2f,
    0xc0, 0x0, 0x8, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xef, 0xc0, 0x0, 0x2, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F6A9 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xfc,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf8, 0x2f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x35, 0x55, 0x8f, 0xf6, 0x2, 0xfb,
    0x0, 0x13, 0x0, 0x0, 0x41, 0x7f, 0xff, 0xff,
    0xf4, 0x0, 0x2f, 0xb0, 0xb, 0xf6, 0x0, 0x7f,
    0xae, 0xf8, 0x88, 0x82, 0x0, 0x2, 0xfb, 0x0,
    0x4f, 0xf6, 0x7f, 0xf3, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xb0, 0x0, 0x4f, 0xff, 0xf3, 0xf,
    0xd0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0x0, 0x0,
    0x9f, 0xf8, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xb0, 0x0, 0x7f, 0xff, 0xf6, 0xe, 0xfc,
    0xbb, 0xb5, 0x0, 0x2, 0xfb, 0x0, 0x6f, 0xf3,
    0x4f, 0xf6, 0x5f, 0xff, 0xff, 0xf8, 0x0, 0x2f,
    0xb0, 0xa, 0xf3, 0x0, 0x4f, 0x90, 0x2, 0x22,
    0x5f, 0xfa, 0x2, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xfb, 0x3f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xfe, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F6AA "" */
    0x0, 0x10, 0x1, 0xdf, 0xc1, 0x9f, 0xff, 0x7a,
    0xff, 0xf8, 0x2e, 0xfe, 0x10, 0x3, 0x0,

    /* U+F6AB "" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xbf, 0xff, 0xff, 0xb6, 0x0,
    0x0, 0x0, 0x7e, 0xff, 0xfd, 0xbd, 0xff, 0xfe,
    0x70, 0x1, 0xbf, 0xf9, 0x30, 0x0, 0x0, 0x39,
    0xff, 0xb1, 0x8f, 0xc2, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xcf, 0x82, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x62, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x0, 0x0, 0x0, 0x0,

    /* U+F6AC "" */
    0x4, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x1, 0x21, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x90, 0x2, 0x7b,
    0xef, 0xff, 0xff, 0xeb, 0x72, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xcc, 0xff, 0xff, 0xdb, 0xbc,
    0xdf, 0xff, 0xfb, 0x30, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xfe, 0x72, 0x0, 0x0, 0x0, 0x3, 0x8d,
    0xff, 0xc2, 0x0, 0x0, 0x4, 0x0, 0x8f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xdf, 0xf6,
    0x0, 0xb, 0xf9, 0x0, 0x5f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf9, 0x0, 0xec,
    0x10, 0x0, 0x2d, 0xfd, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3d, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0x64, 0x31, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xfd, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1a, 0xb0, 0x4, 0xef, 0xfe, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xfe, 0x40, 0x2, 0xcf, 0xe5, 0x7e, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf9, 0x0, 0x0,
    0x0, 0x9f, 0xf6, 0x1b, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x14, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfa, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x3e, 0xfd, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xfc, 0x10, 0x1b, 0xfe, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf7,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0x80, 0x0, 0x5,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xe1, 0x0, 0x0, 0x2, 0xdf, 0xd3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x65, 0x0,

    /* U+F7C2 "" */
    0x0, 0x0, 0x29, 0xbb, 0xbb, 0xbb, 0xb7, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x3f, 0xf7, 0x22, 0x22, 0x22, 0x3f, 0xe0, 0x3f,
    0xf7, 0x1, 0x1, 0x0, 0x10, 0xdf, 0x2e, 0xf7,
    0x4, 0xe0, 0xe4, 0x4e, 0xd, 0xfc, 0xf8, 0x0,
    0x4f, 0xe, 0x54, 0xf0, 0xdf, 0xfe, 0x0, 0x4,
    0xf0, 0xe5, 0x4f, 0xd, 0xff, 0xd0, 0x0, 0x16,
    0x6, 0x11, 0x60, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xef,
    0x32, 0x22, 0x22, 0x22, 0x22, 0x3f, 0xe8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x7, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xb7, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 189, .adv_w = 320, .box_w = 21, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 410, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 620, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 830, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1040, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1250, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1460, .adv_w = 320, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1681, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1891, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2101, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2311, .adv_w = 320, .box_w = 22, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2542, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2752, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2962, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3151, .adv_w = 280, .box_w = 19, .box_h = 14, .ofs_x = -1, .ofs_y = 1},
    {.bitmap_index = 3284, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 3389, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3599, .adv_w = 400, .box_w = 23, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3841, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4051, .adv_w = 360, .box_w = 24, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 4303, .adv_w = 320, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4524, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4695, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4933, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5123, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5333, .adv_w = 200, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5416, .adv_w = 240, .box_w = 15, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5559, .adv_w = 200, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5657, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5770, .adv_w = 200, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5860, .adv_w = 280, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6013, .adv_w = 280, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6166, .adv_w = 240, .box_w = 17, .box_h = 19, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6328, .adv_w = 240, .box_w = 17, .box_h = 19, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6490, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6680, .adv_w = 320, .box_w = 21, .box_h = 19, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6880, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7090, .adv_w = 280, .box_w = 19, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 7290, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7500, .adv_w = 280, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7653, .adv_w = 400, .box_w = 27, .box_h = 19, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7910, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8099, .adv_w = 360, .box_w = 23, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 8249, .adv_w = 360, .box_w = 23, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 8399, .adv_w = 360, .box_w = 23, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 8549, .adv_w = 360, .box_w = 23, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 8699, .adv_w = 360, .box_w = 23, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 8849, .adv_w = 240, .box_w = 13, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 8986, .adv_w = 360, .box_w = 23, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 9136, .adv_w = 400, .box_w = 27, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 9420, .adv_w = 240, .box_w = 15, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9578, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9788, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9998, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10208, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10418, .adv_w = 400, .box_w = 25, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10681, .adv_w = 320, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10902, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11112, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11322, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11532, .adv_w = 400, .box_w = 3, .box_h = 6, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 11541, .adv_w = 400, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 11581, .adv_w = 400, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 11666, .adv_w = 400, .box_w = 18, .box_h = 17, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 11819, .adv_w = 400, .box_w = 26, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12092, .adv_w = 360, .box_w = 22, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 12301, .adv_w = 360, .box_w = 23, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12520, .adv_w = 400, .box_w = 5, .box_h = 6, .ofs_x = 10, .ofs_y = -2},
    {.bitmap_index = 12535, .adv_w = 400, .box_w = 17, .box_h = 12, .ofs_x = 4, .ofs_y = -2},
    {.bitmap_index = 12637, .adv_w = 400, .box_w = 27, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 12921, .adv_w = 240, .box_w = 15, .box_h = 21, .ofs_x = 0, .ofs_y = -3}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0x1a1, 0x320, 0x322, 0x327, 0x32a, 0x339, 0x342,
    0x347, 0x34d, 0x350, 0x359, 0x3be, 0xfb6, 0xfbc, 0xfc1,
    0xfc2, 0xfc6, 0xfc7, 0xfc8, 0xfca, 0xfce, 0xfdc, 0xfdd,
    0xff3, 0xff9, 0xffd, 0x1000, 0x1001, 0x1002, 0x1006, 0x1015,
    0x1016, 0x1017, 0x1018, 0x1026, 0x102a, 0x1061, 0x10a8, 0x10cd,
    0x10d9, 0x11a0, 0x11ad, 0x11f5, 0x11f6, 0x11f7, 0x11f8, 0x11f9,
    0x1248, 0x132b, 0x132c, 0x137a, 0x148f, 0x150b, 0x152e, 0x1539,
    0x153d, 0x154d, 0x1550, 0x1559, 0x1568, 0x1641, 0x1642, 0x1643,
    0x1644, 0x164a, 0x165d, 0x165e, 0x165f, 0x1660, 0x1661, 0x1777
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 57419, .range_length = 6008, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 72, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_awesome_20_4 = {
#else
lv_font_t font_awesome_20_4 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 21,          /*The maximum line height required by the font*/
    .base_line = 3,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if FONT_AWESOME_20_4*/

