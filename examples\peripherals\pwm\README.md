# PWM

## Introduction

This project will introduce how to use the `tuyaos 3 pwm` related interfaces to set `pwm` waveforms.

* `PWM` Introduction

Pulse Width Modulation (`PWM`), is a method to reduce the average power delivered by an electrical signal, by effectively chopping it up into discrete parts.

* Duty Cycle, Frequency, Period

Period (T): T = Ton + Toff

Frequency (f): f = 1/T

Duty Cycle (D): Ton / (Ton + Toff)

![pwm explanation 12138.png](https://airtake-public-data-1254153901.cos.ap-shanghai.myqcloud.com/content-platform/hestia/165596468101cf5b911aa.png)

## Process Introduction

![pwm process 12138.png](https://airtake-public-data-1254153901.cos.ap-shanghai.myqcloud.com/content-platform/hestia/16559652280f408bad0ec.png)

## Technical Support

You can obtain Tuya's support through the following methods:

- TuyaOS Forum: https://www.tuyaos.com

- Developer Center: https://developer.tuya.com

- Help Center: https://support.tuya.com/help

- Technical Support Ticket Center: https://service.console.tuya.com