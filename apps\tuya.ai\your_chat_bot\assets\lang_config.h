// Auto-generated language config
#ifndef __LANGUAGE_CONFIG_H__
#define __LANGUAGE_CONFIG_H__

#include <stdio.h>

#ifndef zh_cn
    #define zh_cn
#endif

#ifdef __cplusplus
extern "C" {
#endif

#define LANG_CODE "zh-CN"

#define VERSION "版本 "
#define INITIALIZING "正在初始化..."
#define REGISTERING_NETWORK "等待网络..."
#define CONNECT_SERVER "连接服务器..."
#define STANDBY "待命"
#define CONNECT_TO "连接 "
#define CONNECTING "连接中..."
#define CONNECTED_TO "已连接 "
#define LISTENING "聆听中..."
#define SPEAKING "说话中..."
#define HOLD_TALK "长按"
#define TRIG_TALK "按键"
#define WAKEUP_TALK "唤醒"
#define FREE_TALK "随意"
#define ENTERING_WIFI_CONFIG_MODE "进入配网模式..."
#define VOLUME "音量 "
#define MUTED "已静音"
#define MAX_VOLUME "最大音量"

#ifdef __cplusplus
}
#endif

#endif // __LANGUAGE_CONFIG_H
