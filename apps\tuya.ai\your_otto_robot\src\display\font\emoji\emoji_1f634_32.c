
#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
#include "lvgl.h"
#elif defined(LV_BUILD_TEST)
#include "../lvgl.h"
#else
#include "lvgl/lvgl.h"
#endif

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_EMOJI_1F634_32
#define LV_ATTRIBUTE_EMOJI_1F634_32
#endif

static const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_EMOJI_1F634_32 uint8_t
    emoji_1f634_32_map[] = {

        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x49, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0xeb, 0xb4,
        0x91, 0x43, 0x91, 0x3b, 0x72, 0x3b, 0x52, 0x33, 0x32, 0x2b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xe8, 0xfd, 0xe9, 0xcc, 0xee, 0x73, 0xee, 0x73, 0x71, 0x3b, 0x32, 0x2b,
        0x4f, 0x74, 0x69, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe,
        0x28, 0xfe, 0x08, 0xfe, 0xe9, 0xcc, 0x52, 0x2b, 0xb1, 0x4b, 0x4a, 0xfe, 0x69, 0xfe, 0x4b, 0xcd, 0x52, 0x2b,
        0x33, 0x2b, 0x33, 0x2b, 0x33, 0x2b, 0x33, 0x2b, 0x33, 0x2b, 0x33, 0x2b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe,
        0x6c, 0xc5, 0x4c, 0xbd, 0x4c, 0xbd, 0x4c, 0xbd, 0x0a, 0xee, 0x69, 0xfe, 0xa8, 0xf5, 0x91, 0x43, 0x52, 0x33,
        0xcb, 0xdd, 0x69, 0xfe, 0xc7, 0xfd, 0x0d, 0x7c, 0x32, 0x2b, 0x32, 0x2b, 0x32, 0x2b, 0x32, 0x2b, 0x32, 0x2b,
        0x32, 0x2b, 0x52, 0x2b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x48, 0xe5, 0x52, 0x33, 0x32, 0x2b, 0x32, 0x2b, 0x32, 0x2b,
        0x6c, 0xbd, 0x29, 0xfe, 0x0e, 0x74, 0x32, 0x2b, 0x0d, 0xa5, 0x69, 0xfe, 0x69, 0xfe, 0x86, 0xfd, 0x08, 0xe5,
        0x4c, 0x94, 0x2d, 0x8c, 0x2d, 0x8c, 0x52, 0x33, 0x32, 0x2b, 0x52, 0x2b, 0x33, 0x2b, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0xe8, 0xfd, 0xc7, 0xfd, 0x28, 0xe5, 0x52, 0x33, 0x2f, 0x6c, 0x49, 0xfe, 0xca, 0xb4, 0x32, 0x2b, 0x52, 0x2b,
        0xd0, 0x53, 0xd1, 0x53, 0x30, 0x6c, 0x49, 0xfe, 0x28, 0xfe, 0x28, 0xfe, 0xe8, 0xfd, 0x6c, 0x9c, 0x32, 0x2b,
        0x32, 0x2b, 0x33, 0x2b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe8, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x08, 0xfe, 0xb0, 0x5b, 0x71, 0x43,
        0x2a, 0xee, 0xe8, 0xfd, 0x8b, 0xac, 0xb0, 0x53, 0xb0, 0x53, 0xb0, 0x53, 0xb0, 0x53, 0x2f, 0x6c, 0x49, 0xfe,
        0x69, 0xfe, 0x49, 0xfe, 0x29, 0xdd, 0x52, 0x33, 0x32, 0x2b, 0x52, 0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x49, 0xfe, 0x4c, 0x94, 0x32, 0x2b, 0x8c, 0xc5, 0x69, 0xfe, 0x49, 0xfe, 0x08, 0xfe, 0x08, 0xfe,
        0x08, 0xfe, 0x08, 0xfe, 0x08, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xa7, 0xfd, 0x90, 0x53, 0x32, 0x2b,
        0xb1, 0x4b, 0x2a, 0xf6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xdd, 0x52, 0x2b, 0x32, 0x2b,
        0x32, 0x2b, 0x52, 0x2b, 0xed, 0x9c, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x08, 0xfe, 0x0d, 0x84, 0x32, 0x2b, 0x52, 0x33, 0xcb, 0xd5, 0x69, 0xfe, 0x49, 0xfe, 0x00, 0x00,
        0x00, 0x00, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x49, 0xfe, 0x67, 0xf5, 0xab, 0xac, 0x8b, 0xac, 0x8b, 0xac, 0xcc, 0xac, 0xea, 0xe5, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0xca, 0xc4, 0x52, 0x2b, 0x32, 0x2b,
        0x72, 0x33, 0xd1, 0x53, 0xd1, 0x53, 0xb1, 0x43, 0x32, 0x2b, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0xa7, 0xfd, 0xef, 0x6b, 0x32, 0x2b, 0x32, 0x2b, 0x32, 0x2b, 0x32, 0x2b, 0x32, 0x2b, 0x32, 0x2b,
        0x52, 0x2b, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x66, 0xfd, 0x27, 0xed, 0xaa, 0xbc,
        0xaa, 0xbc, 0xaa, 0xbc, 0xaa, 0xbc, 0xaa, 0xbc, 0xeb, 0xbc, 0x6b, 0xc5, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x49, 0xfe,
        0x69, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xe8, 0xed, 0xc8, 0xed, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xe8, 0xed, 0xc8, 0xed, 0xc8, 0xed, 0xe8, 0xed, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xc8, 0xed, 0xe8, 0xf5, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x47, 0xd5, 0x20, 0x6a, 0x42, 0x8b, 0x04, 0xac, 0x65, 0xbc, 0x24, 0xac, 0x62, 0x93, 0x40, 0x6a,
        0xc6, 0xc4, 0xc6, 0xc4, 0x40, 0x6a, 0x62, 0x93, 0x24, 0xac, 0x65, 0xbc, 0x04, 0xac, 0x42, 0x8b, 0x20, 0x6a,
        0x47, 0xd5, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xc3, 0xa3, 0x20, 0x62,
        0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x62, 0x93, 0x49, 0xfe, 0x49, 0xfe, 0x62, 0x93, 0x20, 0x62,
        0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0xc3, 0xa3, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0x24, 0xb4, 0x40, 0x6a, 0x20, 0x62, 0x20, 0x6a, 0xe4, 0xa3,
        0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0xe4, 0xa3, 0x20, 0x6a, 0x20, 0x62, 0x40, 0x6a, 0x44, 0xb4,
        0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x29, 0xf6, 0x87, 0xe5, 0x09, 0xf6, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x09, 0xf6, 0x87, 0xe5, 0x29, 0xf6, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xc8, 0xed, 0x65, 0xb4, 0x65, 0xb4,
        0xc8, 0xed, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xaa, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0xa8, 0xe5, 0x40, 0x6a, 0x20, 0x62, 0x20, 0x62, 0x60, 0x6a, 0xa8, 0xe5, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0xaa, 0xfe, 0x00, 0x00, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x04, 0xac, 0x20, 0x62, 0x20, 0x62,
        0x20, 0x62, 0x20, 0x62, 0x04, 0xac, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0xe4, 0xa3, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0xe4, 0xa3, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x49, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x47, 0xd5, 0x20, 0x62,
        0x20, 0x62, 0x20, 0x62, 0x20, 0x62, 0x47, 0xdd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0xe8, 0xfd, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x06, 0xd5, 0x63, 0x93, 0x63, 0x93, 0x26, 0xd5, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x49, 0xfe, 0xc9, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x89, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x69, 0xfe, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc9, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xc9, 0xfe,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x49, 0xfe, 0x49, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xaa, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe,
        0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0x69, 0xfe, 0xaa, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x59, 0x9b, 0xca, 0xea, 0xfa, 0xfa, 0xf8, 0xfa, 0xf6, 0xf0, 0xe7,
        0xbd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x26, 0x9d, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x25, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x8c, 0xfb, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xaf, 0x97, 0x99, 0x99, 0x99, 0x99,
        0x99, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0xd3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9b, 0x00, 0x00,
        0x00, 0x00, 0x31, 0xea, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0x19, 0x00, 0x00, 0x00, 0x25, 0xea, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f, 0x00, 0x00, 0x00, 0x08, 0xd0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0x9d, 0x00, 0x00, 0x00, 0x00, 0x8c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x86, 0x00, 0x00,
        0x00, 0x26, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x18, 0x00, 0x00, 0x9d, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xea, 0x7c, 0x0e, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xd9, 0x59, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0x76, 0x9b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x99, 0xca, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc7, 0xeb, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xf8, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
        0xeb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xca, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc7, 0x9a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0x98, 0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0x55, 0x0c, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0c, 0x00, 0x9c,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9a, 0x00, 0x00, 0x25, 0xfa, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x22, 0x00, 0x00, 0x00, 0x8a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0x87, 0x00, 0x00, 0x00, 0x00, 0x08, 0xc7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc6, 0x07, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x18, 0xe2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x2e, 0xe3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xe2, 0x2d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0xc1, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc6,
        0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x81, 0xf4, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x86, 0x07, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x96, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x99, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x57, 0x98, 0xc8, 0xe9, 0xf9, 0xf9, 0xe9, 0xc8, 0x97,
        0x56, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

};

const lv_image_dsc_t emoji_1f634_32 = {
    .header.magic = LV_IMAGE_HEADER_MAGIC,
    .header.cf = LV_COLOR_FORMAT_RGB565A8,
    .header.flags = 0,
    .header.w = 32,
    .header.h = 32,
    .header.stride = 64,
    .data_size = sizeof(emoji_1f634_32_map),
    .data = emoji_1f634_32_map,
};
