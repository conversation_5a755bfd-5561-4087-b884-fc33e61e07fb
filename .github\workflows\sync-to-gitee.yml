name: syncToGitee
on: 
  - push
  - delete

jobs:
  sync:
    if: github.repository == 'tuya/TuyaOpen'
    runs-on: ubuntu-latest
    name: Git Repo Sync
    steps:
    - uses: actions/checkout@v2
      with:
        fetch-depth: 0
    - uses: tuya/<PERSON><PERSON><PERSON><PERSON>@master
      with:
        target-url: 'https://gitee.com/tuya-open/TuyaOpen.git'
        target-username: 'flyingcys'
        target-token: ${{ secrets.GITEE_TOKEN }}
