/*******************************************************************************
 * Size: 30 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --force-fast-kern-format --font fa-light-300.ttf --format lvgl --lv-include lvgl.h
 *--bpp 4 -o font_awesome_30_4.c --size 30 -r
 *0xf5a4,0xf118,0xf59b,0xf588,0xe384,0xf556,0xf5b3,0xf584,0xf579,0xe36b,0xe375,0xe39b,0xf4da,0xe398,0xe392,0xe372,0xf598,0xe409,0xe38d,0xe3a4,0xe36d,0xf240,0xf241,0xf242,0xf243,0xf244,0xf377,0xf376,0xf1eb,0xf6ab,0xf6aa,0xf6ac,0xf012,0xf68f,0xf68e,0xf68d,0xf68c,0xf695,0xf028,0xf6a8,0xf027,0xf6a9,0xf001,0xf00c,0xf00d,0xf011,0xf013,0xf1f8,0xf015,0xf03e,0xf044,0xf048,0xf051,0xf04b,0xf04c,0xf04d,0xf060,0xf061,0xf062,0xf063,0xf071,0xf0f3,0xf3c5,0xf0ac,0xf124,0xf7c2,0xf293,0xf075,0xe1ec,0xf007,0xe04b,0xf019
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef FONT_AWESOME_30_4
#define FONT_AWESOME_30_4 1
#endif

#if FONT_AWESOME_30_4

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+E04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xbb, 0xbb, 0xff, 0xbb, 0xbb, 0xa6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf7, 0x32, 0x22,
    0x22, 0x22, 0x22, 0x4a, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf5, 0x8, 0xa7, 0x0, 0x0, 0x5a, 0xa3, 0xa, 0xf5, 0x0, 0x0,
    0x65, 0x0, 0xfe, 0xc, 0xff, 0xfa, 0x0, 0x6f, 0xff, 0xf2, 0x4f, 0x90, 0x38, 0xf, 0xd0, 0x1f, 0xc3, 0xfd, 0x7f, 0xf1,
    0xd, 0xf8, 0xbf, 0x92, 0xfa, 0x9, 0xf3, 0xfd, 0x1, 0xfc, 0x4f, 0x80, 0xdf, 0x20, 0xef, 0x4, 0xfa, 0x2f, 0xa0, 0x9f,
    0x3f, 0xd0, 0x1f, 0xc0, 0xef, 0xdf, 0xc0, 0xa, 0xfd, 0xef, 0x62, 0xfa, 0x9, 0xf3, 0xfd, 0x1, 0xfc, 0x2, 0xbe, 0xb1,
    0x0, 0x1b, 0xff, 0x90, 0x2f, 0xa0, 0x9f, 0x3f, 0xd0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x2, 0xfa, 0x9, 0xf3,
    0xfd, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xa0, 0x9f, 0x39, 0x80, 0xf, 0xe0, 0x5, 0xd1, 0x8, 0xc0,
    0xc, 0x80, 0x4, 0xf9, 0x4, 0xb0, 0x0, 0x0, 0xcf, 0x40, 0x9f, 0x40, 0xcf, 0x0, 0xfc, 0x0, 0xbf, 0x50, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x6a, 0xf6, 0x2d, 0xf3, 0x3f, 0xd4, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8b, 0xbb, 0xbb, 0xbb, 0xbb, 0xba, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6b, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xb7, 0x10, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x0, 0xdf, 0xc4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0xff, 0x20, 0x7f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfb, 0xc, 0xf3, 0x0, 0x5d, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdb, 0x20, 0xe, 0xf0, 0xff, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xbf, 0x3f, 0xe0, 0x2, 0xfa, 0x9, 0xf4, 0xc, 0xf0, 0xf, 0xc0, 0x1f,
    0xc0, 0xa, 0xf3, 0xef, 0x30, 0x2f, 0xa0, 0x9f, 0x50, 0xdf, 0x11, 0xfd, 0x2, 0xfc, 0x1, 0xdf, 0x27, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x6, 0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdc, 0x80, 0x0,

    /* U+E1EC "" */
    0x0, 0x0, 0x0, 0x0, 0x5, 0x20, 0x0, 0x24, 0x0, 0x0, 0x52, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xa0, 0x0,
    0xbf, 0x10, 0x1, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0xcf, 0x10, 0x2, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0xcf, 0x10, 0x2, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7a, 0xcf,
    0xeb, 0xbb, 0xef, 0xbb, 0xbc, 0xfe, 0xb8, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x93, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x28, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x10, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x30, 0x0, 0x7d, 0xdd, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xdd,
    0xd8, 0x9f, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xfb, 0x0, 0x1, 0xfc, 0x0, 0x0,
    0x3, 0xe3, 0x0, 0x0, 0xc, 0x80, 0x0, 0xaf, 0x30, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x1f, 0xc0,
    0x0, 0xaf, 0x30, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x1f, 0xff, 0x20, 0x0, 0x1f, 0xc0, 0x0, 0xaf, 0x30, 0x0, 0x0, 0x1,
    0xfc, 0x0, 0x0, 0x8f, 0xef, 0x80, 0x0, 0x1f, 0xc0, 0x0, 0xaf, 0x30, 0x0, 0x9f, 0xff, 0xfc, 0x0, 0x0, 0xef, 0x4f,
    0xe0, 0x0, 0x1f, 0xc0, 0x0, 0xaf, 0xff, 0xfb, 0x7e, 0xee, 0xfc, 0x0, 0x6, 0xfb, 0x1c, 0xf5, 0x0, 0x1f, 0xc0, 0x0,
    0xaf, 0xee, 0xe9, 0x0, 0x1, 0xfc, 0x0, 0xc, 0xff, 0xff, 0xfc, 0x0, 0x1f, 0xc0, 0x0, 0xaf, 0x30, 0x0, 0x0, 0x1, 0xfc,
    0x0, 0x3f, 0xec, 0xcc, 0xff, 0x20, 0x1f, 0xc0, 0x0, 0xaf, 0x30, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x8f, 0x60, 0x0, 0x7f,
    0x70, 0xf, 0xb0, 0x0, 0xaf, 0x30, 0x0, 0x6c, 0xcc, 0xfc, 0x0, 0x38, 0x0, 0x0, 0x8, 0x20, 0x1, 0x0, 0x0, 0xaf, 0xdc,
    0xc8, 0xaf, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xfc, 0x1, 0x12, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x41, 0x10, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x10, 0x0, 0x0, 0x0, 0xcf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfe,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0xcf, 0x10, 0x2, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0xcf, 0x10, 0x2, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0,
    0xcf, 0x10, 0x2, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x40, 0x0, 0x59, 0x0, 0x0, 0x94, 0x0, 0x0, 0x0,
    0x0,

    /* U+E36B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xef,
    0xff, 0xfe, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xfd, 0xcc, 0xdf, 0xff, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x83, 0x0, 0x0, 0x0, 0x38, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xfc, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10, 0x38, 0xa9, 0x0, 0x0, 0x0, 0x9a, 0x83, 0x1, 0xcf, 0xb0, 0x0, 0x0, 0x7f,
    0xd1, 0x2b, 0xff, 0xff, 0x10, 0x0, 0x1, 0xff, 0xff, 0xb2, 0x1d, 0xf7, 0x0, 0x1, 0xff, 0x22, 0xff, 0xd7, 0x31, 0x0,
    0x0, 0x0, 0x13, 0x7d, 0xff, 0x22, 0xff, 0x10, 0x9, 0xf8, 0x3, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x30,
    0x8f, 0x90, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x5f, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf5, 0x9f, 0x50, 0x0, 0x0, 0x3, 0x71, 0x0, 0x0, 0x0, 0x17, 0x30, 0x0,
    0x0, 0x5, 0xf9, 0xcf, 0x10, 0x0, 0x0, 0xf, 0xf9, 0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0, 0x0, 0x1, 0xfc, 0xef, 0x0, 0x0,
    0x0, 0xe, 0xf8, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0x1, 0x50, 0x0, 0x0, 0x0, 0x5,
    0x10, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xdf, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfd, 0xbf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xff, 0xd3,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfb, 0x8f, 0x70, 0x0, 0x0, 0x0, 0x1, 0xff, 0xcc, 0xff, 0x10, 0x0, 0x0, 0x0, 0x7, 0xf8,
    0x3f, 0xc0, 0x0, 0x0, 0x0, 0x8, 0xf7, 0x0, 0x7f, 0x80, 0x0, 0x0, 0x0, 0xc, 0xf3, 0xd, 0xf3, 0x0, 0x0, 0x0, 0xb,
    0xf2, 0x0, 0x2f, 0xb0, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x6, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xf2, 0x0, 0x2f, 0xc0, 0x0, 0x0,
    0x0, 0xbf, 0x60, 0x0, 0xdf, 0x60, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x2f, 0xb0, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x3f, 0xf4,
    0x0, 0x0, 0x7, 0xfa, 0x0, 0x9f, 0x70, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x6, 0xff, 0x40, 0x0, 0x0, 0xdf, 0xff, 0xfd,
    0x0, 0x0, 0x4, 0xff, 0x60, 0x0, 0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0, 0x19, 0xee, 0x91, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xe9,
    0x63, 0x22, 0x36, 0x9e, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x8a, 0xbb, 0xa8, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+E36D "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xef,
    0xff, 0xfe, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xfd, 0xcc, 0xdf, 0xff, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x83, 0x0, 0x0, 0x0, 0x38, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xfc, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xb0, 0x0, 0x0, 0x7f,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x10, 0x9, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x90, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x5f, 0xa0, 0x0, 0x0, 0x1, 0x30, 0x0, 0x0,
    0x0, 0x3, 0x10, 0x0, 0x0, 0xa, 0xf5, 0x9f, 0x50, 0x0, 0x0, 0xd, 0xf8, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0x5,
    0xf9, 0xcf, 0x10, 0x0, 0x0, 0xf, 0xf9, 0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0, 0x0, 0x1, 0xfc, 0xef, 0x0, 0x0, 0x0, 0x3,
    0x71, 0x0, 0x0, 0x0, 0x17, 0x30, 0x0, 0x0, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfd, 0xbf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x30,
    0x0, 0x0, 0x3, 0xfb, 0x8f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xdf, 0xff, 0xf7, 0x0, 0x0, 0x7, 0xf8, 0x3f, 0xc0,
    0x0, 0x0, 0x0, 0x1, 0x7e, 0xff, 0xfe, 0xcb, 0xa2, 0x0, 0x0, 0xc, 0xf3, 0xd, 0xf3, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xa5,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x6, 0xfc, 0x0, 0x0, 0x1b, 0xff, 0x92, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x60, 0x0, 0xdf, 0x60, 0x0, 0x7f, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x6, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xe9, 0x63, 0x22, 0x36, 0x9e,
    0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x8a, 0xbb, 0xa8, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+E372 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xff,
    0xff, 0xff, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xff, 0xfc, 0xbb, 0xcf, 0xff, 0xfb, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0x82, 0x0, 0x0, 0x0, 0x28, 0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xfd, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xb0, 0x0, 0x0, 0x7f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x10, 0x9, 0xf8, 0x0, 0x0, 0x7, 0xbb, 0x0, 0x0, 0x0, 0xab, 0x70, 0x0, 0x0, 0x8f, 0x90, 0xf,
    0xf1, 0x0, 0x1, 0xdf, 0xfe, 0x10, 0x0, 0x0, 0xef, 0xfd, 0x10, 0x0, 0x1f, 0xf0, 0x5f, 0xa0, 0x0, 0xb, 0xfc, 0x30,
    0x0, 0x0, 0x0, 0x3, 0xbf, 0xc0, 0x0, 0xa, 0xf5, 0x9f, 0x50, 0x0, 0x4f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf5,
    0x0, 0x5, 0xf9, 0xcf, 0x10, 0x0, 0x2c, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xc3, 0x0, 0x1, 0xfc, 0xef, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0xdf, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfd, 0xbf, 0x30, 0x0, 0x6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x62, 0x0, 0x3, 0xfb, 0x8f, 0x70, 0x0, 0x5f, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf9, 0x0, 0x7, 0xf7, 0x3f,
    0xc0, 0x0, 0xb, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xe2, 0x0, 0xc, 0xf3, 0xd, 0xf3, 0x0, 0x0, 0xaf, 0xf9, 0x20,
    0x0, 0x1, 0x8e, 0xff, 0xb0, 0x0, 0x3f, 0xd0, 0x5, 0xfc, 0x0, 0x0, 0x6, 0xff, 0xff, 0xcc, 0xef, 0xff, 0xbf, 0xb0,
    0x0, 0xbf, 0x60, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x18, 0xcf, 0xff, 0xff, 0xe2, 0x2f, 0xb0, 0x7, 0xfd, 0x0, 0x0, 0x2f,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x11, 0x1f, 0xc0, 0x2f, 0xb0, 0x3f, 0xf3, 0x0, 0x0, 0x5, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xc0, 0x2f, 0xb0, 0x5f, 0x60, 0x0, 0x0, 0x0, 0x6f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x2f, 0xb0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xd6, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x2f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xff,
    0xea, 0x63, 0x20, 0xa, 0x60, 0x2f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x2f,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x8a, 0xb7, 0x0, 0x0, 0x9, 0x40, 0x0, 0x0, 0x0,

    /* U+E375 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xef,
    0xff, 0xfe, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xfd, 0xcc, 0xdf, 0xff, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x83, 0x0, 0x0, 0x0, 0x38, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd5, 0x7a, 0xa0, 0x0, 0x0, 0x0, 0xa,
    0xa7, 0x5d, 0xfc, 0x0, 0x0, 0x0, 0xb, 0xfe, 0xcf, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xfc, 0xef, 0xb0, 0x0, 0x0,
    0x7f, 0xff, 0xfe, 0x73, 0x10, 0x0, 0x0, 0x0, 0x1, 0x37, 0xef, 0xff, 0xf7, 0x0, 0x1, 0xff, 0x5f, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf5, 0xff, 0x10, 0x9, 0xf8, 0x0, 0x5, 0xac, 0xa3, 0x0, 0x0, 0x0, 0x6c, 0xfd, 0x80, 0x0,
    0x8f, 0x90, 0xf, 0xf1, 0x0, 0xbf, 0xfe, 0xff, 0x80, 0x0, 0xa, 0xff, 0xff, 0xfd, 0x10, 0x1f, 0xf0, 0x5f, 0xa0, 0xa,
    0xfa, 0x10, 0x2d, 0xf5, 0x0, 0x6f, 0xe5, 0x13, 0xcf, 0xa0, 0xa, 0xf5, 0x9f, 0x50, 0x1f, 0xd0, 0x49, 0x21, 0xfd, 0x0,
    0xdf, 0x32, 0x94, 0xe, 0xf1, 0x5, 0xf9, 0xcf, 0x10, 0x4f, 0x90, 0xff, 0xb0, 0xdf, 0x0, 0xfd, 0xb, 0xff, 0x9, 0xf4,
    0x1, 0xfc, 0xef, 0x0, 0x3f, 0xb0, 0xbf, 0x70, 0xef, 0x0, 0xfe, 0x7, 0xfb, 0xa, 0xf3, 0x0, 0xfe, 0xfe, 0x0, 0xf,
    0xf3, 0x1, 0x6, 0xfb, 0x0, 0xcf, 0x60, 0x10, 0x2f, 0xf0, 0x0, 0xef, 0xfe, 0x0, 0x7, 0xff, 0x96, 0xaf, 0xf3, 0x0,
    0x4f, 0xf9, 0x57, 0xef, 0x80, 0x0, 0xef, 0xdf, 0x0, 0x0, 0x8f, 0xff, 0xff, 0x50, 0x0, 0x7, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0xfd, 0xbf, 0x30, 0x0, 0x2, 0x78, 0x60, 0x0, 0x22, 0x0, 0x39, 0xba, 0x50, 0x0, 0x3, 0xfb, 0x8f, 0x70, 0x0, 0x0,
    0x0, 0x2, 0xbf, 0xff, 0xfb, 0x20, 0x0, 0x0, 0x0, 0x7, 0xf8, 0x3f, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xfe, 0xbb, 0xef, 0xf4,
    0x0, 0x0, 0x0, 0xc, 0xf3, 0xd, 0xf3, 0x0, 0x0, 0x0, 0xef, 0x70, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x6,
    0xfc, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x0, 0x0, 0x9f, 0x60, 0x0, 0x0, 0xbf, 0x60, 0x0, 0xdf, 0x60, 0x0, 0x9, 0xfe, 0xdd,
    0xdd, 0xdd, 0xef, 0x90, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x4f, 0xf3, 0x0, 0x0, 0x6, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x60, 0x0, 0x0, 0x0, 0x7f,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5d, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xe9, 0x63, 0x22, 0x36, 0x9e, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x8a,
    0xbb, 0xa8, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+E384 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xef,
    0xff, 0xfe, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xfd, 0xcc, 0xdf, 0xff, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x83, 0x0, 0x0, 0x0, 0x38, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xfc, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xb0, 0x0, 0x0, 0x7f,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x0, 0x4, 0xa0, 0x0, 0x0, 0x0,
    0xa, 0x50, 0x0, 0x2, 0xff, 0x10, 0x9, 0xf8, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x2f, 0xf3, 0x0, 0x0, 0x8f, 0x90,
    0xf, 0xf1, 0x0, 0x3, 0xef, 0x70, 0x0, 0x0, 0x0, 0x7, 0xfe, 0x30, 0x0, 0x1f, 0xf0, 0x5f, 0xa0, 0x1, 0x9f, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf9, 0x10, 0xa, 0xf5, 0x9f, 0x50, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xef,
    0xf2, 0x5, 0xf9, 0xcf, 0x10, 0x1c, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0xc1, 0x1, 0xfc, 0xef, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x82, 0x0, 0x3, 0x60, 0x0, 0x7, 0x40, 0x0, 0x17, 0x10, 0x0, 0xef, 0xdf, 0x0,
    0x4, 0xfe, 0x52, 0x7f, 0xf1, 0x0, 0xf, 0xf7, 0x25, 0xef, 0x50, 0x0, 0xfd, 0xbf, 0x30, 0x0, 0x9f, 0xff, 0xff, 0x60,
    0x0, 0x6, 0xff, 0xff, 0xf9, 0x0, 0x3, 0xfb, 0x8f, 0x70, 0x0, 0x4, 0x9b, 0x82, 0x0, 0x0, 0x0, 0x28, 0xb9, 0x40, 0x0,
    0x7, 0xf8, 0x3f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf3, 0xd, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x6, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x60, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x6, 0xdd, 0xdd, 0xdd, 0x50, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x3f, 0xf4,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x6, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x60, 0x0, 0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xe9, 0x63,
    0x22, 0x36, 0x9e, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x8a, 0xbb, 0xa8, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+E38D "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x10, 0x0, 0x0, 0x0, 0x2, 0x33, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0xcf,
    0xff, 0xff, 0xfc, 0x82, 0x0, 0x8, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xff, 0xfc, 0xbb, 0xce, 0xff, 0xf8,
    0x0, 0x3a, 0xcf, 0xf8, 0x0, 0x0, 0x0, 0x8, 0xff, 0xe8, 0x20, 0x0, 0x0, 0x2, 0x7d, 0x80, 0x0, 0x2e, 0xf8, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xfe, 0x95, 0x0, 0x0, 0xd, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xd0, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbd, 0xdd, 0xc2, 0x2,
    0x22, 0x20, 0x0, 0x7, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x1, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0x90, 0x0, 0x0, 0xe9, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0xd2, 0x0, 0xe, 0xf1,
    0x5, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0x30, 0x0, 0x9f, 0x60, 0x9f, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfa, 0xc, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xd0, 0xef, 0x0, 0x0, 0x82, 0x0, 0x3, 0x60, 0x0, 0x6, 0x40, 0x0, 0x17, 0x10, 0x0, 0xfe, 0xf, 0xe0, 0x0,
    0x4f, 0xe5, 0x27, 0xff, 0x10, 0x0, 0xff, 0x72, 0x5e, 0xf5, 0x0, 0xe, 0xf0, 0xfe, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x60,
    0x0, 0x6, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xef, 0xd, 0xf0, 0x0, 0x0, 0x4a, 0xb9, 0x20, 0x0, 0x0, 0x2, 0x9b, 0x94, 0x0,
    0x0, 0xf, 0xd0, 0xbf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfb, 0x8, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x80, 0x3f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf3, 0x0, 0xdf, 0x30, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x5,
    0xfc, 0x0, 0x0, 0x0, 0x2, 0xfd, 0x11, 0xdf, 0x20, 0x0, 0x0, 0x0, 0xcf, 0x50, 0x0, 0xd, 0xf6, 0x0, 0x0, 0x0, 0x3f,
    0xa0, 0xa, 0xf3, 0x0, 0x0, 0x0, 0x6f, 0xc0, 0x0, 0x0, 0x2f, 0xf4, 0x0, 0x0, 0x0, 0xff, 0x77, 0xff, 0x0, 0x0, 0x0,
    0x4f, 0xf2, 0x0, 0x0, 0x0, 0x5f, 0xf4, 0x0, 0x0, 0x5, 0xff, 0xff, 0x50, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf8, 0x0, 0x0, 0x2, 0x77, 0x20, 0x0, 0x0, 0x7f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfd, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xdf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xea, 0x63, 0x22, 0x36, 0x9e, 0xff, 0xb1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x14, 0x8a, 0xbb, 0xa8, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+E392 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xef,
    0xff, 0xfe, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xfd, 0xcc, 0xdf, 0xff, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x83, 0x0, 0x0, 0x0, 0x38, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xfc, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xb0, 0x0, 0x0, 0x7f,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x10, 0x9, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x90, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x5f, 0xa0, 0x0, 0x0, 0x49, 0xa8, 0x20,
    0x0, 0x2, 0x8a, 0x94, 0x0, 0x0, 0xa, 0xf5, 0x9f, 0x50, 0x0, 0xa, 0xff, 0xff, 0xf5, 0x0, 0x5f, 0xff, 0xff, 0xa0, 0x0,
    0x5, 0xf9, 0xcf, 0x10, 0x0, 0x5f, 0xe5, 0x38, 0xff, 0x0, 0xff, 0x83, 0x5e, 0xf6, 0x0, 0x1, 0xfc, 0xef, 0x0, 0x0,
    0x18, 0x10, 0x0, 0x46, 0x0, 0x65, 0x0, 0x1, 0x71, 0x0, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xdf, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfd, 0xbf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfb, 0x8f, 0x70, 0x0, 0x0, 0x54, 0x0, 0x0, 0x0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x7, 0xf8, 0x3f,
    0xc0, 0x0, 0x0, 0xff, 0x60, 0x0, 0x0, 0x0, 0x6, 0xff, 0x0, 0x0, 0xc, 0xf3, 0xd, 0xf3, 0x0, 0x0, 0x5f, 0xfa, 0x30,
    0x0, 0x3, 0xaf, 0xf5, 0x0, 0x0, 0x3f, 0xd0, 0x6, 0xfc, 0x0, 0x0, 0x3, 0xdf, 0xfe, 0xcc, 0xef, 0xfd, 0x30, 0x0, 0x0,
    0xbf, 0x60, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x5, 0xbf, 0xff, 0xfc, 0x60, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x3f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x6, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x60, 0x0, 0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xe9, 0x63, 0x22, 0x36,
    0x9e, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x8a, 0xbb, 0xa8, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+E398 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xff,
    0xff, 0xff, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xec, 0xbb, 0xce, 0xff, 0xfb, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd, 0x61, 0x0, 0x0, 0x0, 0x16, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xef, 0xc1, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xfd, 0x0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x7f, 0xfd,
    0xee, 0xee, 0xed, 0x91, 0x0, 0x19, 0xde, 0xee, 0xee, 0xdf, 0xf7, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x11,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x9, 0xff, 0x70, 0x7, 0xff, 0x41, 0x9f, 0xee, 0xf9, 0x10, 0x5f, 0xf6, 0x7,
    0xff, 0x90, 0xf, 0xfe, 0x0, 0x7f, 0xf7, 0x0, 0xf, 0xff, 0xf0, 0x5, 0xff, 0x90, 0x0, 0xef, 0xf0, 0x5f, 0xfd, 0x3,
    0xff, 0xfb, 0xa0, 0xf, 0xef, 0xf0, 0x1f, 0xff, 0xca, 0x10, 0xdf, 0xf5, 0x9f, 0xff, 0x1, 0xac, 0xff, 0xf1, 0x2f,
    0xcd, 0xf2, 0xa, 0xbf, 0xff, 0x30, 0xff, 0xf9, 0xcf, 0xcf, 0x20, 0xa, 0xff, 0x50, 0x4f, 0x9a, 0xf4, 0x0, 0x8f, 0xf6,
    0x2, 0xfc, 0xfc, 0xee, 0x8f, 0x60, 0x8f, 0xf5, 0x0, 0x7f, 0x77, 0xf8, 0x6, 0xff, 0x70, 0x5, 0xf8, 0xee, 0xfe, 0x3f,
    0xe8, 0xff, 0x60, 0x4, 0xef, 0x22, 0xff, 0x7f, 0xf8, 0x0, 0x3e, 0xf3, 0xef, 0xff, 0x9, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x90, 0xff, 0xef, 0x0, 0x6b, 0xdd, 0xdd, 0xdb, 0x50, 0x0, 0x5, 0xbd, 0xdd, 0xdd,
    0xb6, 0x0, 0xfe, 0xbf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfb, 0x8f, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf8, 0x3f, 0xd0, 0x0, 0x0, 0x54, 0x0, 0x0, 0x0, 0x0, 0x0, 0x46,
    0x0, 0x0, 0xd, 0xf3, 0xd, 0xf4, 0x0, 0x0, 0xff, 0x60, 0x0, 0x0, 0x0, 0x6, 0xff, 0x0, 0x0, 0x4f, 0xd0, 0x5, 0xfc,
    0x0, 0x0, 0x5f, 0xfa, 0x30, 0x0, 0x3, 0xaf, 0xf5, 0x0, 0x0, 0xdf, 0x60, 0x0, 0xdf, 0x70, 0x0, 0x3, 0xdf, 0xfe, 0xcc,
    0xef, 0xfd, 0x30, 0x0, 0x8, 0xfd, 0x0, 0x0, 0x2f, 0xf4, 0x0, 0x0, 0x5, 0xbe, 0xff, 0xeb, 0x50, 0x0, 0x0, 0x5f, 0xf3,
    0x0, 0x0, 0x5, 0xff, 0x50, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x5, 0xff, 0x50, 0x0, 0x0, 0x0, 0x6f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xea, 0x63, 0x22, 0x36, 0xaf, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x8a, 0xbb, 0xa8, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+E39B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xff,
    0xff, 0xff, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xff, 0xfc, 0xbb, 0xcf, 0xff, 0xfb, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0x82, 0x0, 0x0, 0x0, 0x28, 0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xfd, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x36, 0x63, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xb0, 0x0, 0x0, 0x7f,
    0xc0, 0x2d, 0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x7f, 0xc8, 0x7c, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x10, 0x9, 0xf8, 0x0, 0x3, 0x0, 0x0, 0x5f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x90, 0xf, 0xf1, 0x0, 0x0, 0x2, 0x30, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x5f, 0xa0, 0x0, 0x0, 0x2f,
    0xf5, 0x0, 0x0, 0x0, 0x4, 0x10, 0x0, 0x0, 0xa, 0xf5, 0x9f, 0x50, 0x0, 0x0, 0x3f, 0xf6, 0x0, 0x0, 0x0, 0x9f, 0xe0,
    0x0, 0x0, 0x5, 0xf9, 0xcf, 0x10, 0x0, 0x0, 0x4, 0x60, 0x0, 0x0, 0x0, 0x9f, 0xe0, 0x0, 0x0, 0x1, 0xfc, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x20, 0x0, 0x0, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x20, 0x0, 0x8f, 0xc6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0xef, 0x0, 0x3e, 0xfe, 0x30, 0x4d, 0xff, 0xfb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfd, 0xbf, 0x20, 0xcf, 0xcf, 0xd0,
    0x0, 0x38, 0xef, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x3, 0xfb, 0x8f, 0x40, 0xfd, 0xc, 0xf1, 0x0, 0x5, 0xbf, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x7, 0xf7, 0x6, 0x1, 0xfc, 0xb, 0xf2, 0x39, 0xef, 0xfe, 0x82, 0x8f, 0x70, 0x0, 0x0, 0xc, 0xf3, 0x0,
    0x1, 0xfc, 0xb, 0xfe, 0xff, 0xfa, 0x40, 0x0, 0x7f, 0x70, 0x0, 0x0, 0x3f, 0xd0, 0x0, 0x1, 0xfc, 0xb, 0xff, 0xc6,
    0x10, 0x1, 0x7d, 0xfe, 0x10, 0x0, 0x0, 0xbf, 0x50, 0x0, 0x1, 0xfc, 0x3, 0x83, 0x0, 0x0, 0x4f, 0xff, 0xb2, 0x0, 0x0,
    0x6, 0xfd, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x82, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x1, 0xfc, 0x0,
    0x0, 0x0, 0x2, 0xfb, 0x0, 0x0, 0x0, 0x3, 0xff, 0x60, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0,
    0x7f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x5c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xc2, 0x0, 0x2, 0xbf, 0x70, 0x3, 0x9e, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0xf, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xdd, 0xdb, 0x60, 0x0, 0x5, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+E3A4 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xff,
    0xff, 0xff, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xff, 0xfc, 0xbb, 0xcf, 0xff, 0xfb, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0x82, 0x0, 0x0, 0x0, 0x28, 0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0x60, 0x0, 0x0, 0x1, 0x7a, 0x95, 0x6, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xc1, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xc1, 0x1c, 0xfd, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x2, 0xff, 0xfc, 0x4b, 0xfb, 0x0, 0xbf, 0xb0, 0x0, 0x0,
    0x7f, 0xc0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x40, 0xcf, 0x20, 0xc, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0xc,
    0xfc, 0xff, 0x20, 0x6f, 0x60, 0x2, 0xff, 0x10, 0x9, 0xf8, 0x0, 0x16, 0x86, 0x0, 0xd, 0xf3, 0x53, 0x0, 0x6f, 0x70,
    0x0, 0x8f, 0x90, 0xf, 0xf1, 0x4, 0xff, 0xff, 0xe3, 0x9, 0xf7, 0x0, 0x0, 0xbf, 0x30, 0x0, 0x1f, 0xf0, 0x5f, 0xa0,
    0x2f, 0xfb, 0x8c, 0xfe, 0x13, 0xff, 0x71, 0x29, 0xfc, 0x0, 0x0, 0xa, 0xf5, 0x9f, 0x50, 0x8f, 0x82, 0x71, 0xaf, 0x60,
    0x6f, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x5, 0xf9, 0xcf, 0x10, 0xbf, 0x2e, 0xfb, 0x4f, 0x90, 0x3, 0xad, 0xc8, 0x11, 0x8b,
    0x20, 0x1, 0xfc, 0xef, 0x0, 0xaf, 0x3c, 0xf9, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x6e, 0xff, 0x80, 0x0, 0xfe, 0xfe, 0x0,
    0x5f, 0xc1, 0x32, 0xdf, 0x40, 0x0, 0x0, 0x4d, 0xff, 0xdf, 0xb0, 0x0, 0xef, 0xfe, 0x0, 0xc, 0xff, 0xdf, 0xfa, 0x0,
    0x0, 0x2a, 0xff, 0xe5, 0x2f, 0xc0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0x9f, 0xfe, 0x80, 0x0, 0x8, 0xff, 0xf7, 0x0, 0x3f,
    0xb0, 0x0, 0xfd, 0xbf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x5, 0xef, 0xf9, 0x10, 0x0, 0x6f, 0x80, 0x3, 0xfb, 0x8f, 0x70, 0x0,
    0x0, 0x0, 0x3, 0xcf, 0xfb, 0x20, 0x0, 0x0, 0xbf, 0x40, 0x7, 0xf8, 0x3f, 0xc0, 0x0, 0x0, 0x1, 0xaf, 0xfd, 0x40, 0x0,
    0x0, 0x4, 0xfd, 0x0, 0xc, 0xf3, 0xd, 0xf3, 0x0, 0x0, 0x2f, 0xff, 0xfa, 0x44, 0xab, 0x70, 0x2e, 0xf5, 0x0, 0x3f,
    0xd0, 0x6, 0xfc, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xef, 0x90, 0x0, 0xcf, 0x60, 0x0, 0xdf, 0x60, 0x0,
    0x8, 0xfc, 0x34, 0xef, 0x53, 0xbf, 0xf9, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x3f, 0xf3, 0x0, 0x9, 0xf4, 0x0, 0x56, 0x0, 0x3f,
    0xb0, 0x0, 0x3f, 0xf3, 0x0, 0x0, 0x6, 0xff, 0x40, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x3, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x7f, 0xf7, 0x6, 0xf9, 0x0, 0x0, 0x0, 0x8f, 0x70, 0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x51, 0xff, 0x40,
    0x0, 0x3, 0xff, 0x25, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28, 0x10, 0x7f, 0xf8, 0x32, 0x7f, 0xf7, 0x1, 0x83, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x28, 0xbb, 0x82, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+E409 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xff,
    0xff, 0xff, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xfc, 0xbb, 0xcf, 0xff, 0xfb, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0x82, 0x0, 0x0, 0x0, 0x28, 0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xfd, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xb0, 0x0, 0x0, 0x7f, 0xd0,
    0x1, 0x20, 0x0, 0x0, 0x0, 0x2, 0x7a, 0x84, 0x0, 0xd, 0xf7, 0x0, 0x1, 0xff, 0x21, 0xbf, 0xff, 0xa0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xb0, 0x2, 0xff, 0x20, 0x9, 0xf8, 0xd, 0xfe, 0xef, 0xfc, 0x0, 0x4, 0xff, 0x87, 0xff, 0xf8, 0x0, 0x7f,
    0x90, 0xf, 0xf1, 0x7f, 0xb0, 0x8f, 0xff, 0x50, 0xb, 0xf6, 0x4, 0xff, 0xff, 0x0, 0xf, 0xf0, 0x5f, 0xa0, 0xbf, 0x30,
    0x7, 0x9f, 0x90, 0xf, 0xf0, 0x0, 0x66, 0xbf, 0x30, 0x9, 0xf5, 0x9f, 0x40, 0xcf, 0x20, 0x0, 0x4f, 0xa0, 0xf, 0xe0,
    0x0, 0x0, 0x9f, 0x40, 0x4, 0xf9, 0xcf, 0x10, 0xaf, 0xff, 0xff, 0xff, 0x80, 0xe, 0xff, 0xff, 0xff, 0xff, 0x30, 0x1,
    0xfc, 0xef, 0x0, 0x1b, 0xdd, 0xdd, 0xda, 0x10, 0x4, 0xcd, 0xdd, 0xdd, 0xc7, 0x0, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x10, 0x0, 0x0, 0xef, 0xef, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0xfe,
    0xbf, 0x20, 0x9f, 0xdc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xdf, 0xa0, 0x3, 0xfb, 0x8f, 0x70, 0x6f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x70, 0x6, 0xf8, 0x3f, 0xc0, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x3, 0x8b, 0xa6,
    0x0, 0xdf, 0x40, 0xc, 0xf3, 0xd, 0xf3, 0xa, 0xf7, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xea, 0xfd, 0x0, 0x3f, 0xd0, 0x5,
    0xfc, 0x2, 0xff, 0x40, 0x0, 0x8, 0xfe, 0x63, 0x4a, 0xff, 0xf5, 0x0, 0xbf, 0x60, 0x0, 0xdf, 0x70, 0x5f, 0xf6, 0x0,
    0x2f, 0xe2, 0x0, 0x5, 0xff, 0xa0, 0x7, 0xfd, 0x0, 0x0, 0x2f, 0xf4, 0x5, 0xff, 0xc6, 0x9f, 0x60, 0x4, 0xbf, 0xfa,
    0x0, 0x4f, 0xf3, 0x0, 0x0, 0x5, 0xff, 0x40, 0x2c, 0xff, 0xff, 0xee, 0xff, 0xff, 0x70, 0x4, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x6f, 0xf8, 0x0, 0x49, 0xde, 0xff, 0xec, 0x71, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xd5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5d, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xea, 0x63, 0x22, 0x36, 0xae, 0xff, 0xb2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x8a, 0xbb, 0xa8, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x48, 0xdf, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xae, 0xff, 0xfc, 0x72, 0xef, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x6b, 0xff, 0xff, 0xb6, 0x10, 0x0, 0xdf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0xcf, 0xff, 0xe9, 0x50, 0x0, 0x0,
    0x0, 0xdf, 0x0, 0x0, 0x0, 0x0, 0x2, 0x9e, 0xff, 0xfd, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xff, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf2, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xff, 0xff, 0xa5, 0xef, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x38, 0xdf,
    0xff, 0xe9, 0x40, 0x0, 0xdf, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x5, 0x9e, 0xff, 0xfc, 0x72, 0x0, 0x0, 0x0, 0xdf, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xfc, 0xff, 0xff, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xe9,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x21, 0x0, 0xdf, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x8, 0xef, 0xff,
    0xd6, 0xef, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xfd, 0xbd, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf2, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x20, 0x0, 0x3d, 0xff, 0x0, 0x16, 0xac, 0xb7, 0x1b, 0xf2, 0x0, 0x0, 0x0, 0x1f,
    0xe0, 0x0, 0x0, 0x2, 0xff, 0x4, 0xef, 0xff, 0xff, 0xfe, 0xf2, 0x0, 0x0, 0x0, 0x3f, 0xb0, 0x0, 0x0, 0x0, 0xff, 0x3f,
    0xfa, 0x41, 0x39, 0xff, 0xf2, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x4, 0xfc, 0xbf, 0x70, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x0, 0x9, 0xfd, 0x40, 0x0, 0x6f, 0xf5, 0xff, 0x0, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xef,
    0xff, 0x80, 0xef, 0x0, 0x0, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbe, 0xfe, 0xa3, 0x0, 0xaf, 0x80, 0x0, 0x0,
    0x6f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfb, 0x42, 0x49, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xab, 0xa7, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F007 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28, 0xef, 0xff, 0xb5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xdc, 0xef, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf7, 0x10, 0x0, 0x4d, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf3, 0x0, 0x0, 0x0, 0xc, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf9, 0x0, 0x0, 0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xfd, 0x73, 0x25, 0xaf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x8a, 0xb9, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x33, 0x33, 0x33, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5e, 0xff, 0xec, 0xaa, 0xaa, 0xaa, 0xbd, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xfa, 0x30, 0x0, 0x0, 0x0, 0x0, 0x2, 0x8f, 0xfc, 0x10, 0x0, 0x0, 0x8f, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xfc, 0x0, 0x0, 0x4f, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf8, 0x0, 0xd, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf2, 0x4, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x80, 0x9f, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfd, 0xd, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf1, 0xee, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x2e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x4c, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xc6, 0x0,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6, 0x0, 0x0,
    0x0, 0xd, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x60, 0x0, 0x0, 0x0, 0x9, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x3f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd1, 0x0, 0x0, 0x3, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x4f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xd1, 0x4, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x5f, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xc5, 0xe, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xf6, 0x2, 0xef, 0x90, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x90, 0x0, 0x3e, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xf9, 0x0, 0x0, 0x3, 0xef, 0x90, 0x0, 0x0, 0x2, 0xef, 0x90, 0x0, 0x0, 0x0, 0x3e, 0xf8,
    0x0, 0x0, 0x2e, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x80, 0x2, 0xef, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf8,
    0x2e, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xfb, 0x4f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xb0, 0x4, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xfb, 0x0, 0x0, 0x4f, 0xf6, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xb0, 0x0, 0x0, 0x4, 0xff, 0x60, 0x0, 0x0, 0x1c, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf6, 0x0, 0x0,
    0xcf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x50, 0xc, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4, 0xe, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0x70, 0x0, 0xf, 0xd0, 0x0, 0x8, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0xe0, 0x0, 0xf, 0xd0, 0x0, 0x1f, 0xfb,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xfb, 0x20, 0x0, 0xf, 0xd0, 0x0, 0x3, 0xcf, 0xd2, 0x0, 0x0, 0x0, 0x3, 0xff, 0x70,
    0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x9, 0xfe, 0x20, 0x0, 0x0, 0x1e, 0xf6, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x8f,
    0xd0, 0x0, 0x0, 0xbf, 0x90, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0xb, 0xf8, 0x0, 0x4, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xd0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x10, 0xb, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x80,
    0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xe0, 0x5f, 0x90, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf2, 0x8f, 0x60, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf5, 0x9f,
    0x40, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf7, 0xaf, 0x30, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xf8, 0xaf, 0x40, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf7, 0x8f, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x7, 0x50, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf5, 0x5f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf2, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xe0, 0xb, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x90, 0x4, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0x20, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x2f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xd0, 0x0, 0x0, 0x4, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x5f, 0xfa, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3, 0xcf, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x3, 0xdf, 0xf9, 0x40, 0x0, 0x0,
    0x15, 0xaf, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xec, 0xce, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xae, 0xff, 0xff, 0xda, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F012 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x60, 0x0, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x10, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc8, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0,
    0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x1, 0x30, 0x0, 0x0, 0x1f, 0xc0,
    0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb,
    0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x85, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc,
    0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc1, 0xfc, 0x0, 0x0, 0xb,
    0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc,
    0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20,
    0x0, 0x1, 0xfc, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc1, 0xfc,
    0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x9, 0x60, 0x0, 0x0, 0x5a, 0x0,
    0x0, 0x0, 0x96, 0x0, 0x0, 0x5, 0xa0, 0x0, 0x0, 0x9, 0x60,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf,
    0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0xcc, 0xdf, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0xe, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xa0, 0x0, 0xa, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x60, 0x0, 0x6, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xb7, 0x6e, 0xfe, 0x0, 0x0, 0x0, 0xdf, 0xe6, 0x7b, 0xff, 0x90, 0x0, 0x0, 0x6f, 0xef, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0x0, 0x1a, 0xff, 0xff, 0xfe, 0xf6, 0x0, 0x1, 0xff, 0x30, 0x37, 0x73, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x27, 0x73, 0x2, 0xff, 0x10, 0x8, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x80, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x2, 0x55, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0xd, 0xf6, 0x0, 0x0, 0x0, 0x1, 0xbf,
    0xff, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x6f, 0xd0, 0x4, 0xff, 0x50, 0x0, 0x0, 0x1d, 0xfd, 0x88, 0xdf, 0xd1, 0x0, 0x0, 0x5,
    0xff, 0x40, 0x0, 0x5f, 0xf3, 0x0, 0x0, 0x9f, 0xa0, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x3f, 0xf5, 0x0, 0x0, 0x6, 0xf9, 0x0,
    0x0, 0xff, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x9f, 0x60, 0x0, 0x0, 0x3, 0xfa, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0xcf,
    0x10, 0x0, 0x9f, 0x30, 0x0, 0x0, 0x3, 0xfa, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x9f, 0x30, 0x0, 0x0,
    0xa, 0xf8, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x4, 0xfc, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x0, 0xaf, 0xd0, 0x0, 0x0, 0x4f, 0xf6,
    0x0, 0x6f, 0xf4, 0x0, 0x0, 0xd, 0xfa, 0x0, 0x8, 0xfd, 0x10, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x1,
    0xdf, 0x80, 0xe, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xdd, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf0, 0xd, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x5, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x50, 0x0, 0xdf, 0x75, 0x9d, 0xe9, 0x10, 0x0, 0x0, 0x0, 0x1, 0x9e, 0xd9, 0x57, 0xfd, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x4, 0xa9, 0x62, 0x8, 0xff, 0x20,
    0x0, 0x2, 0xff, 0x80, 0x26, 0x9a, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x80, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xc0, 0x0, 0xc, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf4, 0x22, 0x4f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7a, 0xbb, 0xa7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9, 0x9f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x70, 0x7, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf5, 0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x40, 0x0, 0x0, 0x4, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0, 0x0, 0x8, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x90, 0x0, 0x0, 0xaf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xfb, 0x0, 0xa, 0xfe, 0x4f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf4, 0xdf,
    0xb0, 0x8, 0xc1, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x1b, 0x90, 0x0, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0x0, 0x2, 0x22, 0x22, 0x20, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xc, 0xfb,
    0xbb, 0xbb, 0xbf, 0xc0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x1f,
    0xc0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xb,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0,
    0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x1f, 0xc0,
    0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf3, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x2f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfe, 0x51, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x14, 0xdf, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xac, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xca, 0x50, 0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xee, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xa0, 0x0, 0x0, 0xee, 0x0, 0x0, 0xa, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xfa, 0x0, 0x0, 0xee, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xb0, 0x0, 0xee, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfb, 0x0, 0xee, 0x0, 0xbf, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xb0, 0xee, 0xb, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7a, 0xbb, 0xbb, 0x60, 0x1c, 0xfb, 0xff,
    0xbf, 0xc1, 0x6, 0xbb, 0xbb, 0xa7, 0x0, 0xd, 0xff, 0xff, 0xff, 0xf6, 0x1, 0xcf, 0xff, 0xfc, 0x10, 0x6f, 0xff, 0xff,
    0xff, 0xd0, 0x9f, 0xa3, 0x22, 0x22, 0x22, 0x0, 0x1c, 0xff, 0xc1, 0x0, 0x22, 0x22, 0x22, 0x3a, 0xf9, 0xdf, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xcc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfd, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x30, 0x0, 0xef, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0, 0xdf, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf3, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0x40, 0x0, 0xdf, 0xef, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x9f,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19, 0xf9, 0x1e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x1, 0x9c, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xc9, 0x10,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xd3, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xc1, 0x1f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xb0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x90,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x70, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x4, 0xbd,
    0xdd, 0xde, 0xff, 0x50, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x0, 0x0, 0x1, 0xfc,
    0x0, 0x4, 0xd5, 0x0, 0xcf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x4f, 0xf3, 0xf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x7f, 0xb0, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0,
    0xef, 0x1f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xa, 0xf3, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x2f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x2f, 0xe0, 0xef,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x1c, 0xf8, 0xa, 0xfc, 0x87, 0x77, 0x76, 0x0, 0x0, 0x0, 0x1,
    0xfc, 0x0, 0x6, 0xfc, 0x0, 0x1c, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x16, 0x0, 0x0, 0x3, 0x55,
    0x55, 0x6d, 0xfc, 0x10, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfe, 0x20, 0x0, 0x1f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfe, 0x30, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x50, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x61, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xcc, 0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfd, 0x3f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xc1, 0x1f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xfb, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x9f,
    0x30, 0x0, 0xc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x90, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x6f, 0xf3, 0x0,
    0x2, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x0, 0x0, 0x9f,
    0x70, 0x4, 0xbd, 0xdd, 0xde, 0xff, 0x50, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x80, 0x0, 0x2f, 0xd0,
    0x5f, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x4d, 0x50, 0x0, 0x1f, 0xf1, 0x0, 0xd, 0xf1, 0xcf,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x4f, 0xf3, 0x0, 0xa, 0xf6, 0x0, 0x9, 0xf5, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x7, 0xfb, 0x0, 0x4, 0xfa, 0x0, 0x6, 0xf8, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xef, 0x10, 0x2, 0xfc, 0x0, 0x4, 0xf9, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0xaf, 0x30, 0x0, 0xfd, 0x0, 0x3, 0xfa, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0,
    0x0, 0x0, 0xbf, 0x20, 0x1, 0xfd, 0x0, 0x3, 0xfa, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x2,
    0xfe, 0x0, 0x3, 0xfb, 0x0, 0x4, 0xf9, 0xef, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x1c, 0xf8, 0x0,
    0x7, 0xf8, 0x0, 0x7, 0xf6, 0xaf, 0xc8, 0x77, 0x77, 0x60, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x6f, 0xc0, 0x0, 0xd, 0xf3,
    0x0, 0xb, 0xf3, 0x1c, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x16, 0x0, 0x0, 0x5f, 0xc0, 0x0, 0xf,
    0xf0, 0x0, 0x35, 0x55, 0x56, 0xdf, 0xc1, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x1, 0xef, 0x30, 0x0, 0x6f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xfe, 0x20, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x1d, 0xf9, 0x0, 0x0, 0xdf, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xe3, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0xaf, 0xa0, 0x0, 0x7, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x50, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x48, 0x0, 0x0, 0x1f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf6,
    0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x9f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xcc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7b, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x0, 0x1, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x10, 0x0, 0x5, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x50, 0x4f, 0xfc, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xcf, 0xf4, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfc,
    0xfe, 0x0, 0x0, 0x6, 0x96, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0xaf, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x1, 0xff, 0x9f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xfe, 0x0, 0x2, 0xfc, 0xc, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0xdf, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x2c, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1, 0x41,
    0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x50, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x9f, 0xf5, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf5,
    0x4, 0xff, 0x50, 0xef, 0xfe, 0x0, 0x0, 0x58, 0x50, 0x0, 0x0, 0x0, 0x5, 0xff, 0x50, 0x0, 0x5f, 0xf5, 0xef, 0xfe, 0x0,
    0x8, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x5f, 0xf5, 0x0, 0x0, 0x5, 0xff, 0xff, 0xfe, 0x0, 0x9f, 0xe5, 0xef, 0x90, 0x0, 0x5,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xfe, 0x9, 0xfe, 0x20, 0x2e, 0xf9, 0x0, 0x5f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xfe, 0x9f, 0xe2, 0x0, 0x2, 0xef, 0x95, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x2e, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x7, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0x20, 0x0, 0x0, 0x0, 0x5f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xef, 0x0, 0x0, 0x4, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0xaf, 0x80,
    0x0, 0x4f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfa, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x1, 0x9d, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xd9, 0x10,

    /* U+F044 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xef, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfe, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfa, 0x10, 0x4f, 0xf3, 0x0, 0x28, 0xbb, 0xbb, 0xbb, 0xbb,
    0x80, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x5, 0xf9, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0xc, 0xff, 0xe2,
    0x0, 0x2, 0xfb, 0x4f, 0xf7, 0x32, 0x22, 0x22, 0x22, 0x0, 0x0, 0x0, 0xcf, 0x99, 0xfe, 0x20, 0x9, 0xf9, 0xbf, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf9, 0x0, 0x9f, 0xe2, 0xaf, 0xf2, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xa0, 0x0, 0xa, 0xff, 0xff, 0x40, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xf4, 0x0,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xa0, 0x0, 0x0, 0xa, 0xff, 0x40, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xfa, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xa0, 0x0, 0x0, 0xa, 0xff, 0x40,
    0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfb, 0x0, 0x0, 0x0, 0x9f, 0xf3, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xb0, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x9f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0x0, 0x8f, 0x70, 0x0, 0x0, 0x8f, 0xf3, 0x0, 0x0, 0x24, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0xcf, 0x30, 0x0, 0x8, 0xff,
    0x30, 0x0, 0x0, 0xaf, 0x10, 0x0, 0xfd, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x8f, 0xf3, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0,
    0xfd, 0x0, 0x0, 0x3, 0xfc, 0x26, 0x9e, 0xff, 0x30, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0xfd, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xc2, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0xfd, 0x0, 0x0, 0x8, 0xff, 0xc9, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x20, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x20, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x10, 0x0, 0xcf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x5f, 0xe6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x5d, 0xf7, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x4a, 0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xa5, 0x0, 0x0, 0x0,

    /* U+F048 "" */
    0x5, 0x30, 0x0, 0x0, 0x0, 0x0, 0x4, 0x71, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfb, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xfe, 0x1f, 0xc0, 0x0, 0x0, 0x2, 0xdf, 0xd2, 0xfe, 0x1f, 0xc0, 0x0, 0x0, 0x4e, 0xfb, 0x10, 0xfe, 0x1f, 0xc0,
    0x0, 0x6, 0xff, 0x80, 0x0, 0xfe, 0x1f, 0xc0, 0x0, 0x9f, 0xf5, 0x0, 0x0, 0xfe, 0x1f, 0xc0, 0x1c, 0xfe, 0x30, 0x0,
    0x0, 0xfe, 0x1f, 0xc3, 0xef, 0xc1, 0x0, 0x0, 0x0, 0xfe, 0x1f, 0xef, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x1f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x1f, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xfe, 0x1f, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x1f, 0xda, 0xff, 0x40, 0x0, 0x0, 0x0, 0xfe, 0x1f, 0xc0, 0x7f,
    0xf7, 0x0, 0x0, 0x0, 0xfe, 0x1f, 0xc0, 0x4, 0xff, 0xa0, 0x0, 0x0, 0xfe, 0x1f, 0xc0, 0x0, 0x2d, 0xfd, 0x20, 0x0,
    0xfe, 0x1f, 0xc0, 0x0, 0x1, 0xbf, 0xe4, 0x0, 0xfe, 0x1f, 0xc0, 0x0, 0x0, 0x8, 0xff, 0x60, 0xfe, 0x1f, 0xc0, 0x0,
    0x0, 0x0, 0x6f, 0xf9, 0xfe, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xfe, 0xd, 0x80, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xf6,

    /* U+F04B "" */
    0x1, 0x54, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xfd, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x9b, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xe0, 0x5, 0xef, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xfe, 0x0, 0x0, 0x8f, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xe0, 0x0, 0x0, 0x2c, 0xff, 0x91, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x5, 0xef, 0xe6, 0x0, 0x0, 0x0, 0x0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x1, 0x9f,
    0xfc, 0x20, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0xff, 0x80, 0x0, 0x0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xef, 0xe5, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x9f, 0xfb, 0x20, 0xf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3d, 0xff, 0x70, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf9, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x7f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xef, 0xe1, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xa1, 0xf, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd, 0x30, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xef, 0xf7, 0x0, 0x0, 0xf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xa1, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfd, 0x40, 0x0, 0x0,
    0x0, 0xf, 0xe0, 0x0, 0x0, 0x4, 0xdf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x1a, 0xff, 0xb2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xe0, 0x0, 0x7f, 0xfe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x24, 0xdf, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xdc, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x5, 0xab, 0xbb, 0x70, 0x0, 0x3, 0x9b, 0xbb, 0x93, 0x7, 0xff, 0xff, 0xff, 0xa0, 0x3, 0xff, 0xff, 0xff, 0xf4, 0xdf,
    0x42, 0x23, 0xef, 0x10, 0xaf, 0x72, 0x22, 0x6f, 0xbf, 0xe0, 0x0, 0xa, 0xf3, 0xc, 0xf1, 0x0, 0x0, 0xfd, 0xfe, 0x0,
    0x0, 0xaf, 0x30, 0xcf, 0x10, 0x0, 0xf, 0xdf, 0xe0, 0x0, 0xa, 0xf3, 0xc, 0xf1, 0x0, 0x0, 0xfd, 0xfe, 0x0, 0x0, 0xaf,
    0x30, 0xcf, 0x10, 0x0, 0xf, 0xdf, 0xe0, 0x0, 0xa, 0xf3, 0xc, 0xf1, 0x0, 0x0, 0xfd, 0xfe, 0x0, 0x0, 0xaf, 0x30, 0xcf,
    0x10, 0x0, 0xf, 0xdf, 0xe0, 0x0, 0xa, 0xf3, 0xc, 0xf1, 0x0, 0x0, 0xfd, 0xfe, 0x0, 0x0, 0xaf, 0x30, 0xcf, 0x10, 0x0,
    0xf, 0xdf, 0xe0, 0x0, 0xa, 0xf3, 0xc, 0xf1, 0x0, 0x0, 0xfd, 0xfe, 0x0, 0x0, 0xaf, 0x30, 0xcf, 0x10, 0x0, 0xf, 0xdf,
    0xe0, 0x0, 0xa, 0xf3, 0xc, 0xf1, 0x0, 0x0, 0xfd, 0xfe, 0x0, 0x0, 0xaf, 0x30, 0xcf, 0x10, 0x0, 0xf, 0xdf, 0xe0, 0x0,
    0xa, 0xf3, 0xc, 0xf1, 0x0, 0x0, 0xfd, 0xfe, 0x0, 0x0, 0xaf, 0x30, 0xcf, 0x10, 0x0, 0xf, 0xdf, 0xe0, 0x0, 0xa, 0xf3,
    0xc, 0xf1, 0x0, 0x0, 0xfd, 0xfe, 0x0, 0x0, 0xaf, 0x30, 0xcf, 0x10, 0x0, 0xf, 0xdf, 0xe0, 0x0, 0xa, 0xf3, 0xc, 0xf1,
    0x0, 0x0, 0xfd, 0xef, 0x10, 0x0, 0xcf, 0x20, 0xbf, 0x30, 0x0, 0x2f, 0xc9, 0xff, 0xee, 0xef, 0xd0, 0x6, 0xff, 0xee,
    0xef, 0xf7, 0x9, 0xff, 0xff, 0xb2, 0x0, 0x7, 0xef, 0xff, 0xe8, 0x0,

    /* U+F04D "" */
    0x0, 0x6a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xb9, 0x40, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x9f, 0xa3, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x24, 0xdf, 0x4e, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf9, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xbf, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xbf, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xbf, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xbf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xbe, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfa, 0xbf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x62, 0xff, 0xed, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xde, 0xff, 0xd0, 0x2, 0xbe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x90, 0x0,

    /* U+F051 "" */
    0x2, 0x73, 0x0, 0x0, 0x0, 0x0, 0x0, 0x43, 0xe, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0xee, 0x1f, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0xfe, 0x1f, 0xc4, 0xef, 0xb1, 0x0, 0x0, 0x0, 0xfe, 0x1f, 0xc0, 0x2d, 0xfd, 0x20, 0x0, 0x0, 0xfe, 0x1f,
    0xc0, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0xfe, 0x1f, 0xc0, 0x0, 0x7, 0xff, 0x70, 0x0, 0xfe, 0x1f, 0xc0, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0xfe, 0x1f, 0xc0, 0x0, 0x0, 0x2, 0xdf, 0xd1, 0xfe, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x1b, 0xfe, 0xfe, 0x1f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xfe, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xfe, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x6f, 0xf8, 0xfe, 0x1f, 0xc0,
    0x0, 0x0, 0x9, 0xff, 0x50, 0xfe, 0x1f, 0xc0, 0x0, 0x1, 0xcf, 0xe3, 0x0, 0xfe, 0x1f, 0xc0, 0x0, 0x3e, 0xfc, 0x10,
    0x0, 0xfe, 0x1f, 0xc0, 0x5, 0xff, 0x90, 0x0, 0x0, 0xfe, 0x1f, 0xc0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0xfe, 0x1f, 0xdb,
    0xfe, 0x40, 0x0, 0x0, 0x0, 0xfe, 0xf, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x8, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xba,

    /* U+F060 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xb1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xa, 0xff, 0xdc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xa0, 0x0, 0xaf, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F061 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x33, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf4,
    0x0, 0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x7f, 0xf4, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x8c, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F062 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x9b, 0xf6, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xf9, 0xb,
    0xf2, 0x3f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x90, 0xb, 0xf2, 0x3, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf9, 0x0, 0xb, 0xf2, 0x0, 0x3f, 0xf6, 0x0, 0x0, 0x0, 0x2, 0xef, 0x90, 0x0, 0xb, 0xf2, 0x0, 0x3, 0xff, 0x60, 0x0,
    0x0, 0x2e, 0xf9, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x3f, 0xf6, 0x0, 0x2, 0xef, 0x80, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0,
    0x3, 0xff, 0x60, 0xd, 0xf8, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x3f, 0xf4, 0x9, 0x70, 0x0, 0x0, 0x0, 0xb,
    0xf2, 0x0, 0x0, 0x0, 0x3, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F063 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x10, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x30, 0xf, 0xe2, 0x0,
    0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0xa, 0xf5, 0x8, 0xfe, 0x20, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0xaf, 0xd1, 0x0,
    0x8f, 0xe2, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x8, 0xfe, 0x20, 0x0, 0xb, 0xf2, 0x0, 0x0, 0xaf,
    0xd1, 0x0, 0x0, 0x0, 0x8f, 0xe2, 0x0, 0xb, 0xf2, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x8, 0xfe, 0x20, 0xb, 0xf2,
    0x0, 0xaf, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe2, 0xb, 0xf2, 0xa, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xfe, 0x2b, 0xf2, 0xaf, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xed, 0xfc, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xdd, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfc, 0x0, 0xbf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf2, 0x0, 0x2f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x90, 0x0, 0x8, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x10, 0x0, 0x0, 0xef, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf6, 0x0, 0x0, 0x0, 0x5f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xc0, 0x0, 0x99, 0x0, 0xb, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x30, 0x0, 0xee, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xfa, 0x0, 0x0, 0xff, 0x0, 0x0, 0x9f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0x0, 0x0, 0xff, 0x0, 0x0,
    0x1e, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x70, 0x0, 0x0, 0xff, 0x0, 0x0, 0x6, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xfd, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0xcf, 0x50, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf4, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x9, 0xf8, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x20, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0xa, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x33,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xa0, 0x0, 0x0, 0x4f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x0,
    0x0, 0xdf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfd, 0x0, 0x7, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x70, 0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf1, 0x9f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x77, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf9, 0xef, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0xef, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xfe, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x6, 0xce,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xec, 0x60,

    /* U+F075 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xad,
    0xff, 0xff, 0xff, 0xda, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x9f, 0xff, 0xfe, 0xcb, 0xbc, 0xef, 0xff,
    0xf9, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf9, 0x51, 0x0, 0x0, 0x0, 0x15, 0x9f, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xc, 0xfd, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xc0, 0x0, 0x0, 0xb, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf,
    0xb0, 0x0, 0x6, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0x60, 0x0, 0xef, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf5, 0xa, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0xdf, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfd, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf0, 0xef, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xc,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xd0, 0x9f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf9, 0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x40, 0xc, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x4f, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xf4, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xf8, 0x0, 0x0, 0x5, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfa, 0x0, 0x0, 0x0, 0x8f,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xbf, 0xf8, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x0, 0x6d, 0xc8, 0x41, 0x0, 0x0,
    0x14, 0x8d, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x7, 0xfa, 0x4, 0xcf, 0xff, 0xff, 0xff, 0xee, 0xff, 0xff, 0xfc, 0x60, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xad, 0xff, 0xd3, 0x15, 0x9c, 0xdf, 0xfe, 0xca, 0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xfd, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xca, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0AC "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xff,
    0xff, 0xff, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xff, 0xcc, 0xff, 0xff, 0xfb, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0xcf, 0xf4, 0x0, 0x4f, 0xfc, 0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfe,
    0x61, 0xef, 0x40, 0x0, 0x4, 0xff, 0x26, 0xef, 0xc1, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xc1, 0x8, 0xf9, 0x0, 0x0, 0x0, 0x9f,
    0x90, 0x1c, 0xfd, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0xbf, 0xb0, 0x0, 0x0,
    0x7f, 0xc0, 0x0, 0x6f, 0x90, 0x0, 0x0, 0x0, 0xa, 0xf6, 0x0, 0xc, 0xf7, 0x0, 0x1, 0xff, 0x10, 0x0, 0xbf, 0x40, 0x0,
    0x0, 0x0, 0x4, 0xfb, 0x0, 0x1, 0xff, 0x20, 0x9, 0xf7, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x7f,
    0x90, 0xf, 0xfe, 0xee, 0xee, 0xff, 0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xee, 0xee, 0xef, 0xf0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x9f, 0x40, 0x0, 0x7, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0x60, 0x0, 0x4, 0xf9, 0xcf, 0x10, 0x0, 0x8, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x0, 0x1,
    0xfc, 0xef, 0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x90, 0x0, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x9, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x90, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x90,
    0x0, 0x0, 0xff, 0xef, 0x0, 0x0, 0x8, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x80, 0x0, 0x0, 0xfe, 0xbf, 0x30, 0x0,
    0x7, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x70, 0x0, 0x3, 0xfb, 0x8f, 0xdb, 0xbb, 0xbd, 0xfd, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xdf, 0xdb, 0xbb, 0xbd, 0xf8, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0xd, 0xf7, 0x22, 0x22, 0xff, 0x22, 0x22, 0x22, 0x22, 0x22, 0xff, 0x22, 0x22, 0x7f, 0xd0, 0x5,
    0xfd, 0x0, 0x0, 0xcf, 0x30, 0x0, 0x0, 0x0, 0x3, 0xfc, 0x0, 0x0, 0xdf, 0x60, 0x0, 0xdf, 0x70, 0x0, 0x8f, 0x70, 0x0,
    0x0, 0x0, 0x7, 0xf8, 0x0, 0x8, 0xfd, 0x0, 0x0, 0x2f, 0xf5, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x0, 0xe, 0xf2, 0x0, 0x5f,
    0xf3, 0x0, 0x0, 0x5, 0xff, 0x50, 0xc, 0xf5, 0x0, 0x0, 0x0, 0x5f, 0xc0, 0x5, 0xff, 0x50, 0x0, 0x0, 0x0, 0x6f, 0xf9,
    0x4, 0xfd, 0x0, 0x0, 0x0, 0xdf, 0x40, 0x9f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xd6, 0xbf, 0xa0, 0x0, 0xa, 0xfb,
    0x6d, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xff, 0xfb, 0x34, 0xcf, 0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x8a,
    0xbb, 0xa8, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x8e, 0xff, 0xff, 0xfa, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xfc, 0x99, 0xbe, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x81, 0x0, 0x0, 0x5, 0xdf, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xf4, 0x0, 0x0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x70, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xf9, 0x0, 0x0, 0x0, 0x3, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xa0, 0x0, 0x0,
    0x0, 0x3f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfa, 0x0, 0x0, 0x0, 0x5, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xb0, 0x0, 0x0, 0x0, 0x7f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0xb, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x60,
    0x0, 0x0, 0x9f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf9, 0x0, 0x2e, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xf5, 0xc, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xe0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x2b, 0xfe, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdf, 0xe0, 0x1c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0x40,
    0x1, 0xe7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x43, 0xbf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xab, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F118 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xef,
    0xff, 0xfe, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xfd, 0xcc, 0xdf, 0xff, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x83, 0x0, 0x0, 0x0, 0x38, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xfc, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xb0, 0x0, 0x0, 0x7f,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x10, 0x9, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x90, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x5f, 0xa0, 0x0, 0x0, 0x2, 0x50, 0x0, 0x0,
    0x0, 0x5, 0x20, 0x0, 0x0, 0xa, 0xf5, 0x9f, 0x50, 0x0, 0x0, 0xe, 0xf9, 0x0, 0x0, 0x0, 0x9f, 0xe0, 0x0, 0x0, 0x5,
    0xf9, 0xcf, 0x10, 0x0, 0x0, 0xe, 0xf8, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0x1, 0xfc, 0xef, 0x0, 0x0, 0x0, 0x1,
    0x40, 0x0, 0x0, 0x0, 0x4, 0x10, 0x0, 0x0, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfd, 0xbf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xfb, 0x8f, 0x70, 0x0, 0x0, 0x54, 0x0, 0x0, 0x0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x7, 0xf8, 0x3f, 0xc0,
    0x0, 0x0, 0xff, 0x60, 0x0, 0x0, 0x0, 0x6, 0xff, 0x0, 0x0, 0xc, 0xf3, 0xd, 0xf3, 0x0, 0x0, 0x5f, 0xfa, 0x30, 0x0,
    0x3, 0xaf, 0xf5, 0x0, 0x0, 0x3f, 0xd0, 0x6, 0xfc, 0x0, 0x0, 0x3, 0xdf, 0xfe, 0xcc, 0xef, 0xfd, 0x30, 0x0, 0x0, 0xbf,
    0x60, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x5, 0xbf, 0xff, 0xfc, 0x60, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x6, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xe9, 0x63, 0x22, 0x36, 0x9e,
    0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x8a, 0xbb, 0xa8, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x25, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5b, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x8e, 0xff, 0xee, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xbf, 0xff, 0xb4, 0xe, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x17, 0xdf, 0xfe, 0x82, 0x0, 0x6f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3a, 0xff, 0xfc, 0x50, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xff, 0xf9, 0x20, 0x0,
    0x0, 0x3, 0xfd, 0x0, 0x0, 0x0, 0x3, 0x9f, 0xff, 0xc6, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf7, 0x0, 0x0, 0x6, 0xcf, 0xff,
    0xa3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x5, 0xef, 0xfd, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xa0, 0x0,
    0xf, 0xfb, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x30, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x3, 0xfd, 0x0, 0x0, 0x3, 0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdf, 0xe0, 0x0, 0xa, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf0, 0x0, 0x7f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf0, 0x0, 0xdf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf0,
    0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf0, 0xb, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf0, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf0, 0x8f, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf0, 0xef, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf5, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfb, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xde, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x6a, 0xcf, 0xff, 0xff, 0xff, 0xeb, 0x84, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x7d, 0xff, 0xff, 0xec, 0xbb, 0xbc, 0xdf, 0xff, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0xff,
    0xfc, 0x84, 0x10, 0x0, 0x0, 0x0, 0x2, 0x6a, 0xef, 0xfd, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfe, 0x72, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4b, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x8, 0xff, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4d, 0xfd, 0x20, 0x0, 0xa, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x40, 0xb, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x30, 0xcb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0xad, 0xef, 0xfe, 0xc9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xcf, 0xff, 0xff, 0xee, 0xff, 0xff, 0xe8, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3b, 0xff, 0xd7, 0x30, 0x0, 0x0, 0x25, 0xaf, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfd, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x54, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xb9, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xfa, 0x35, 0xef, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x0, 0x7, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x7f,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x80, 0x3e, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x9d, 0xc6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F1F8 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x11, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xef, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xcc, 0xcc, 0xcc, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0x50, 0x0, 0x0, 0x2, 0xff, 0x30, 0x0, 0x0, 0x0, 0x7b, 0xbb, 0xbb, 0xdf, 0xfb, 0xbb, 0xbb, 0xbb, 0xbe, 0xfe,
    0xbb, 0xbb, 0xba, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x2, 0xfe,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0xbf, 0x51, 0x0, 0xe, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xf2, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x10, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x0,
    0x0, 0xa, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x8f, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x7, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0x0, 0x6f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfa, 0x0, 0x0, 0x5, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0x90, 0x0, 0x0, 0x4f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x0, 0x3, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x60, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf5, 0x0, 0x0, 0x0,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x40, 0x0, 0x0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf3, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x0, 0xd, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x0, 0xcf, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0,
    0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0xaf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x8, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x7f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfa, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0x60,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xad, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdb, 0x70, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x0, 0x4a, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xa4, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x5f, 0xe5, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x5e, 0xf5, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0xff, 0x0, 0x8, 0xde, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xed, 0x91, 0x0,
    0xff, 0x0, 0x0, 0xfd, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0xdf, 0x0, 0x0,
    0xfd, 0x0, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0x10, 0xdf, 0x0, 0xd9, 0xfd, 0x1, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0xdf, 0x1, 0xfc, 0xfd, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x20, 0xdf, 0x1, 0xfc, 0xfd, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x20, 0xdf, 0x1, 0xfc, 0xfd, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0xdf, 0x1, 0xfc,
    0xfd, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0xdf, 0x1, 0xfc, 0xfd, 0x0, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x10, 0xdf, 0x0, 0xfb, 0xfd, 0x0, 0xcf, 0xdb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbc, 0xfd, 0x0, 0xdf, 0x0, 0x21, 0xfe, 0x0, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd3, 0x0, 0xef, 0x0, 0x0, 0xdf, 0x30, 0x0, 0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x3,
    0xfd, 0x0, 0x0, 0x7f, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xf7, 0x0, 0x0, 0xc, 0xff,
    0xed, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xde, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x7d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0x0, 0x4a, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xa4, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x5f, 0xe5, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x5e, 0xf5, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0xff, 0x0, 0x8, 0xde, 0xee, 0xee, 0xee, 0xee, 0xee, 0xc6, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xfd, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xfd, 0x0,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xc0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0xd9, 0xfd, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0xdf, 0x1, 0xfc, 0xfd, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0,
    0x0, 0x0, 0x0, 0xdf, 0x1, 0xfc, 0xfd, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0xdf, 0x1,
    0xfc, 0xfd, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0xdf, 0x1, 0xfc, 0xfd, 0x1, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0xdf, 0x1, 0xfc, 0xfd, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0xfb, 0xfd, 0x0, 0xcf, 0xdb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xef, 0x80,
    0x0, 0x0, 0x0, 0xdf, 0x0, 0x21, 0xfe, 0x0, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x0, 0x0, 0xdf, 0x30, 0x0, 0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0, 0x7f, 0xd2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xf7, 0x0, 0x0, 0xc, 0xff, 0xed, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xde, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x7d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F242 "" */
    0x0, 0x4a, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xa4, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x5f, 0xe5, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x5e, 0xf5, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0xff, 0x0, 0x8, 0xde, 0xee, 0xee, 0xee, 0xc7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xfd, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xfd, 0x0,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0xd9, 0xfd, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x1, 0xfc, 0xfd, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x1, 0xfc, 0xfd, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x1,
    0xfc, 0xfd, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x1, 0xfc, 0xfd, 0x1, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x1, 0xfc, 0xfd, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0xfb, 0xfd, 0x0, 0xcf, 0xdb, 0xbb, 0xbb, 0xbb, 0xef, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x0, 0x21, 0xfe, 0x0, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x0,
    0x0, 0xdf, 0x30, 0x0, 0x12, 0x22, 0x22, 0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0, 0x7f, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xf7, 0x0, 0x0, 0xc, 0xff, 0xed, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xde, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x7d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F243 "" */
    0x0, 0x4a, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xa4, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x5f, 0xe5, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x5e, 0xf5, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0xff, 0x0, 0x8, 0xdd, 0xd9, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0xfd, 0x0, 0xaf, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xfd, 0x0, 0xfe,
    0x10, 0x1e, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0xd8, 0xfd, 0x1, 0xfc, 0x0, 0xb, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x1, 0xfc, 0xfd, 0x1, 0xfc, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x1, 0xfc, 0xfd, 0x1, 0xfc, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x1, 0xfc,
    0xfd, 0x1, 0xfc, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x1, 0xfc, 0xfd, 0x1, 0xfc, 0x0, 0xb,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x1, 0xfc, 0xfd, 0x0, 0xfd, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0xfb, 0xfd, 0x0, 0xcf, 0xcb, 0xcf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x0, 0x21, 0xfe, 0x0, 0x2d, 0xff, 0xfd, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0, 0xdf,
    0x30, 0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0, 0x7f, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xf7, 0x0, 0x0, 0xc, 0xff, 0xed, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xde, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x7d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0x0, 0x4a, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xa4, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x5f, 0xe5, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x5e, 0xf5, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0xd8, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x1, 0xfc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x1, 0xfc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x1, 0xfc, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x1, 0xfc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x1, 0xfc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x0, 0xfc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0x53,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0, 0xdf, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0, 0x7f, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xf7, 0x0, 0x0, 0xc, 0xff, 0xed, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xde,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x7d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf4, 0xef, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x2d, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x1, 0xcf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0xb, 0xfe, 0x30, 0xb, 0x60, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0x9f, 0xf3, 0x1f, 0xfa, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x6f, 0xf6, 0x4, 0xef, 0xd2, 0x0, 0xc, 0xf1, 0x0, 0x9, 0xff,
    0x70, 0x0, 0x1c, 0xfe, 0x40, 0xc, 0xf1, 0x2, 0xcf, 0xe4, 0x0, 0x0, 0x0, 0x9f, 0xf8, 0xc, 0xf1, 0x4e, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xbd, 0xf9, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2c, 0xfe, 0x4c, 0xf2, 0xcf, 0xe4, 0x0, 0x0, 0x0, 0x4, 0xef,
    0xc1, 0xc, 0xf1, 0x9, 0xff, 0x80, 0x0, 0x0, 0x8f, 0xf9, 0x0, 0xc, 0xf1, 0x0, 0x5f, 0xfb, 0x10, 0xb, 0xff, 0x50, 0x0,
    0xc, 0xf1, 0x0, 0x2, 0xdf, 0xe2, 0x2f, 0xd2, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x3f, 0xf6, 0x3, 0x0, 0x0, 0x0, 0xc,
    0xf1, 0x0, 0x3, 0xef, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x6,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x8f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfb, 0xfe, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0x90, 0x0, 0x0, 0x0, 0x0,

    /* U+F376 "" */
    0x0, 0x4a, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd9, 0x0, 0x0, 0x2d, 0xdd, 0xdd, 0xa4, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x7, 0xd3, 0xb, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x5f, 0xe5, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x1, 0x5e, 0xf5, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xe7, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0xd8, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfe,
    0x2a, 0xfc, 0x99, 0x81, 0x0, 0x0, 0x0, 0xef, 0x1, 0xfc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xe2, 0xe, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xef, 0x1, 0xfc, 0xfe, 0x0, 0x0, 0x0, 0xb, 0xfd, 0x20, 0x2, 0x44, 0xbf, 0xf3, 0x0, 0x0, 0x0, 0xef,
    0x1, 0xfc, 0xfe, 0x0, 0x0, 0x0, 0x7f, 0xfd, 0xcc, 0x90, 0x8, 0xff, 0x30, 0x0, 0x0, 0x0, 0xef, 0x1, 0xfc, 0xfe, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xe0, 0x9f, 0xe3, 0x0, 0x0, 0x0, 0x0, 0xef, 0x1, 0xfc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x11,
    0xaf, 0x8a, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0xfc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xbf, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0x53, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x0, 0x0, 0xdf, 0x30, 0x0, 0x0, 0x0, 0xf, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0, 0x7f, 0xd2, 0x0,
    0x0, 0x0, 0x6f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xf7, 0x0, 0x0, 0xc, 0xff, 0xed, 0xdd, 0x80, 0x5f, 0xb0,
    0x8, 0xdd, 0xdd, 0xdd, 0xdd, 0xde, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x7d, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F377 "" */
    0x1, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x6d, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xca, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfc, 0x10, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xb1, 0x0, 0x2d, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x30, 0x0, 0xa, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0,
    0x0, 0x7, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x4,
    0xef, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xe0, 0xd, 0x80, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xfe, 0x1, 0xfc, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xe0, 0x1f, 0xc0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xfe, 0x1, 0xfc, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfe, 0x40, 0x0, 0x0, 0x0, 0xf, 0xe0,
    0x1f, 0xc0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x70, 0x0, 0x0, 0x0, 0xfe, 0x1, 0xfc, 0x0,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xb0, 0x0, 0x0, 0xf, 0xe0, 0xf, 0xc0, 0x0, 0x1, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xd2, 0x0, 0x0, 0xee, 0x0, 0x53, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xf5, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0x90, 0x0, 0x2d, 0xfd, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc1, 0x0, 0xa, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xef,
    0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F3C5 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x7c, 0xff, 0xff, 0xfb, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xfe, 0xcb, 0xcf, 0xff, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0x82, 0x0, 0x0, 0x4, 0xaf,
    0xfa, 0x0, 0x0, 0x0, 0x1e, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xfb, 0x0, 0x0, 0xc, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xf8, 0x0, 0x7, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xf3, 0x0, 0xff, 0x20, 0x0, 0x0, 0x26,
    0x74, 0x0, 0x0, 0x0, 0x6f, 0xb0, 0x5f, 0xa0, 0x0, 0x0, 0x9f, 0xff, 0xfe, 0x30, 0x0, 0x0, 0xef, 0x1a, 0xf4, 0x0, 0x0,
    0x8f, 0xc5, 0x36, 0xfe, 0x10, 0x0, 0x9, 0xf5, 0xdf, 0x0, 0x0, 0x1f, 0xe0, 0x0, 0x3, 0xf9, 0x0, 0x0, 0x5f, 0x8f,
    0xf0, 0x0, 0x5, 0xf8, 0x0, 0x0, 0xd, 0xd0, 0x0, 0x3, 0xfa, 0xfe, 0x0, 0x0, 0x6f, 0x80, 0x0, 0x0, 0xde, 0x0, 0x0,
    0x3f, 0xad, 0xf1, 0x0, 0x3, 0xfe, 0x0, 0x0, 0x3f, 0xb0, 0x0, 0x6, 0xf8, 0x9f, 0x60, 0x0, 0xc, 0xfd, 0x53, 0x7f,
    0xf4, 0x0, 0x0, 0xaf, 0x44, 0xfc, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xf, 0xf0, 0xe, 0xf3, 0x0, 0x0, 0x7,
    0xcd, 0xa3, 0x0, 0x0, 0x7, 0xf9, 0x0, 0x7f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0xef, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xa0, 0x0, 0x6, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0, 0xc,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x0, 0x0, 0x0, 0x3f, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x90, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0x0, 0x9f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xfe, 0x10, 0x0, 0x0, 0x5f, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x0, 0x1e, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf7, 0x0, 0xc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf3, 0x8, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xe6, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F4DA "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xef,
    0xff, 0xfe, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xfd, 0xcc, 0xdf, 0xff, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x83, 0x0, 0x0, 0x0, 0x38, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xfc, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xb0, 0x0, 0x0, 0x7f,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x10, 0x9, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x90, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x5f, 0xa0, 0x0, 0x0, 0x5, 0x92, 0x0, 0x0,
    0x2, 0x8a, 0x94, 0x0, 0x0, 0xa, 0xf5, 0x9f, 0x50, 0x0, 0x0, 0xf, 0xfa, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xa0, 0x0, 0x5,
    0xf9, 0xcf, 0x10, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0xff, 0x83, 0x5e, 0xf5, 0x0, 0x1, 0xfc, 0xef, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x65, 0x0, 0x1, 0x71, 0x0, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfd, 0xbf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xfb, 0x8f, 0x70, 0x0, 0x0, 0x54, 0x0, 0x0, 0x0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x7, 0xf8, 0x3f, 0xc0,
    0x0, 0x0, 0xff, 0x60, 0x0, 0x0, 0x0, 0x6, 0xff, 0x0, 0x0, 0xc, 0xf3, 0xd, 0xf3, 0x0, 0x0, 0x5f, 0xfa, 0x30, 0x0,
    0x3, 0xaf, 0xf5, 0x0, 0x0, 0x3f, 0xd0, 0x6, 0xfc, 0x0, 0x0, 0x3, 0xdf, 0xfe, 0xcc, 0xef, 0xfd, 0x30, 0x0, 0x0, 0xbf,
    0x60, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x5, 0xbf, 0xff, 0xfc, 0x60, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x6, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xe9, 0x63, 0x22, 0x36, 0x9e,
    0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x8a, 0xbb, 0xa8, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F556 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xff,
    0xff, 0xff, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xfc, 0xbb, 0xcf, 0xff, 0xfb, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0x82, 0x0, 0x0, 0x0, 0x28, 0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xfd, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xb0, 0x0, 0x0, 0x7f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x20, 0x9, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x90, 0xf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x5f, 0xa0, 0x0, 0x3e, 0xb6, 0x10, 0x0, 0x0, 0x0,
    0x1, 0x6b, 0xe3, 0x0, 0x9, 0xf5, 0x9f, 0x40, 0x0, 0x3e, 0xff, 0xfb, 0x61, 0x0, 0x16, 0xbf, 0xff, 0xe3, 0x0, 0x4,
    0xf9, 0xcf, 0x10, 0x0, 0x0, 0x3d, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xd3, 0x0, 0x0, 0x1, 0xfc, 0xef, 0x0, 0x0, 0x0, 0xf,
    0xfc, 0x88, 0x0, 0x78, 0xcf, 0xf0, 0x0, 0x0, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x6f, 0xc0,
    0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xef, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0xbf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xfb, 0x8f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf8, 0x3f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x38, 0xaa, 0x83, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf3, 0xd, 0xf3, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x5, 0xfc, 0x0, 0x0, 0x0, 0xdf, 0xd6, 0x33, 0x6d, 0xfd, 0x0, 0x0, 0x0, 0xbf,
    0x60, 0x0, 0xdf, 0x70, 0x0, 0x1, 0xea, 0x0, 0x0, 0x0, 0xae, 0x10, 0x0, 0x7, 0xfd, 0x0, 0x0, 0x2f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x5, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x6f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xea, 0x63, 0x22, 0x36, 0xae,
    0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x8a, 0xbb, 0xa8, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F579 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xef,
    0xff, 0xfe, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xfd, 0xcc, 0xdf, 0xff, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x83, 0x0, 0x0, 0x0, 0x38, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xfc, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xb0, 0x0, 0x0, 0x7f,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x10, 0x9, 0xf8, 0x0, 0x5, 0xac, 0xa4, 0x0, 0x0, 0x0, 0x4a, 0xca, 0x50, 0x0, 0x8f, 0x90,
    0xf, 0xf1, 0x0, 0xbf, 0xff, 0xff, 0x80, 0x0, 0x9, 0xff, 0xff, 0xfb, 0x0, 0x1f, 0xf0, 0x5f, 0xa0, 0x9, 0xfc, 0x41,
    0x5e, 0xf5, 0x0, 0x7f, 0xd4, 0x13, 0xcf, 0x90, 0xa, 0xf5, 0x9f, 0x50, 0x1f, 0xe1, 0x36, 0x3, 0xfc, 0x0, 0xef, 0x21,
    0x62, 0x1e, 0xf1, 0x5, 0xf9, 0xcf, 0x10, 0x4f, 0xa0, 0xef, 0x90, 0xdf, 0x1, 0xfc, 0xb, 0xfd, 0xa, 0xf3, 0x1, 0xfc,
    0xef, 0x0, 0x3f, 0xa0, 0xef, 0x80, 0xef, 0x0, 0xfd, 0xb, 0xfd, 0xb, 0xf3, 0x0, 0xfe, 0xfe, 0x0, 0xf, 0xe1, 0x25,
    0x4, 0xfc, 0x0, 0xdf, 0x40, 0x51, 0x2f, 0xe0, 0x0, 0xef, 0xfe, 0x0, 0x8, 0xfd, 0x52, 0x6f, 0xf4, 0x0, 0x4f, 0xf8,
    0x57, 0xef, 0x70, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x70, 0x0, 0x5, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xfd,
    0xbf, 0x30, 0x0, 0x4, 0x9b, 0x82, 0x0, 0x0, 0x0, 0x16, 0x86, 0x10, 0x0, 0x3, 0xfb, 0x8f, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf8, 0x3f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf3, 0xd, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x6, 0xfc, 0x0, 0x0, 0x0,
    0x6d, 0xdd, 0xdd, 0xdd, 0xc4, 0x0, 0x0, 0x0, 0xbf, 0x60, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x6,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x60, 0x0, 0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2b, 0xff, 0xe9, 0x63, 0x22, 0x36, 0x9e, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x8a, 0xbb, 0xa8, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F584 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xef,
    0xff, 0xfe, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xfd, 0xcc, 0xdf, 0xff, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x83, 0x0, 0x0, 0x0, 0x38, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xfc, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xb0, 0x0, 0x0, 0x7f,
    0xd1, 0x0, 0x2, 0x89, 0x40, 0x0, 0x4, 0x98, 0x20, 0x0, 0x1d, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x0, 0x2e, 0xff, 0xf6, 0x0,
    0x6f, 0xff, 0xe2, 0x0, 0x2, 0xff, 0x10, 0x9, 0xf8, 0x0, 0x59, 0xcf, 0xb8, 0xfe, 0x0, 0xef, 0x7b, 0xfc, 0x95, 0x0,
    0x8f, 0x90, 0xf, 0xf1, 0x9, 0xff, 0xff, 0x10, 0xef, 0x0, 0xfd, 0x1, 0xff, 0xff, 0x90, 0x1f, 0xf0, 0x5f, 0xa0, 0x2f,
    0xe8, 0xa7, 0x1, 0xfc, 0x0, 0xcf, 0x10, 0x8a, 0x8f, 0xf2, 0xa, 0xf5, 0x9f, 0x50, 0x3f, 0x90, 0x0, 0x5, 0xf8, 0x0,
    0x8f, 0x50, 0x0, 0xa, 0xf3, 0x5, 0xf9, 0xcf, 0x10, 0xe, 0xf8, 0x30, 0xa, 0xf4, 0x0, 0x4f, 0x90, 0x3, 0x8f, 0xe0,
    0x1, 0xfc, 0xef, 0x0, 0x3, 0xdf, 0xff, 0xbe, 0xf0, 0x0, 0xf, 0xeb, 0xff, 0xfd, 0x30, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x3,
    0x8c, 0xff, 0x70, 0x0, 0x7, 0xff, 0xc8, 0x30, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfd, 0xbf, 0x30,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x3, 0xfb, 0x8f, 0x70, 0x0, 0x5f, 0xfc, 0x96, 0x54, 0x33,
    0x45, 0x69, 0xcf, 0xf5, 0x0, 0x7, 0xf8, 0x3f, 0xc0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0xc, 0xf3, 0xd, 0xf3, 0x0, 0x6, 0xff, 0x87, 0x9a, 0xaa, 0xa9, 0x78, 0xff, 0x60, 0x0, 0x3f, 0xd0, 0x6, 0xfc, 0x0,
    0x0, 0x7f, 0xf9, 0x20, 0x0, 0x2, 0x9f, 0xf7, 0x0, 0x0, 0xbf, 0x60, 0x0, 0xdf, 0x60, 0x0, 0x5, 0xef, 0xfe, 0xcc,
    0xef, 0xfe, 0x50, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0, 0x6, 0xcf, 0xff, 0xfc, 0x60, 0x0, 0x0, 0x4f, 0xf3,
    0x0, 0x0, 0x6, 0xff, 0x40, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x4, 0xff, 0x60, 0x0, 0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xe9, 0x63, 0x22, 0x36, 0x9e, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x8a, 0xbb, 0xa8, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F588 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x9c, 0xff, 0xff, 0xfe, 0xa6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4d, 0xff, 0xfe, 0xcb, 0xbd, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xfc,
    0x61, 0x0, 0x0, 0x0, 0x39, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf3, 0x0, 0x0, 0x2, 0x30, 0x0, 0x0, 0x0, 0x14, 0x10, 0x0, 0x0, 0xbf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xfc, 0x0, 0x0, 0x1c, 0xff, 0xe3, 0x0, 0x0, 0x8f, 0xff, 0x80, 0x0, 0x4, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x20,
    0x0, 0xc, 0xfc, 0xbf, 0xe1, 0x0, 0x6f, 0xea, 0xef, 0x60, 0x0, 0x8, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfc, 0x0, 0x8f, 0x80, 0xe, 0xf2, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x30, 0x1,
    0xfc, 0x2, 0xfa, 0x0, 0xa, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7a, 0xdf, 0x60, 0x1, 0x40, 0x0, 0x2, 0x20,
    0x4, 0x10, 0x0, 0x14, 0x0, 0xc, 0xec, 0x96, 0x10, 0x0, 0x1b, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0x80, 0xa, 0xfd, 0x84, 0xbf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf7, 0x59, 0xff, 0x40, 0xef, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x70, 0x5, 0xf8, 0xe, 0xf1, 0x1, 0xfd, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x2, 0xfb, 0x0,
    0x6f, 0x80, 0x8f, 0xc5, 0xaf, 0x80, 0x0, 0x9f, 0xeb, 0x86, 0x54, 0x33, 0x45, 0x69, 0xdf, 0xf1, 0x0, 0xd, 0xf7, 0x6e,
    0xf3, 0x0, 0xbf, 0xff, 0xe1, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x48, 0x71, 0x0, 0x0, 0xa, 0xfe, 0x78, 0x9a, 0xba, 0xa9, 0x79, 0xff, 0x20, 0x0, 0x0, 0x38, 0x72, 0x0, 0x0,
    0x0, 0x0, 0x8, 0x50, 0x0, 0xb, 0xfe, 0x71, 0x0, 0x0, 0x3a, 0xff, 0x40, 0x0, 0x19, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x30, 0x0, 0x7, 0xff, 0xfe, 0xcd, 0xff, 0xfc, 0x20, 0x0, 0xb, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfe, 0x10, 0x0, 0x1, 0x8d, 0xff, 0xfe, 0xa5, 0x0, 0x0, 0x8, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd,
    0x20, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x8, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfe, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x7e, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xdf, 0xfd, 0x95, 0x32, 0x24, 0x7b,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x92,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x58, 0xab, 0xb9, 0x73, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F598 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0xcf,
    0xff, 0xff, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xff, 0xfc, 0xbb, 0xcf, 0xff, 0xfb, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xe8, 0x20, 0x0, 0x0, 0x2, 0x7e, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xd1, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xc0, 0x0, 0x0,
    0x7, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x70, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x20, 0x0, 0x9f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xf9, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x5, 0xfa, 0x0, 0x0, 0x0,
    0x59, 0x20, 0x0, 0x0, 0x28, 0xa9, 0x40, 0x0, 0x0, 0x9f, 0x50, 0x9f, 0x50, 0x0, 0x0, 0xf, 0xfa, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xa0, 0x0, 0x4, 0xf9, 0xc, 0xf1, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0xf, 0xf8, 0x35, 0xef, 0x60, 0x0, 0x1f, 0xc0,
    0xef, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x64, 0x0, 0x1, 0x81, 0x0, 0x0, 0xfe, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xfe, 0xd, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0xbf, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3a, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfb, 0x8, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x90, 0x0,
    0x27, 0x60, 0x0, 0x7f, 0x70, 0x3f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x29, 0xdf, 0xe2, 0x0, 0x5f, 0xff, 0xd2, 0x0, 0x41,
    0x0, 0xdf, 0x30, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf6, 0x0, 0xf, 0xf8, 0xaf, 0xa0, 0x0, 0x0, 0x5, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x8e, 0xf5, 0x3, 0xfa, 0x0, 0xef, 0xff, 0x80, 0x0, 0xd, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xa0, 0x2f,
    0xb0, 0x9, 0xfe, 0xff, 0x70, 0x0, 0x2f, 0xf4, 0x0, 0x0, 0x0, 0x4, 0x8e, 0xf5, 0x0, 0xef, 0x0, 0x1, 0x2, 0xfd, 0x0,
    0x0, 0x5f, 0xf4, 0x0, 0x0, 0x8, 0xff, 0xc4, 0x0, 0xb, 0xf3, 0x0, 0x0, 0xf, 0xe0, 0x0, 0x0, 0x6f, 0xf8, 0x0, 0x0,
    0x26, 0x20, 0x0, 0x0, 0x7f, 0x70, 0x0, 0x5b, 0xfa, 0x0, 0x0, 0x0, 0x5f, 0xfd, 0x50, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfc,
    0x7c, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xea, 0x63, 0x22, 0x36, 0xa1, 0xd, 0xff, 0xfe, 0xa5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x80, 0x18, 0x73, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14,
    0x8a, 0xbb, 0xa8, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F59B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xef,
    0xff, 0xfe, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xfd, 0xcc, 0xdf, 0xff, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x83, 0x0, 0x0, 0x0, 0x38, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xfc, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xb0, 0x0, 0x0, 0x7f,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x10, 0x9, 0xf8, 0x0, 0x2, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0x20, 0x0, 0x8f, 0x90,
    0xf, 0xf1, 0x0, 0x6, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x3b, 0xff, 0x60, 0x0, 0x1f, 0xf0, 0x5f, 0xa0, 0x0, 0x0, 0x5d, 0xff,
    0xa0, 0x0, 0xa, 0xff, 0xd6, 0x0, 0x0, 0xa, 0xf5, 0x9f, 0x50, 0x0, 0x0, 0x18, 0xff, 0xe0, 0x0, 0xe, 0xff, 0x81, 0x0,
    0x0, 0x5, 0xf9, 0xcf, 0x10, 0x0, 0x4, 0xff, 0xf8, 0x10, 0x0, 0x1, 0x8f, 0xff, 0x40, 0x0, 0x1, 0xfc, 0xef, 0x0, 0x0,
    0x5, 0xe8, 0x10, 0x0, 0x0, 0x0, 0x1, 0x8e, 0x40, 0x0, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xdf, 0x0,
    0x0, 0x2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20, 0x0, 0x0, 0xfd, 0xbf, 0x30, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x3, 0xfb, 0x8f, 0x70, 0x0, 0xcf, 0xcb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbc, 0xfc,
    0x0, 0x7, 0xf8, 0x3f, 0xc0, 0x0, 0x6f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf6, 0x0, 0xc, 0xf3, 0xd, 0xf3, 0x0,
    0xe, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xe0, 0x0, 0x3f, 0xd0, 0x6, 0xfc, 0x0, 0x4, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x40, 0x0, 0xbf, 0x60, 0x0, 0xdf, 0x60, 0x0, 0x5f, 0xfb, 0x40, 0x0, 0x4, 0xbf, 0xf5, 0x0, 0x6, 0xfd, 0x0,
    0x0, 0x3f, 0xf4, 0x0, 0x3, 0xdf, 0xff, 0xdd, 0xff, 0xfd, 0x30, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x6, 0xff, 0x40, 0x0, 0x5,
    0xbe, 0xff, 0xeb, 0x50, 0x0, 0x4, 0xff, 0x60, 0x0, 0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b,
    0xff, 0xe9, 0x63, 0x22, 0x36, 0x9e, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x8a, 0xbb, 0xa8, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F5A4 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xef,
    0xff, 0xfe, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xfd, 0xcc, 0xdf, 0xff, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x93, 0x0, 0x0, 0x0, 0x39, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xfc, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xb0, 0x0, 0x0, 0x7f,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x10, 0x9, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x90, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x5f, 0xa0, 0x0, 0x0, 0x2, 0x50, 0x0, 0x0,
    0x0, 0x5, 0x20, 0x0, 0x0, 0xa, 0xf5, 0x9f, 0x50, 0x0, 0x0, 0xe, 0xf9, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0x5,
    0xf9, 0xcf, 0x10, 0x0, 0x0, 0xe, 0xf9, 0x0, 0x0, 0x0, 0x9f, 0xe0, 0x0, 0x0, 0x1, 0xfc, 0xef, 0x0, 0x0, 0x0, 0x2,
    0x50, 0x0, 0x0, 0x0, 0x5, 0x20, 0x0, 0x0, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfd, 0xbf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xfb, 0x8f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf8, 0x3f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf3, 0xd, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xd0, 0x6, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x60, 0x0, 0xdf, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x6, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x7f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5d, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xea, 0x63, 0x22, 0x36, 0xae, 0xff, 0xb2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x8a, 0xbb, 0xa8, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F5B3 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xff,
    0xff, 0xff, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xfc, 0xbb, 0xcf, 0xff, 0xfb, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0x82, 0x0, 0x0, 0x0, 0x28, 0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xfd, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xb0, 0x0, 0x0, 0x7f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf7, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x20, 0x9, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x90, 0xf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x5f, 0xa0, 0x0, 0x4, 0x9a, 0x82, 0x0, 0x0, 0x0,
    0x28, 0xa9, 0x30, 0x0, 0xa, 0xf5, 0x9f, 0x50, 0x0, 0x9f, 0xff, 0xff, 0x60, 0x0, 0x6, 0xff, 0xff, 0xf9, 0x0, 0x5,
    0xf9, 0xcf, 0x10, 0x5, 0xfe, 0x53, 0x7f, 0xf1, 0x0, 0x1f, 0xf7, 0x35, 0xef, 0x50, 0x1, 0xfc, 0xef, 0x0, 0x1, 0x82,
    0x0, 0x4, 0x70, 0x0, 0x7, 0x40, 0x0, 0x28, 0x10, 0x0, 0xfe, 0xfe, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x6, 0xaa, 0x60, 0x0, 0x1f, 0xa0, 0x0, 0x0, 0xef, 0xef, 0x0,
    0x0, 0xb, 0xf2, 0x0, 0xbf, 0xff, 0xfb, 0x0, 0x2f, 0xb0, 0x0, 0x0, 0xfe, 0xbf, 0x20, 0x0, 0xb, 0xf2, 0x6, 0xfc, 0x33,
    0xcf, 0x50, 0x2f, 0xb0, 0x0, 0x2, 0xfb, 0x8f, 0x60, 0x0, 0xb, 0xf2, 0xa, 0xf3, 0x0, 0x3f, 0xa0, 0x2f, 0xb0, 0x0,
    0x6, 0xf8, 0x3f, 0xb0, 0x0, 0xb, 0xf2, 0xb, 0xf1, 0x0, 0x1f, 0xb0, 0x2f, 0xb0, 0x0, 0xb, 0xf3, 0xd, 0xf2, 0x0, 0xb,
    0xf2, 0xb, 0xf2, 0x0, 0x2f, 0xb0, 0x2f, 0xb0, 0x0, 0x2f, 0xd0, 0x5, 0xfa, 0x0, 0xb, 0xf2, 0x8, 0xf8, 0x0, 0x8f,
    0x80, 0x2f, 0xb0, 0x0, 0xaf, 0x60, 0x0, 0xdf, 0x50, 0xb, 0xf2, 0x1, 0xef, 0xdd, 0xfe, 0x10, 0x2f, 0xb0, 0x5, 0xfd,
    0x0, 0x0, 0x2f, 0xf2, 0xb, 0xf2, 0x0, 0x2b, 0xff, 0xb2, 0x0, 0x2f, 0xb0, 0x2e, 0xf3, 0x0, 0x0, 0x5, 0xfe, 0x2b,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb2, 0xef, 0x50, 0x0, 0x0, 0x0, 0x6f, 0xfe, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2b, 0xff, 0xd9, 0x53, 0x22, 0x35, 0x9d, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x8a, 0xbb, 0xa8, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F68C "" */
    0x7, 0x41, 0xfc, 0x1f, 0xc1, 0xfc, 0x1f, 0xc1, 0xfc, 0x1f, 0xc0, 0xa7,

    /* U+F68D "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x0, 0x0, 0xb, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x7, 0x40, 0x0, 0x0, 0xbf, 0x21, 0xfc, 0x0, 0x0, 0xb,
    0xf2, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x21, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x21, 0xfc, 0x0, 0x0,
    0xb, 0xf2, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0xa7, 0x0, 0x0, 0x6, 0xb0,

    /* U+F68E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x13, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x6, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x20, 0x0, 0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0,
    0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x6, 0xf7, 0x8, 0x50, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x6, 0xf7, 0x1f,
    0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x6, 0xf7, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x6, 0xf7, 0x1f, 0xc0, 0x0, 0x0,
    0xbf, 0x20, 0x0, 0x6, 0xf7, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x6, 0xf7, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0,
    0x6, 0xf7, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x6, 0xf7, 0x9, 0x50, 0x0, 0x0, 0x59, 0x0, 0x0, 0x1, 0xa2,

    /* U+F68F "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb7, 0x0, 0x0, 0xb, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc,
    0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0,
    0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x7, 0x40,
    0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0,
    0x0, 0xb, 0xf2, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x1f, 0xc0, 0x0, 0x0, 0xbf,
    0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2,
    0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1,
    0xfc, 0x0, 0x0, 0xb, 0xf2, 0xa, 0x70, 0x0, 0x0, 0x6b, 0x0, 0x0, 0x0, 0xa7, 0x0, 0x0, 0x6, 0xb0,

    /* U+F695 "" */
    0x1, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xa0, 0x0, 0x8, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x5, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x2, 0xdf, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x25, 0x0,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf1, 0x0, 0x0, 0x1f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xef, 0xc1, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf7, 0x0,
    0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfa, 0x0, 0x0, 0x0,
    0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xfd, 0x20, 0x0, 0xb, 0xf2, 0x0,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfe, 0x40, 0x0, 0x6f, 0x20, 0x0, 0x1,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x9, 0xff, 0x70, 0x0, 0x41, 0x0, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x0, 0x5, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xd2, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf2, 0x0, 0x0, 0x1a, 0x0, 0x1, 0xbf, 0xf5, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20,
    0x0, 0x1, 0xfb, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f,
    0xc0, 0x0, 0x0, 0x4f, 0xfb, 0x10, 0x0, 0xac, 0x0, 0x0, 0x0, 0x7, 0x40, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0,
    0x0, 0x0, 0x2d, 0xfd, 0x30, 0x0, 0x40, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xa3, 0x0, 0x4, 0xef,
    0xc1, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf1, 0x0, 0x1, 0xcf, 0xe3,
    0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x0, 0x9f, 0xf6, 0x0,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x6f, 0xf6, 0x0, 0x0,
    0xa7, 0x0, 0x0, 0x6, 0xb0, 0x0, 0x0, 0xa, 0x70, 0x0, 0x0, 0x6b, 0x0, 0x0, 0x0, 0x0, 0x3b, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F6A8 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfd, 0x3f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xfc, 0x11, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xfa, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0xbe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf9, 0x0, 0x1, 0xfc, 0x0,
    0x0, 0x0, 0x7, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x8, 0xfd, 0x0,
    0x0, 0x5b, 0xde, 0xee, 0xef, 0xf5, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf7, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x5e, 0x40, 0x0, 0x2f, 0xe0, 0xe, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfc, 0x0, 0x5, 0xff, 0x20, 0x0, 0xbf, 0x40, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x8, 0xfa,
    0x0, 0x6, 0xf9, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xe, 0xf0, 0x0, 0x3f, 0xb1, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0xbf, 0x20, 0x1, 0xfc, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0xd, 0xf1, 0x0, 0x2f, 0xc1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x2,
    0xfd, 0x0, 0x4, 0xfa, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x1, 0xdf, 0x60, 0x0, 0x8f, 0x70,
    0xbf, 0xc8, 0x88, 0x88, 0x60, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x7f, 0xb0, 0x0, 0xe, 0xf2, 0x2, 0xdf, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x1, 0x50, 0x0, 0x7, 0xfb, 0x0, 0x0, 0x35, 0x66, 0x66, 0xdf, 0xc1, 0x0, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xd2, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x1,
    0xdf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xe3, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0xbf, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf5, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x5, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf6, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf9, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xc2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F6A9 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xd3, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xc1, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xb0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x90, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x70, 0x0, 0x1, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xbd, 0xdd, 0xde, 0xff, 0x50, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x3c, 0x30, 0x0,
    0x0, 0x1, 0xb7, 0x5, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x4, 0xff, 0x40, 0x0, 0x1, 0xcf, 0x90,
    0xcf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x6, 0xff, 0x40, 0x1, 0xdf, 0xb0, 0xf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x6, 0xff, 0x41, 0xdf, 0xb0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0x6, 0xff, 0xdf, 0xb0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xc0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0x40,
    0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x1, 0xdf, 0xb7, 0xff, 0x40, 0x0, 0xef, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x1, 0xcf, 0xb0, 0x6, 0xff, 0x40, 0xa, 0xfc, 0x87, 0x77, 0x76, 0x0, 0x0,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0xcf, 0xb0, 0x0, 0x6, 0xff, 0x30, 0x1c, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x1f, 0xc0,
    0x0, 0x6f, 0xb0, 0x0, 0x0, 0x6, 0xfb, 0x0, 0x3, 0x55, 0x55, 0x6d, 0xfc, 0x10, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x40, 0x0,
    0x0, 0x0, 0x3, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfe, 0x20, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfe, 0x30, 0x1, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x50, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x61, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xcc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F6AA "" */
    0x0, 0x7, 0xb9, 0x30, 0x0, 0x1, 0xdf, 0xff, 0xf6, 0x0, 0x9, 0xfa, 0x35, 0xef, 0x10, 0xe, 0xf0, 0x0, 0x7f, 0x60, 0xf,
    0xf0, 0x0, 0x7f, 0x70, 0xa, 0xf8, 0x3, 0xef, 0x20, 0x2, 0xef, 0xff, 0xf8, 0x0, 0x0, 0x29, 0xdc, 0x60, 0x0,

    /* U+F6AB "" */
    0x0, 0x0, 0x0, 0x2, 0x7a, 0xde, 0xff, 0xec, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xcf, 0xff, 0xff, 0xee, 0xff,
    0xff, 0xe8, 0x10, 0x0, 0x0, 0x0, 0x3, 0xbf, 0xfd, 0x73, 0x0, 0x0, 0x2, 0x5a, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x6f, 0xfd,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0xff, 0xc1, 0x0, 0x9, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b,
    0xfe, 0x30, 0x5f, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xd0, 0x17, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x98, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x46, 0xef, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf0, 0x0, 0x6f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x4f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf8, 0x2, 0xcf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19, 0xdc, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F6AC "" */
    0x1, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x23, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xb1, 0x0, 0x0, 0x2, 0x7a, 0xdf,
    0xff, 0xff, 0xff, 0xeb, 0x84, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xd3, 0x0, 0x7, 0xff, 0xff, 0xed, 0xcb,
    0xbc, 0xdf, 0xff, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xf5, 0x0, 0x6, 0x84, 0x10, 0x0, 0x0, 0x0,
    0x2, 0x6a, 0xef, 0xfd, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4b, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbf,
    0xfa, 0x0, 0x0, 0x0, 0x9, 0x70, 0x0, 0x2d, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xfd,
    0x20, 0x0, 0xb, 0xfe, 0x20, 0x0, 0xa, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x40, 0xc,
    0xfc, 0x10, 0x0, 0x0, 0x7, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x30, 0xba, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xef, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xe4, 0x0, 0x3, 0x97, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf7, 0x0, 0x3, 0xef, 0xd7, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0x30, 0x0, 0x6f, 0xfa,
    0x0, 0x1, 0xaf, 0xfe, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfc, 0x0, 0x0, 0x3e, 0xfd, 0x20,
    0x0, 0x19, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf6, 0x0, 0x0, 0x0, 0x1c, 0xff, 0x50, 0x0,
    0x2, 0xcf, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x80, 0x0, 0x0, 0x9f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xb1, 0x0, 0x0, 0x55, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xca, 0x40, 0x0, 0x8f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0x70, 0x0, 0x4f, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfa,
    0x35, 0xef, 0x20, 0x0, 0x2d, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x7,
    0xf7, 0x0, 0x0, 0xa, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x7f, 0x70, 0x0,
    0x0, 0x7, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x80, 0x3e, 0xf3, 0x0, 0x0, 0x0, 0x4,
    0xef, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xae, 0xd7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11, 0x11, 0x11, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x10, 0x0, 0x0, 0x2, 0xef, 0xec, 0xcc, 0xcc, 0xcc, 0xcc, 0xce, 0xfe, 0x0, 0x0, 0x2, 0xef, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf7, 0x0, 0x2, 0xef, 0x90, 0x2, 0x50, 0x3, 0x40, 0x5, 0x30, 0x3f, 0xa0, 0x2, 0xef, 0x80, 0x0, 0xbf,
    0x10, 0xdf, 0x0, 0xfc, 0x2, 0xfb, 0x2, 0xef, 0x80, 0x0, 0xb, 0xf2, 0xd, 0xf0, 0x1f, 0xc0, 0x2f, 0xb2, 0xef, 0x80,
    0x0, 0x0, 0xbf, 0x20, 0xdf, 0x1, 0xfc, 0x2, 0xfb, 0xbf, 0x80, 0x0, 0x0, 0xb, 0xf2, 0xd, 0xf0, 0x1f, 0xc0, 0x2f,
    0xbf, 0xf0, 0x0, 0x0, 0x0, 0x9f, 0x0, 0xbd, 0x0, 0xea, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xbe, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfa, 0xaf,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0x51, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x1, 0x8c, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdb, 0x60, 0x0};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 420, .box_w = 27, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 419, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 884, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1349, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1814, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2279, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2744, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3209, .adv_w = 480, .box_w = 31, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3690, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4155, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4620, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5085, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5550, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6015, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6480, .adv_w = 420, .box_w = 27, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6899, .adv_w = 420, .box_w = 28, .box_h = 19, .ofs_x = -1, .ofs_y = 2},
    {.bitmap_index = 7165, .adv_w = 360, .box_w = 20, .box_h = 21, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 7375, .adv_w = 480, .box_w = 28, .box_h = 30, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 7795, .adv_w = 600, .box_w = 33, .box_h = 31, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 8307, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 8772, .adv_w = 540, .box_w = 36, .box_h = 31, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 9330, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 9795, .adv_w = 420, .box_w = 27, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10160, .adv_w = 600, .box_w = 38, .box_h = 28, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10692, .adv_w = 480, .box_w = 30, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11097, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 11562, .adv_w = 300, .box_w = 16, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11746, .adv_w = 360, .box_w = 23, .box_h = 28, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12068, .adv_w = 300, .box_w = 19, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12287, .adv_w = 360, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12552, .adv_w = 300, .box_w = 16, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12736, .adv_w = 420, .box_w = 28, .box_h = 24, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 13072, .adv_w = 420, .box_w = 27, .box_h = 24, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 13396, .adv_w = 360, .box_w = 24, .box_h = 27, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 13720, .adv_w = 360, .box_w = 24, .box_h = 28, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 14056, .adv_w = 480, .box_w = 30, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14461, .adv_w = 480, .box_w = 31, .box_h = 27, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 14880, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 15345, .adv_w = 420, .box_w = 27, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 15764, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 16229, .adv_w = 420, .box_w = 26, .box_h = 26, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16567, .adv_w = 600, .box_w = 39, .box_h = 27, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 17094, .adv_w = 420, .box_w = 27, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 17513, .adv_w = 540, .box_w = 34, .box_h = 20, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 17853, .adv_w = 540, .box_w = 34, .box_h = 20, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 18193, .adv_w = 540, .box_w = 34, .box_h = 20, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 18533, .adv_w = 540, .box_w = 34, .box_h = 20, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 18873, .adv_w = 540, .box_w = 34, .box_h = 20, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 19213, .adv_w = 360, .box_w = 20, .box_h = 31, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 19523, .adv_w = 540, .box_w = 34, .box_h = 20, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 19863, .adv_w = 600, .box_w = 39, .box_h = 32, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 20487, .adv_w = 360, .box_w = 23, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 20844, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 21309, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 21774, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 22239, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 22704, .adv_w = 600, .box_w = 39, .box_h = 31, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 23309, .adv_w = 480, .box_w = 31, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 23790, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 24255, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 24720, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 25185, .adv_w = 600, .box_w = 3, .box_h = 8, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 25197, .adv_w = 600, .box_w = 11, .box_h = 14, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 25274, .adv_w = 600, .box_w = 18, .box_h = 19, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 25445, .adv_w = 600, .box_w = 26, .box_h = 25, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 25770, .adv_w = 600, .box_w = 39, .box_h = 32, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 26394, .adv_w = 540, .box_w = 33, .box_h = 27, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 26840, .adv_w = 540, .box_w = 35, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 27313, .adv_w = 600, .box_w = 10, .box_h = 8, .ofs_x = 14, .ofs_y = -2},
    {.bitmap_index = 27353, .adv_w = 600, .box_w = 26, .box_h = 18, .ofs_x = 6, .ofs_y = -3},
    {.bitmap_index = 27587, .adv_w = 600, .box_w = 39, .box_h = 32, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 28211, .adv_w = 360, .box_w = 23, .box_h = 31, .ofs_x = 0, .ofs_y = -4}};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0,    0x1a1,  0x320,  0x322,  0x327,  0x32a,  0x339,  0x342,  0x347,  0x34d,  0x350,  0x359,
    0x3be,  0xfb6,  0xfbc,  0xfc1,  0xfc2,  0xfc6,  0xfc7,  0xfc8,  0xfca,  0xfce,  0xfdc,  0xfdd,
    0xff3,  0xff9,  0xffd,  0x1000, 0x1001, 0x1002, 0x1006, 0x1015, 0x1016, 0x1017, 0x1018, 0x1026,
    0x102a, 0x1061, 0x10a8, 0x10cd, 0x10d9, 0x11a0, 0x11ad, 0x11f5, 0x11f6, 0x11f7, 0x11f8, 0x11f9,
    0x1248, 0x132b, 0x132c, 0x137a, 0x148f, 0x150b, 0x152e, 0x1539, 0x153d, 0x154d, 0x1550, 0x1559,
    0x1568, 0x1641, 0x1642, 0x1643, 0x1644, 0x164a, 0x165d, 0x165e, 0x165f, 0x1660, 0x1661, 0x1777};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {{.range_start = 57419,
                                                .range_length = 6008,
                                                .glyph_id_start = 1,
                                                .unicode_list = unicode_list_0,
                                                .glyph_id_ofs_list = NULL,
                                                .list_length = 72,
                                                .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY}};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_awesome_30_4 = {
#else
lv_font_t font_awesome_30_4 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt, /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt, /*Function pointer to get glyph's bitmap*/
    .line_height = 32,                              /*The maximum line height required by the font*/
    .base_line = 5,                                 /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc, /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};

#endif /*#if FONT_AWESOME_30_4*/
