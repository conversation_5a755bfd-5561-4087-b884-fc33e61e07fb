#!/usr/bin/env python3
"""
编译验证脚本
检查PWM门控制系统的编译状态
"""

import os
import subprocess
import sys

def check_file_exists(filepath):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        print(f"✅ 文件存在: {filepath}")
        return True
    else:
        print(f"❌ 文件不存在: {filepath}")
        return False

def check_function_in_file(filepath, function_name):
    """检查文件中是否包含指定函数"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            if function_name in content:
                print(f"✅ 函数 {function_name} 在 {filepath} 中找到")
                return True
            else:
                print(f"❌ 函数 {function_name} 在 {filepath} 中未找到")
                return False
    except Exception as e:
        print(f"❌ 读取文件 {filepath} 失败: {e}")
        return False

def main():
    print("🔍 开始验证PWM门控制系统编译状态...")
    print("=" * 50)
    
    # 检查关键文件
    files_to_check = [
        "src/tuya_main.c",
        "src/pwm_door_control.c", 
        "include/pwm_door_control.h"
    ]
    
    all_files_exist = True
    for file_path in files_to_check:
        if not check_file_exists(file_path):
            all_files_exist = False
    
    if not all_files_exist:
        print("\n❌ 关键文件缺失，无法继续验证")
        return False
    
    print("\n🔍 检查函数调用...")
    
    # 检查tuya_main.c中的关键函数调用
    functions_to_check = [
        ("src/tuya_main.c", "pwm_door_control_init"),
        ("src/tuya_main.c", "pwm_door_control_handle_dp111"),
        ("src/tuya_main.c", "pwm_door_control_handle_dp112"),
        ("src/tuya_main.c", "#include \"pwm_door_control.h\""),
    ]
    
    all_functions_found = True
    for file_path, function_name in functions_to_check:
        if not check_function_in_file(file_path, function_name):
            all_functions_found = False
    
    # 检查pwm_door_control.c中的函数实现
    implementation_functions = [
        ("src/pwm_door_control.c", "OPERATE_RET pwm_door_control_init(void)"),
        ("src/pwm_door_control.c", "OPERATE_RET pwm_door_control_handle_dp111(bool open)"),
        ("src/pwm_door_control.c", "OPERATE_RET pwm_door_control_handle_dp112(bool open)"),
    ]
    
    for file_path, function_name in implementation_functions:
        if not check_function_in_file(file_path, function_name):
            all_functions_found = False
    
    print("\n" + "=" * 50)
    if all_files_exist and all_functions_found:
        print("✅ 所有验证通过！PWM门控制系统应该可以正常编译")
        return True
    else:
        print("❌ 验证失败，存在问题需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
