/**
 * @file iot_comm_manager.h
 * @brief IoT通信管理模块头文件
 * 
 * 提供IoT数据包管理、网络状态管理和DP数据发送功能
 * 
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#ifndef __IOT_COMM_MANAGER_H__
#define __IOT_COMM_MANAGER_H__

#include "tuya_cloud_types.h"
#include "tuya_iot.h"

#ifdef __cplusplus
extern "C" {
#endif

// ========== 配置常量 ==========
#define IOT_COMM_MAX_RETRY_COUNT        3
#define IOT_COMM_RETRY_INTERVAL_MS      1000
#define IOT_COMM_TIMEOUT_MS             5000
#define IOT_COMM_QUEUE_SIZE             20
#define IOT_COMM_MAX_PAYLOAD_SIZE       1024

// ========== 枚举定义 ==========

/**
 * @brief IoT数据包优先级枚举
 */
typedef enum {
    IOT_PRIORITY_LOW = 0,       /**< 低优先级 */
    IOT_PRIORITY_NORMAL,        /**< 普通优先级 */
    IOT_PRIORITY_HIGH,          /**< 高优先级 */
    IOT_PRIORITY_URGENT         /**< 紧急优先级 */
} iot_priority_e;

// ========== 结构体定义 ==========

/**
 * @brief IoT数据包结构体
 */
typedef struct {
    uint32_t id;                                    /**< 数据包ID */
    iot_priority_e priority;                        /**< 优先级 */
    char payload[IOT_COMM_MAX_PAYLOAD_SIZE];        /**< 数据载荷 */
    uint32_t payload_size;                          /**< 载荷大小 */
    uint32_t create_time;                           /**< 创建时间 */
    uint32_t retry_count;                           /**< 重试次数 */
    bool is_sent;                                   /**< 是否已发送 */
} iot_data_packet_t;

// ========== 函数声明 ==========

/**
 * @brief 初始化IoT通信管理器
 * 
 * @return OPERATE_RET 操作结果
 *         - OPRT_OK: 成功
 *         - 其他: 失败
 */
OPERATE_RET iot_comm_manager_init(void);

/**
 * @brief 设置网络连接状态
 * 
 * @param connected 网络连接状态
 *                  - true: 已连接
 *                  - false: 断开连接
 */
void iot_comm_manager_set_network_status(bool connected);

/**
 * @brief 发送DP数据
 * 
 * @param dp_json DP数据JSON字符串
 * @param priority 数据包优先级
 * @return uint32_t 数据包ID，0表示失败
 */
uint32_t iot_comm_manager_send_dp_data(const char *dp_json, iot_priority_e priority);

/**
 * @brief 获取通信统计信息
 * 
 * @param total_sent 总发送数量指针
 * @param total_success 成功发送数量指针
 * @param total_failed 失败发送数量指针
 */
void iot_comm_manager_get_stats(uint32_t *total_sent, uint32_t *total_success, uint32_t *total_failed);

/**
 * @brief 检查网络连接状态
 * 
 * @return bool 网络连接状态
 *         - true: 已连接
 *         - false: 未连接
 */
bool iot_comm_manager_is_network_connected(void);

/**
 * @brief 重置通信统计信息
 */
void iot_comm_manager_reset_stats(void);

// ========== 测试函数声明 ==========

/**
 * @brief 运行IoT通信管理器完整测试
 *
 * 该函数会测试IoT通信管理器的所有功能，包括：
 * - 基本初始化功能
 * - 网络状态管理
 * - DP数据发送
 * - 统计信息管理
 */
void iot_comm_manager_run_tests(void);

#ifdef __cplusplus
}
#endif

#endif /* __IOT_COMM_MANAGER_H__ */
