/**
 * @file test_pwm_simple.c
 * @brief 简化的PWM舵机控制测试
 * 
 * 基于TuyaOpen PWM API的正确实现
 */

#include "tuya_cloud_types.h"
#include "tkl_pwm.h"
#include "tkl_output.h"
#include "tal_system.h"

// PWM舵机控制参数
#define SERVO_PWM_FREQ              50          // PWM频率 50Hz
#define SERVO_PWM_CYCLE             20000       // 周期 20ms = 20000μs
#define SERVO_PWM_DUTY_0_DEG        500         // 0度对应的脉宽 (0.5ms)
#define SERVO_PWM_DUTY_90_DEG       1500        // 90度对应的脉宽 (1.5ms)
#define SERVO_PWM_DUTY_180_DEG      2500        // 180度对应的脉宽 (2.5ms)

// PWM通道定义
#define SERVO_PWM_CH_P06            TUYA_PWM_NUM_0  // P06引脚对应PWM通道
#define SERVO_PWM_CH_P07            TUYA_PWM_NUM_1  // P07引脚对应PWM通道

/**
 * @brief 角度转换为PWM占空比
 * @param angle 角度 (0-180)
 * @return uint32_t PWM占空比值
 */
static uint32_t angle_to_duty(uint16_t angle)
{
    if (angle > 180) {
        angle = 180;
    }
    
    // 线性插值: duty = 500 + (angle * (2500-500) / 180)
    return SERVO_PWM_DUTY_0_DEG + 
           (angle * (SERVO_PWM_DUTY_180_DEG - SERVO_PWM_DUTY_0_DEG) / 180);
}

/**
 * @brief 设置舵机角度
 * @param pwm_channel PWM通道
 * @param angle 目标角度 (0-180)
 * @return OPERATE_RET 操作结果
 */
static OPERATE_RET set_servo_angle(TUYA_PWM_NUM_E pwm_channel, uint16_t angle)
{
    uint32_t duty = angle_to_duty(angle);
    
    TUYA_PWM_BASE_CFG_T pwm_cfg = {
        .frequency = SERVO_PWM_FREQ,        // 50Hz
        .duty = duty,                       // 脉宽时间
        .cycle = SERVO_PWM_CYCLE,           // 周期时间 20000μs
        .polarity = TUYA_PWM_POSITIVE,      // 正极性
        .count_mode = TUYA_PWM_CNT_UP,      // 向上计数
    };
    
    PR_INFO("🎛️ 设置PWM通道%d: 角度=%d°, duty=%d, cycle=%d", 
            pwm_channel, angle, duty, SERVO_PWM_CYCLE);
    
    // 初始化PWM
    OPERATE_RET ret = tkl_pwm_init(pwm_channel, &pwm_cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ PWM通道%d初始化失败: %d", pwm_channel, ret);
        return ret;
    }
    
    // 启动PWM
    ret = tkl_pwm_start(pwm_channel);
    if (ret != OPRT_OK) {
        PR_ERR("❌ PWM通道%d启动失败: %d", pwm_channel, ret);
        return ret;
    }
    
    PR_INFO("✅ PWM通道%d设置成功: %d°", pwm_channel, angle);
    return OPRT_OK;
}

/**
 * @brief 控制门开关
 * @param pwm_channel PWM通道
 * @param open true=开门(90°), false=关门(0°)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET control_door(TUYA_PWM_NUM_E pwm_channel, bool open)
{
    uint16_t target_angle = open ? 90 : 0;  // 开门90°，关门0°
    
    PR_INFO("🚪 控制门: PWM通道%d %s (角度: %d°)", 
            pwm_channel, open ? "开启" : "关闭", target_angle);
    
    return set_servo_angle(pwm_channel, target_angle);
}

/**
 * @brief 简单的PWM门控制测试
 */
void test_pwm_door_control(void)
{
    PR_INFO("🚀 开始PWM门控制测试...");
    
    // 测试P06引脚舵机 (DP111)
    PR_INFO("📍 测试P06引脚舵机 (DP111)...");
    
    // 关门测试
    if (control_door(SERVO_PWM_CH_P06, false) == OPRT_OK) {
        PR_INFO("✅ P06舵机关门成功");
    } else {
        PR_ERR("❌ P06舵机关门失败");
    }
    
    tal_system_sleep(2000);  // 等待2秒
    
    // 开门测试
    if (control_door(SERVO_PWM_CH_P06, true) == OPRT_OK) {
        PR_INFO("✅ P06舵机开门成功");
    } else {
        PR_ERR("❌ P06舵机开门失败");
    }
    
    tal_system_sleep(2000);  // 等待2秒
    
    // 测试P07引脚舵机 (DP112)
    PR_INFO("📍 测试P07引脚舵机 (DP112)...");
    
    // 关门测试
    if (control_door(SERVO_PWM_CH_P07, false) == OPRT_OK) {
        PR_INFO("✅ P07舵机关门成功");
    } else {
        PR_ERR("❌ P07舵机关门失败");
    }
    
    tal_system_sleep(2000);  // 等待2秒
    
    // 开门测试
    if (control_door(SERVO_PWM_CH_P07, true) == OPRT_OK) {
        PR_INFO("✅ P07舵机开门成功");
    } else {
        PR_ERR("❌ P07舵机开门失败");
    }
    
    PR_INFO("🎉 PWM门控制测试完成!");
}
