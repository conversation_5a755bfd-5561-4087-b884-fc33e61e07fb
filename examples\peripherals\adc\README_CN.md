# ADC

该项目将会介绍如何使用 `tuyaos 3 adc ` 相关接口，获取 `adc` 采集到的值。

* `ADC` 简介
  
  `ADC`（` Analog-to-Digital Converter` ），中文名模拟数字转换器，也称 `AD` 转换器。`ADC` 是将模拟量转换为数字量的器件，常见的应用是将连续变化的电压值转换为数字量。通俗易懂的说法，可以用来采集电压。

接口详细介绍可在 VS Code 中的 Tuya Wind IDE 中的 [TuyaOS API 文档](https://developer.tuya.com/cn/docs/iot-device-dev/tuyaos-wind-ide?id=Kbfy6kfuuqqu3#title-12-TuyaOS%20%E6%96%87%E6%A1%A3%E5%AF%BC%E8%88%AA)中进行查看。

## 运行结果

没调用一次 `example_adc` ，就会将当前采集到的 adc 值打印出来。

```c
[01-01 00:01:34 TUYA D][lr:0x70309] ADC0 value = 4049
```

## 技术支持

您可以通过以下方法获得涂鸦的支持:

- TuyaOS 论坛： https://www.tuyaos.com

- 开发者中心： https://developer.tuya.com

- 帮助中心： https://support.tuya.com/help

- 技术支持工单中心： https://service.console.tuya.com
