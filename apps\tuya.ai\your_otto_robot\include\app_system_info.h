/**
 * @file app_system_info.h
 * @brief app_system_info module is used to
 * @version 0.1
 * @date 2025-04-28
 */

#ifndef __APP_SYSTEM_INFO_H__
#define __APP_SYSTEM_INFO_H__

#include "tuya_cloud_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
************************macro define************************
***********************************************************/

/***********************************************************
***********************typedef define***********************
***********************************************************/

/***********************************************************
********************function declaration********************
***********************************************************/

void app_system_info(void);

void app_system_info_loop_start(void);

#ifdef __cplusplus
}
#endif

#endif /* __APP_SYSTEM_INFO_H__ */
