/**
 * @file test_compile.c
 * @brief 测试编译的简化版本
 */

#include "tal_api.h"
#include "pwm_door_control.h"

// 门状态变量
static bool door1 = false;
static bool door2 = false;
static bool door_check_loop_running = false;

/**
 * @brief 门状态检查循环
 */
static void door_status_check_loop(void *arg)
{
    static bool last_door1_state = false;
    static bool last_door2_state = false;
    
    PR_INFO("🔄 门状态检查循环已启动");
    
    while (door_check_loop_running) {
        // 检查door1状态变化
        if (door1 != last_door1_state) {
            PR_INFO("🚪 检测到门1状态变化: %s → %s", 
                    last_door1_state ? "开启" : "关闭",
                    door1 ? "开启" : "关闭");
            
            // 执行PWM控制
            OPERATE_RET ret = pwm_door_control_handle_dp111(door1);
            if (ret == OPRT_OK) {
                PR_INFO("✅ 门1 PWM控制成功: %s", door1 ? "开启(90°)" : "关闭(0°)");
                last_door1_state = door1;
            } else {
                PR_ERR("❌ 门1 PWM控制失败: %d", ret);
            }
        }
        
        // 检查door2状态变化
        if (door2 != last_door2_state) {
            PR_INFO("🚪 检测到门2状态变化: %s → %s", 
                    last_door2_state ? "开启" : "关闭",
                    door2 ? "开启" : "关闭");
            
            // 执行PWM控制
            OPERATE_RET ret = pwm_door_control_handle_dp112(door2);
            if (ret == OPRT_OK) {
                PR_INFO("✅ 门2 PWM控制成功: %s", door2 ? "开启(90°)" : "关闭(0°)");
                last_door2_state = door2;
            } else {
                PR_ERR("❌ 门2 PWM控制失败: %d", ret);
            }
        }
        
        // 等待下次检查
        tal_system_sleep(100);
    }
    
    PR_INFO("🛑 门状态检查循环已停止");
}

/**
 * @brief 启动门状态检查循环线程
 */
static OPERATE_RET start_door_check_loop(void)
{
    if (door_check_loop_running) {
        PR_WARN("⚠️ 门状态检查循环已在运行");
        return OPRT_OK;
    }
    
    door_check_loop_running = true;
    
    // 创建门状态检查线程
    THREAD_HANDLE door_check_thread = NULL;
    const THREAD_CFG_T thread_cfg = {
        .thrdname = "door_check",
        .stackDepth = 4096,
        .priority = THREAD_PRIO_2,
    };
    OPERATE_RET ret = tal_thread_create_and_start(&door_check_thread, 
                                                  NULL, 
                                                  NULL, 
                                                  door_status_check_loop, 
                                                  NULL, 
                                                  &thread_cfg);
    
    if (ret != OPRT_OK) {
        PR_ERR("❌ 创建门状态检查线程失败: %d", ret);
        door_check_loop_running = false;
        return ret;
    }
    
    PR_INFO("✅ 门状态检查循环线程已启动");
    return OPRT_OK;
}

/**
 * @brief 测试函数
 */
void test_door_loop_compile(void)
{
    PR_INFO("🧪 测试门状态循环编译...");
    
    // 初始化PWM门控制
    if (pwm_door_control_init() == OPRT_OK) {
        PR_INFO("✅ PWM门控制系统初始化成功");
        
        // 启动状态检查循环
        if (start_door_check_loop() == OPRT_OK) {
            PR_INFO("✅ 门状态检查循环已启动");
        }
    }
    
    // 测试门控制
    door1 = true;
    tal_system_sleep(2000);
    door1 = false;
    
    PR_INFO("🎉 测试完成!");
}
