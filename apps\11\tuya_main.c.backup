/**
 * @file tuya_main.c
 * @brief Implements main audio functionality for IoT device
 *
 * This source file provides the implementation of the main audio functionalities
 * required for an IoT device. It includes functionality for audio processing,
 * device initialization, event handling, and network communication. The
 * implementation supports audio volume control, data point processing, and
 * interaction with the Tuya IoT platform. This file is essential for developers
 * working on IoT applications that require audio capabilities and integration
 * with the Tuya IoT ecosystem.
 *
 * @copyright Copyright (c) 2021-2025 Tuya Inc. All Rights Reserved.
 *
 */

#include "tuya_cloud_types.h"

#include <assert.h>
#include "cJSON.h"
#include "tal_api.h"
#include "tuya_config.h"
#include "tuya_iot.h"
#include "tuya_iot_dp.h"
#include "netmgr.h"
#include "tkl_output.h"
#include "tal_cli.h"
#include "tuya_authorize.h"
#if defined(ENABLE_WIFI) && (ENABLE_WIFI == 1)
#include "netconn_wifi.h"
#endif
#if defined(ENABLE_WIRED) && (ENABLE_WIRED == 1)
#include "netconn_wired.h"
#endif
#if defined(ENABLE_LIBLWIP) && (ENABLE_LIBLWIP == 1)
#include "lwip_init.h"
#endif

#if defined(ENABLE_CHAT_DISPLAY) && (ENABLE_CHAT_DISPLAY == 1)
#include "app_display.h"
#endif

#include "board_com_api.h"

#include "app_chat_bot.h"
#include "ai_audio.h"
#include "reset_netcfg.h"
#include "app_system_info.h"



/* Tuya device handle */
tuya_iot_client_t ai_client;

#ifndef PROJECT_VERSION
#define PROJECT_VERSION "1.0.0"
#endif

#define DPID_VOLUME 3

static uint8_t _need_reset = 0;











/**
 * @brief user defined log output api, in this demo, it will use uart0 as log-tx
 *
 * @param str log string
 * @return void
 */
void user_log_output_cb(const char *str)
{
    tal_uart_write(TUYA_UART_NUM_0, (const uint8_t *)str, strlen(str));
}

/**
 * @brief user defined upgrade notify callback, it will notify device a OTA request received
 *
 * @param client device info
 * @param upgrade the upgrade request info
 * @return void
 */

void user_upgrade_notify_on(tuya_iot_client_t *client, cJSON *upgrade)
static OPERATE_RET inline_medication_log_init(void)
{
    PR_INFO("🔧 初始化服药日志系统 (Flash存储)...");

    memset(&g_medication_log, 0, sizeof(g_medication_log));
    g_medication_log.flash_addr = MEDICATION_LOG_FLASH_ADDR;
    g_medication_log.flash_size = MEDICATION_LOG_FLASH_SIZE;

    // 初始化Flash驱动 (暂时注释掉，因为函数不存在)
    // OPERATE_RET ret = tkl_flash_init();
    // if (ret != OPRT_OK) {
    //     PR_ERR("❌ Flash驱动初始化失败: %d", ret);
    //     return ret;
    // }
    OPERATE_RET ret = OPRT_OK; // 临时设置为成功

    // 读取Flash头部
    ret = tkl_flash_read(g_medication_log.flash_addr, (uint8_t*)&g_medication_log.header, sizeof(flash_log_header_t));
    if (ret != OPRT_OK) {
        PR_ERR("❌ 读取Flash头部失败: %d", ret);
        return ret;
    }

    // 检查魔数，如果不匹配则初始化Flash区域
    if (g_medication_log.header.magic != MEDICATION_LOG_MAGIC) {
        PR_INFO("🔧 首次使用，初始化Flash日志区域...");

        // 擦除Flash区域
        ret = tkl_flash_erase(g_medication_log.flash_addr, g_medication_log.flash_size);
        if (ret != OPRT_OK) {
            PR_ERR("❌ Flash擦除失败: %d", ret);
            return ret;
        }

        // 初始化头部
        memset(&g_medication_log.header, 0, sizeof(flash_log_header_t));
        g_medication_log.header.magic = MEDICATION_LOG_MAGIC;
        g_medication_log.header.version = 1;
        g_medication_log.header.entry_count = 0;
        g_medication_log.header.write_index = 0;

        // 写入头部
        ret = tkl_flash_write(g_medication_log.flash_addr, (uint8_t*)&g_medication_log.header, sizeof(flash_log_header_t));
        if (ret != OPRT_OK) {
            PR_ERR("❌ 写入Flash头部失败: %d", ret);
            return ret;
        }
    }

    g_medication_log.initialized = true;
    PR_INFO("✅ 服药日志系统初始化完成 (Flash地址: 0x%X, 已有%d条记录)",
            g_medication_log.flash_addr, g_medication_log.header.entry_count);



    return OPRT_OK;
}

/**
 * @brief 写入服药日志到Flash
 */
static OPERATE_RET inline_medication_log_write_dispense(const char *medication_name)
{
    if (!g_medication_log.initialized || !medication_name) {
        return OPRT_INVALID_PARM;
    }

    // 创建日志条目
    flash_log_entry_t entry = {0};
    entry.magic = MEDICATION_LOG_ENTRY_MAGIC;
    entry.timestamp = tal_time_get_posix();
    strncpy(entry.medication_name, medication_name, sizeof(entry.medication_name) - 1);

    // 格式化日志内容
    TIME_T current_time = tal_time_get_posix();
    time_t std_time = (time_t)current_time;
    struct tm *time_info = localtime(&std_time);

    if (time_info) {
        snprintf(entry.log_entry, sizeof(entry.log_entry),
                "%04d年%02d月%02d日%02d时%02d分服用%s",
                time_info->tm_year + 1900, time_info->tm_mon + 1, time_info->tm_mday,
                time_info->tm_hour, time_info->tm_min, medication_name);
    } else {
        snprintf(entry.log_entry, sizeof(entry.log_entry),
                "系统时间%ld服用%s", current_time, medication_name);
    }

    // 计算CRC
    entry.crc = inline_crc32(&entry, sizeof(entry) - sizeof(entry.crc));

    // 计算Flash写入地址
    uint32_t entry_addr = g_medication_log.flash_addr + sizeof(flash_log_header_t) +
                         (g_medication_log.header.write_index * sizeof(flash_log_entry_t));

    // 写入Flash
    OPERATE_RET ret = tkl_flash_write(entry_addr, (uint8_t*)&entry, sizeof(flash_log_entry_t));
    if (ret != OPRT_OK) {
        PR_ERR("❌ 写入Flash日志失败: %d", ret);
        return ret;
    }

    // 更新头部信息
    g_medication_log.header.write_index = (g_medication_log.header.write_index + 1) % MAX_LOG_ENTRIES_FLASH;
    if (g_medication_log.header.entry_count < MAX_LOG_ENTRIES_FLASH) {
        g_medication_log.header.entry_count++;
    }

    // 写回头部
    ret = tkl_flash_write(g_medication_log.flash_addr, (uint8_t*)&g_medication_log.header, sizeof(flash_log_header_t));
    if (ret != OPRT_OK) {
        PR_ERR("❌ 更新Flash头部失败: %d", ret);
        return ret;
    }

    PR_INFO("📝 服药日志已写入Flash: %s (条目%d)", medication_name, g_medication_log.header.entry_count);



    return OPRT_OK;
}

/**
 * @brief user defined upgrade notify callback, it will notify device a OTA request received
 *
 * @param client device info
 * @param upgrade the upgrade request info
 * @return void
 */
void user_upgrade_notify_on(tuya_iot_client_t *client, cJSON *upgrade)
{
    PR_INFO("----- Upgrade information -----");
    PR_INFO("OTA Channel: %d", cJSON_GetObjectItem(upgrade, "type")->valueint);
    PR_INFO("Version: %s", cJSON_GetObjectItem(upgrade, "version")->valuestring);
    PR_INFO("Size: %s", cJSON_GetObjectItem(upgrade, "size")->valuestring);
    PR_INFO("MD5: %s", cJSON_GetObjectItem(upgrade, "md5")->valuestring);
    PR_INFO("HMAC: %s", cJSON_GetObjectItem(upgrade, "hmac")->valuestring);
    PR_INFO("URL: %s", cJSON_GetObjectItem(upgrade, "url")->valuestring);
    PR_INFO("HTTPS URL: %s", cJSON_GetObjectItem(upgrade, "httpsUrl")->valuestring);
}

// ========== 舵机控制系统 ==========

/**
 * @brief 初始化舵机系统
 */
static OPERATE_RET inline_medication_servo_init(void)
{
    PR_INFO("🔧 开始初始化药品给药舵机系统...");

    // 初始化所有舵机PWM通道
    for (int i = 0; i < SERVO_COUNT; i++) {
        TUYA_PWM_BASE_CFG_T cfg = {
            .polarity = TUYA_PWM_POSITIVE,      // 正极性输出
            .count_mode = TUYA_PWM_CNT_UP,      // 向上计数模式
            .duty = inline_angle_to_pwm_duty(SERVO_CENTER_ANGLE),  // 初始90度位置
            .cycle = SERVO_PWM_CYCLE,           // 20ms周期 (20000us)
            .frequency = SERVO_PWM_FREQ         // 50Hz频率
        };

        OPERATE_RET ret = tkl_pwm_init(g_servo_pwm_channels[i], &cfg);
        if (ret != OPRT_OK) {
            PR_ERR("❌ 舵机%d PWM初始化失败: %d", i + 1, ret);
            return ret;
        }

        // 启动PWM输出
        ret = tkl_pwm_start(g_servo_pwm_channels[i]);
        if (ret != OPRT_OK) {
            PR_ERR("❌ 舵机%d PWM启动失败: %d", i + 1, ret);
            return ret;
        }

        // 初始化舵机状态
        g_servo_states[i].servo_index = i;
        g_servo_states[i].gpio_pin = g_servo_pins[i];
        g_servo_states[i].current_angle = SERVO_CENTER_ANGLE;
        g_servo_states[i].target_angle = SERVO_CENTER_ANGLE;
        g_servo_states[i].last_operation_time = tal_time_get_posix();
        g_servo_states[i].is_active = true;
        g_servo_states[i].operation_count = 0;

        PR_INFO("✅ 舵机%d (P%d, PWM%d) 初始化成功", i + 1, g_servo_pins[i], g_servo_pwm_channels[i]);
    }

    PR_INFO("✅ 所有舵机初始化完成");
    return OPRT_OK;
}

/**
 * @brief 设置舵机角度
 */
static OPERATE_RET inline_medication_servo_set_angle(uint8_t servo_id, uint16_t angle)
{
    if (servo_id >= SERVO_COUNT) {
        PR_ERR("❌ 无效的舵机ID: %d", servo_id);
        return OPRT_INVALID_PARM;
    }

    if (angle > 180) {
        PR_WARN("⚠️ 角度超出范围，限制为180度");
        angle = 180;
    }

    // 计算PWM占空比
    uint32_t duty = inline_angle_to_pwm_duty(angle);

    // 获取当前PWM配置
    TUYA_PWM_BASE_CFG_T cfg;
    OPERATE_RET ret = tkl_pwm_info_get(g_servo_pwm_channels[servo_id], &cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机%d 获取PWM配置失败: %d", servo_id + 1, ret);
        return ret;
    }

    // 更新占空比
    cfg.duty = duty;

    // 设置新的PWM配置
    ret = tkl_pwm_info_set(g_servo_pwm_channels[servo_id], &cfg);
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机%d 设置PWM配置失败: %d", servo_id + 1, ret);
        return ret;
    }

    // 更新舵机状态
    g_servo_states[servo_id].current_angle = angle;
    g_servo_states[servo_id].last_operation_time = tal_time_get_posix();
    g_servo_states[servo_id].operation_count++;

    PR_INFO("🔧 舵机%d (P%d) 设置角度: %d度 (占空比: %d/%d)",
            servo_id + 1, g_servo_pins[servo_id], angle, duty, SERVO_PWM_CYCLE);

    // 等待舵机移动到位
    tal_system_sleep(500);

    return OPRT_OK;
}

/**
 * @brief 舵机分药动作
 */
static OPERATE_RET inline_medication_servo_dispense(uint8_t servo_id)
{
    if (servo_id >= SERVO_COUNT) {
        return OPRT_INVALID_PARM;
    }

    PR_INFO("🏥 开始分药: 舵机%d", servo_id + 1);

    // 执行分药动作序列
    OPERATE_RET ret;

    // 1. 移动到分药位置 (45度)
    PR_INFO("  步骤1: 移动到分药位置 (45度)");
    ret = inline_medication_servo_set_angle(servo_id, 45);
    if (ret != OPRT_OK) {
        return ret;
    }

    // 2. 等待药物分配
    PR_INFO("  步骤2: 等待药物分配...");
    tal_system_sleep(1000);

    // 3. 返回休息位置 (90度)
    PR_INFO("  步骤3: 返回休息位置 (90度)");
    ret = inline_medication_servo_set_angle(servo_id, 90);
    if (ret != OPRT_OK) {
        return ret;
    }

    PR_INFO("✅ 分药完成: 舵机%d", servo_id + 1);
    return OPRT_OK;
}

// ========== IoT数据管理系统 ==========

/**
 * @brief 初始化IoT数据管理器
 */
static void iot_data_init(void)
{
    memset(&g_iot_data, 0, sizeof(iot_data_manager_t));
    g_iot_data.system_start_time = tal_time_get_posix();
    g_iot_data.mqtt_connected = false;

    PR_INFO("✅ IoT数据管理器初始化完成");
}

/**
 * @brief 更新药品配置数据
 */
static void iot_data_update_medication(int index, const char *data)
{
    if (index < 0 || index >= 6 || !data) {
        return;
    }

    strncpy(g_iot_data.medication_data[index], data, sizeof(g_iot_data.medication_data[index]) - 1);
    g_iot_data.medication_configured[index] = true;
    g_iot_data.last_update_time[index] = tal_time_get_posix();

    // 生成药品ID
    snprintf(g_iot_data.medication_ids[index], sizeof(g_iot_data.medication_ids[index]),
             "MED%02d%04X", index + 1, (uint16_t)(tal_time_get_posix() & 0xFFFF));

    PR_INFO("📊 更新药品%d配置数据: %s", index + 1, g_iot_data.medication_ids[index]);
}

/**
 * @brief 更新分药统计数据
 */
static void iot_data_update_stats(bool success)
{
    g_iot_data.total_dispense_count++;
    if (success) {
        g_iot_data.success_dispense_count++;
    } else {
        g_iot_data.error_count++;
    }
}

/**
 * @brief 上报错误信息到DP107 (增强tuya_iot功能使用)
 */
static void report_to_error_log(const char *error_message)
{
    if (!error_message) return;

    strncpy(g_iot_data.last_error, error_message, sizeof(g_iot_data.last_error) - 1);
    g_iot_data.error_count++; // 增加错误计数

    // 获取tuya_iot客户端信息
    tuya_iot_client_t *client = tuya_iot_client_get();
    if (!client) {
        PR_ERR("❌ 无法获取TuyaIoT客户端");
        return;
    }

    // 构建包含设备信息的JSON错误报告
    cJSON *error_obj = cJSON_CreateObject();
    if (error_obj) {
        cJSON_AddStringToObject(error_obj, "error_type", "MEDICATION_SYSTEM_ERROR");
        cJSON_AddStringToObject(error_obj, "message", error_message);
        cJSON_AddNumberToObject(error_obj, "timestamp", tal_time_get_posix());
        cJSON_AddNumberToObject(error_obj, "error_count", g_iot_data.error_count);

        // 添加设备信息 (使用tuya_iot客户端信息)
        if (client->config.uuid) {
            cJSON_AddStringToObject(error_obj, "device_uuid", client->config.uuid);
        }
        if (client->config.productkey) {
            cJSON_AddStringToObject(error_obj, "product_key", client->config.productkey);
        }
        cJSON_AddStringToObject(error_obj, "software_ver", client->config.software_ver);

        char *json_string = cJSON_Print(error_obj);
        if (json_string) {
            // 使用tuya_iot异步上报到DP107
            char dp_json[1024];
            snprintf(dp_json, sizeof(dp_json), "{\"%d\":\"%s\"}", DP_ERROR_REPORT, json_string);

            OPERATE_RET ret = tuya_iot_dp_report_json_async(client, dp_json, NULL, NULL, NULL, 5000);
            if (ret == OPRT_OK) {
                PR_ERR("📤 错误信息已通过TuyaIoT上报到DP107: %s", error_message);
            } else {
                PR_ERR("❌ TuyaIoT上报DP107失败: %d", ret);
            }


            tal_free(json_string);
        }
        cJSON_Delete(error_obj);
    }
}

/**
 * @brief 上报测试日志到DP108 (使用tuya_iot异步上报)
 */
static void report_to_test_log(const char *message)
{
    if (!message) return;

    strncpy(g_iot_data.last_test_log, message, sizeof(g_iot_data.last_test_log) - 1);

    // 使用tuya_iot_dp_report_json_async进行异步上报
    char dp_json[512];
    snprintf(dp_json, sizeof(dp_json), "{\"%d\":\"%s\"}", DP_MANUAL_DISPENSE, message);

    OPERATE_RET ret = tuya_iot_dp_report_json_async(tuya_iot_client_get(), dp_json, NULL, NULL, NULL, 5000);
    if (ret == OPRT_OK) {
        PR_INFO("📤 测试日志已通过TuyaIoT上报到DP108: %s", message);
    } else {
        PR_ERR("❌ TuyaIoT上报DP108失败: %d", ret);
    }
}

/**
 * @brief 自动存储DP消息到DP109 (使用tuya_iot异步上报)
 */
static void auto_store_dp_message(int dp_id, const char *message)
{
    if (!message) return;

    char storage_entry[256];
    TIME_T current_time = tal_time_get_posix();

    snprintf(storage_entry, sizeof(storage_entry),
             "[%ld] DP%d: %s", current_time, dp_id, message);

    // 添加到存储缓冲区
    if (strlen(g_iot_data.instruction_storage) + strlen(storage_entry) + 2 < sizeof(g_iot_data.instruction_storage)) {
        if (strlen(g_iot_data.instruction_storage) > 0) {
            strcat(g_iot_data.instruction_storage, "\n");
        }
        strcat(g_iot_data.instruction_storage, storage_entry);
        g_iot_data.stored_message_count++;

        // 使用tuya_iot异步上报到DP109
        char dp_json[1200];
        snprintf(dp_json, sizeof(dp_json), "{\"%d\":\"%s\"}", DP_OPERATION_LOG, g_iot_data.instruction_storage);

        OPERATE_RET ret = tuya_iot_dp_report_json_async(tuya_iot_client_get(), dp_json, NULL, NULL, NULL, 5000);
        if (ret == OPRT_OK) {
            PR_DEBUG("📤 消息已通过TuyaIoT存储到DP109 (总计%d条)", g_iot_data.stored_message_count);
        } else {
            PR_ERR("❌ TuyaIoT上报DP109失败: %d", ret);
        }
    }
}

// 暂时注释掉未使用的函数以避免编译警告
/*
static void report_device_status_to_cloud(void)
{
    // 功能已暂时禁用
    PR_DEBUG("设备状态上报功能已禁用");
}
*/

// ========== DP处理函数 ==========

/**
 * @brief 处理单个药品配置字符串
 */
static void handle_single_medication_string(const char *medication_str, int medicine_index, int dp_id)
{
    if (!medication_str || medicine_index < 0 || medicine_index >= 6) {
        return;
    }

    PR_INFO("🔥 处理药品%d配置: %s", medicine_index + 1, medication_str);

    // 解析药品信息 (简化版本，实际可以更复杂)
    char *str_copy = tal_malloc(strlen(medication_str) + 1);
    if (!str_copy) {
        report_to_error_log("内存分配失败");
        return;
    }
    strcpy(str_copy, medication_str);

    // 使用空格分割字符串
    char *fields[10];
    int field_count = 0;
    char *token = strtok(str_copy, " ");

    while (token != NULL && field_count < 10) {
        fields[field_count] = token;
        field_count++;
        token = strtok(NULL, " ");
    }

    if (field_count >= 1) {
        // 至少有药品名称
        medication_config_t *config = &g_medication_configs[medicine_index];

        strncpy(config->medication_name, fields[0], sizeof(config->medication_name) - 1);
        config->medication_id = generate_medication_id(fields[0]);
        config->dosage_per_time = (field_count > 1) ? atoi(fields[1]) : 1;
        config->times_per_day = (field_count > 2) ? atoi(fields[2]) : 1;
        config->duration_days = (field_count > 3) ? atoi(fields[3]) : 7;
        config->is_active = true;
        config->created_time = tal_time_get_posix();
        config->last_modified = config->created_time;

        PR_INFO("✅ 药品%d配置完成: %s (ID: %u)", medicine_index + 1,
                config->medication_name, config->medication_id);


    }

    tal_free(str_copy);
}

/**
 * @brief 处理手动分药控制 (DP108)
 */
static void handle_manual_dispense_command(const char *command_str)
{
    if (!command_str) return;

    PR_INFO("🔥 收到手动分药控制指令: %s", command_str);

    // 解析指令格式: "dispense:1" 或 "test:all"
    if (strncmp(command_str, "dispense:", 9) == 0) {
        int servo_id = atoi(command_str + 9) - 1; // 转换为0-5索引

        if (servo_id >= 0 && servo_id < SERVO_COUNT) {
            PR_INFO("🏥 执行手动分药: 舵机%d", servo_id + 1);

            OPERATE_RET ret = inline_medication_servo_dispense(servo_id);
            if (ret == OPRT_OK) {
                // 写入服药日志
                char med_name[64];
                snprintf(med_name, sizeof(med_name), "手动分药-位置%d", servo_id + 1);
                inline_medication_log_write_dispense(med_name);

                // 更新统计
                iot_data_update_stats(true);

                report_to_test_log("手动分药执行成功");
            } else {
                report_to_error_log("手动分药执行失败");
                iot_data_update_stats(false);
            }
        } else {
            report_to_error_log("无效的舵机ID");
        }
    } else if (strcmp(command_str, "test:all") == 0) {
        PR_INFO("🔧 执行所有舵机测试");

        for (int i = 0; i < SERVO_COUNT; i++) {
            inline_medication_servo_set_angle(i, 45);
            tal_system_sleep(500);
            inline_medication_servo_set_angle(i, 90);
            tal_system_sleep(500);
        }

        report_to_test_log("所有舵机测试完成");
    } else {
        report_to_error_log("未知的控制指令");
    }
}

/**
 * @brief 处理舵机测试 (DP110) - 增强tuya_iot功能使用
 */
static void handle_servo_test_dp110(bool enable)
{
    PR_INFO("🔥 DP110舵机测试: %s", enable ? "开启" : "关闭");

    if (enable) {
        // 测试第一个舵机，并通过tuya_iot上报详细状态
        PR_INFO("🔧 开始舵机测试序列...");

        OPERATE_RET ret = inline_medication_servo_dispense(0);
        if (ret == OPRT_OK) {
            // 构建详细的测试结果报告
            char test_result[256];
            snprintf(test_result, sizeof(test_result),
                     "DP110舵机测试成功 - 舵机0完成分药动作 - 时间: %ld", tal_time_get_posix());

            report_to_test_log(test_result);

            // 通过tuya_iot上报测试成功状态到DP110 (回传确认)
            char dp_json[128];
            snprintf(dp_json, sizeof(dp_json), "{\"%d\":true}", DP_SERVO_TEST);
            tuya_iot_dp_report_json_async(tuya_iot_client_get(), dp_json, NULL, NULL, NULL, 3000);

            PR_INFO("✅ 舵机测试成功，状态已通过TuyaIoT回传");
        } else {
            char error_msg[128];
            snprintf(error_msg, sizeof(error_msg), "DP110舵机测试失败 - 错误码: %d", ret);
            report_to_error_log(error_msg);

            // 通过tuya_iot上报测试失败状态
            char dp_json[128];
            snprintf(dp_json, sizeof(dp_json), "{\"%d\":false}", DP_SERVO_TEST);
            tuya_iot_dp_report_json_async(tuya_iot_client_get(), dp_json, NULL, NULL, NULL, 3000);
        }
    } else {
        // 停止测试，返回中心位置
        inline_medication_servo_set_angle(0, 90);

        char stop_msg[128];
        snprintf(stop_msg, sizeof(stop_msg), "DP110舵机测试停止 - 舵机0返回中心位置 - 时间: %ld", tal_time_get_posix());
        report_to_test_log(stop_msg);

        // 通过tuya_iot确认停止状态
        char dp_json[128];
        snprintf(dp_json, sizeof(dp_json), "{\"%d\":false}", DP_SERVO_TEST);
        tuya_iot_dp_report_json_async(tuya_iot_client_get(), dp_json, NULL, NULL, NULL, 3000);

        PR_INFO("🛑 舵机测试已停止，状态已通过TuyaIoT确认");
    }
}

/**
 * @brief 处理舱门开关控制 (DP111-116) - 增强tuya_iot双向通信
 */
static void handle_door_switch_control(int door_index, bool open)
{
    if (door_index < 0 || door_index >= 6) {
        return;
    }

    PR_INFO("🚪 舱门%d控制: %s", door_index + 1, open ? "开启" : "关闭");

    // 舱门控制实际上就是对应的舵机控制
    uint16_t target_angle = open ? 45 : 90; // 开启45度，关闭90度

    OPERATE_RET ret = inline_medication_servo_set_angle(door_index, target_angle);
    if (ret == OPRT_OK) {
        // 操作成功，记录详细日志
        char log_msg[256];
        snprintf(log_msg, sizeof(log_msg),
                 "舱门%d%s成功 - 舵机角度: %d度 - 时间: %ld",
                 door_index + 1, open ? "开启" : "关闭", target_angle, tal_time_get_posix());
        report_to_test_log(log_msg);



        // 通过tuya_iot回传舱门状态确认 (双向通信)
        int dp_id = DP_DOOR_SWITCH_1 + door_index; // DP111-116
        char dp_json[128];
        snprintf(dp_json, sizeof(dp_json), "{\"%d\":%s}", dp_id, open ? "true" : "false");

        OPERATE_RET report_ret = tuya_iot_dp_report_json_async(tuya_iot_client_get(), dp_json, NULL, NULL, NULL, 3000);
        if (report_ret == OPRT_OK) {
            PR_INFO("✅ 舱门%d状态已通过TuyaIoT回传确认: %s", door_index + 1, open ? "开启" : "关闭");
        } else {
            PR_ERR("❌ TuyaIoT回传舱门%d状态失败: %d", door_index + 1, report_ret);
        }

        // 更新统计信息
        iot_data_update_stats(true);

    } else {
        // 操作失败，记录错误并上报
        char error_msg[256];
        snprintf(error_msg, sizeof(error_msg),
                 "舱门%d%s失败 - 错误码: %d - 目标角度: %d度",
                 door_index + 1, open ? "开启" : "关闭", ret, target_angle);
        report_to_error_log(error_msg);

        // 更新统计信息
        iot_data_update_stats(false);

        // 通过tuya_iot上报错误状态 (保持前端同步)
        int dp_id = DP_DOOR_SWITCH_1 + door_index;
        char dp_json[128];
        snprintf(dp_json, sizeof(dp_json), "{\"%d\":%s}", dp_id, open ? "false" : "true"); // 保持原状态
        tuya_iot_dp_report_json_async(tuya_iot_client_get(), dp_json, NULL, NULL, NULL, 3000);
    }
}

OPERATE_RET audio_dp_obj_proc(dp_obj_recv_t *dpobj)
{
    uint32_t index = 0;
    for (index = 0; index < dpobj->dpscnt; index++) {
        dp_obj_t *dp = dpobj->dps + index;
        PR_DEBUG("idx:%d dpid:%d type:%d ts:%u", index, dp->id, dp->type, dp->time_stamp);

        switch (dp->id) {
        case DPID_VOLUME: {
            uint8_t volume = dp->value.dp_value;
            PR_DEBUG("volume:%d", volume);
            ai_audio_set_volume(volume);
#if defined(ENABLE_CHAT_DISPLAY) && (ENABLE_CHAT_DISPLAY == 1)
            char volume_str[20] = {0};
            snprintf(volume_str, sizeof(volume_str), "%s%d", VOLUME, volume);
            app_display_send_msg(TY_DISPLAY_TP_NOTIFICATION, (uint8_t *)volume_str, strlen(volume_str));
#endif
            break;
        }

        // ========== 智能药品管理系统DP处理 ==========
        case DP_MEDICATION_CONFIG_1:
        case DP_MEDICATION_CONFIG_2:
        case DP_MEDICATION_CONFIG_3:
        case DP_MEDICATION_CONFIG_4:
        case DP_MEDICATION_CONFIG_5:
        case DP_MEDICATION_CONFIG_6:
            if (dp->type == PROP_STR) {
                int medicine_index = dp->id - DP_MEDICATION_CONFIG_1;
                PR_INFO("📥 收到药品%d配置 (DP%d): %s", medicine_index + 1, dp->id, dp->value.dp_str);

                // 自动存储到DP109
                auto_store_dp_message(dp->id, dp->value.dp_str);

                // 更新IoT数据管理器
                iot_data_update_medication(medicine_index, dp->value.dp_str);

                // 处理药品配置
                handle_single_medication_string(dp->value.dp_str, medicine_index, dp->id);
            }
            break;

        case DP_MANUAL_DISPENSE:
            if (dp->type == PROP_STR) {
                PR_INFO("📥 收到手动分药控制 (DP108): %s", dp->value.dp_str);
                auto_store_dp_message(dp->id, dp->value.dp_str);
                handle_manual_dispense_command(dp->value.dp_str);
            }
            break;

        case DP_SERVO_TEST:
            if (dp->type == PROP_BOOL) {
                PR_INFO("📥 收到舵机测试控制 (DP110): %s", dp->value.dp_bool ? "开启" : "关闭");
                handle_servo_test_dp110(dp->value.dp_bool);
            }
            break;

        case DP_DOOR_SWITCH_1:
        case DP_DOOR_SWITCH_2:
        case DP_DOOR_SWITCH_3:
        case DP_DOOR_SWITCH_4:
        case DP_DOOR_SWITCH_5:
        case DP_DOOR_SWITCH_6:
            if (dp->type == PROP_BOOL) {
                int door_index = dp->id - DP_DOOR_SWITCH_1;
                PR_INFO("📥 收到舱门%d开关控制 (DP%d): %s", door_index + 1, dp->id, dp->value.dp_bool ? "开启" : "关闭");
                handle_door_switch_control(door_index, dp->value.dp_bool);
            }
            break;

        default:
            break;
        }
    }

    return OPRT_OK;
}

OPERATE_RET ai_audio_volume_upload(void)
{
    tuya_iot_client_t *client = tuya_iot_client_get();
    dp_obj_t dp_obj = {0};

    uint8_t volume = ai_audio_get_volume();

    dp_obj.id = DPID_VOLUME;
    dp_obj.type = PROP_VALUE;
    dp_obj.value.dp_value = volume;

    PR_DEBUG("DP upload volume:%d", volume);

    return tuya_iot_dp_obj_report(client, client->activate.devid, &dp_obj, 1, 0);
}

/**
 * @brief user defined event handler
 *
 * @param client device info
 * @param event the event info
 * @return void
 */
void user_event_handler_on(tuya_iot_client_t *client, tuya_event_msg_t *event)
{
    PR_DEBUG("Tuya Event ID:%d(%s)", event->id, EVENT_ID2STR(event->id));
    PR_INFO("Device Free heap %d", tal_system_get_free_heap_size());

    switch (event->id) {
    case TUYA_EVENT_BIND_START:
        PR_INFO("🔗 设备绑定开始!");
        if (_need_reset == 1) {
            PR_INFO("Device Reset!");
            tal_system_reset();
        }

        ai_audio_player_play_alert(AI_AUDIO_ALERT_NETWORK_CFG);


        break;

    case TUYA_EVENT_BIND_TOKEN_ON:
        break;

    /* MQTT with tuya cloud is connected, device online */
    case TUYA_EVENT_MQTT_CONNECTED:
        PR_INFO("Device MQTT Connected!");
        tal_event_publish(EVENT_MQTT_CONNECTED, NULL);

        static uint8_t first = 1;
        if (first) {
            first = 0;

#if defined(ENABLE_CHAT_DISPLAY) && (ENABLE_CHAT_DISPLAY == 1)
            UI_WIFI_STATUS_E wifi_status = UI_WIFI_STATUS_GOOD;
            app_display_send_msg(TY_DISPLAY_TP_NETWORK, (uint8_t *)&wifi_status, sizeof(UI_WIFI_STATUS_E));
#endif

            ai_audio_player_play_alert(AI_AUDIO_ALERT_NETWORK_CONNECTED);
            ai_audio_volume_upload();
        }
        break;

    /* MQTT with tuya cloud is disconnected, device offline */
    case TUYA_EVENT_MQTT_DISCONNECT:
        PR_INFO("Device MQTT DisConnected!");
        tal_event_publish(EVENT_MQTT_DISCONNECTED, NULL);
        break;

    /* RECV upgrade request */
    case TUYA_EVENT_UPGRADE_NOTIFY:
        user_upgrade_notify_on(client, event->value.asJSON);
        break;

    /* Sync time with tuya Cloud */
    case TUYA_EVENT_TIMESTAMP_SYNC:
        PR_INFO("=== TUYA CLOUD TIME SYNC EVENT ===");
        PR_INFO("Received timestamp: %d", event->value.asInteger);

        // Set system time first
        tal_time_set_posix(event->value.asInteger, 1);
        PR_INFO("System time set via tal_time_set_posix");

#if defined(ENABLE_CHAT_DISPLAY) && (ENABLE_CHAT_DISPLAY == 1)
        // Force time sync and update display
        time_t sync_time = (time_t)event->value.asInteger;

        struct tm *sync_tm = localtime(&sync_time);
        if (sync_tm) {
            PR_INFO("Cloud time: %04d-%02d-%02d %02d:%02d:%02d (timestamp: %ld)",
                   sync_tm->tm_year + 1900, sync_tm->tm_mon + 1, sync_tm->tm_mday,
                   sync_tm->tm_hour, sync_tm->tm_min, sync_tm->tm_sec, sync_time);
        }

        // 直接使用tuya_iot时间同步，无需额外的time_manager
        PR_INFO("✅ TuyaIoT时间同步完成: %ld", sync_time);

        // 更新IoT数据管理器的时间同步状态
        g_iot_data.system_start_time = sync_time;



// 暂时禁用时间同步时的UI更新以解决页面刷新问题
// #if defined(ENABLE_CHAT_DISPLAY) && (ENABLE_CHAT_DISPLAY == 1)
//         // Update network status and UI
//         ui_main_menu_set_network_status(true);
// #endif
        PR_INFO("=== TUYA IOT TIME SYNC COMPLETE ===");
#endif
        break;

    case TUYA_EVENT_RESET:
        PR_INFO("Device Reset:%d", event->value.asInteger);

        _need_reset = 1;
        break;

    /* RECV OBJ DP */
    case TUYA_EVENT_DP_RECEIVE_OBJ: {
        dp_obj_recv_t *dpobj = event->value.dpobj;
        PR_DEBUG("SOC Rev DP Cmd t1:%d t2:%d CNT:%u", dpobj->cmd_tp, dpobj->dtt_tp, dpobj->dpscnt);
        if (dpobj->devid != NULL) {
            PR_DEBUG("devid.%s", dpobj->devid);
        }

        audio_dp_obj_proc(dpobj);

        tuya_iot_dp_obj_report(client, dpobj->devid, dpobj->dps, dpobj->dpscnt, 0);

    } break;

    /* RECV RAW DP */
    case TUYA_EVENT_DP_RECEIVE_RAW: {
        dp_raw_recv_t *dpraw = event->value.dpraw;
        PR_DEBUG("SOC Rev DP Cmd t1:%d t2:%d", dpraw->cmd_tp, dpraw->dtt_tp);
        if (dpraw->devid != NULL) {
            PR_DEBUG("devid.%s", dpraw->devid);
        }

        uint32_t index = 0;
        dp_raw_t *dp = &dpraw->dp;
        PR_DEBUG("dpid:%d type:RAW len:%d data:", dp->id, dp->len);
        for (index = 0; index < dp->len; index++) {
            PR_DEBUG_RAW("%02x", dp->data[index]);
        }

        tuya_iot_dp_raw_report(client, dpraw->devid, &dpraw->dp, 3);

    } break;

    default:
        break;
    }
}

/**
 * @brief user defined network check callback, it will check the network every 1sec,
 *        in this demo it alwasy return ture due to it's a wired demo
 *
 * @return true
 * @return false
 */
bool user_network_check(void)
{
    netmgr_status_e status = NETMGR_LINK_DOWN;
    netmgr_conn_get(NETCONN_AUTO, NETCONN_CMD_STATUS, &status);
    return status == NETMGR_LINK_DOWN ? false : true;
}

void user_main(void)
{
    int ret = OPRT_OK;

    //! open iot development kit runtim init
    cJSON_InitHooks(&(cJSON_Hooks){.malloc_fn = tal_malloc, .free_fn = tal_free});
    tal_log_init(TAL_LOG_LEVEL_DEBUG, 1024, (TAL_LOG_OUTPUT_CB)tkl_log_output);

    PR_NOTICE("🚀 智能药品管理系统启动中...");
    PR_NOTICE("Application information:");
    PR_NOTICE("Project name:        %s", PROJECT_NAME);
    PR_NOTICE("App version:         %s", PROJECT_VERSION);
    PR_NOTICE("Compile time:        %s", __DATE__);
    PR_NOTICE("TuyaOpen version:    %s", OPEN_VERSION);
    PR_NOTICE("TuyaOpen commit-id:  %s", OPEN_COMMIT);
    PR_NOTICE("Platform chip:       %s", PLATFORM_CHIP);
    PR_NOTICE("Platform board:      %s", PLATFORM_BOARD);
    PR_NOTICE("Platform commit-id:  %s", PLATFORM_COMMIT);

    // 1. 基础系统初始化
    tal_kv_init(&(tal_kv_cfg_t){
        .seed = "vmlkasdh93dlvlcy",
        .key = "dflfuap134ddlduq",
    });
    tal_sw_timer_init();  // 保留给TuyaOpen SDK内部使用，应用层不直接使用定时器
    tal_workq_init();
    tal_cli_init();
    tuya_authorize_init();

    reset_netconfig_start();

    tuya_iot_license_t license;

    if (OPRT_OK != tuya_authorize_read(&license)) {
        license.uuid = TUYA_OPENSDK_UUID;
        license.authkey = TUYA_OPENSDK_AUTHKEY;
        PR_WARN("Replace the TUYA_OPENSDK_UUID and TUYA_OPENSDK_AUTHKEY contents, otherwise the demo cannot work.\n \
                Visit https://platform.tuya.com/purchase/index?type=6 to get the open-sdk uuid and authkey.");
    }

    /* Initialize Tuya device configuration */
    ret = tuya_iot_init(&ai_client, &(const tuya_iot_config_t){
                                        .software_ver = PROJECT_VERSION,
                                        .productkey = TUYA_PRODUCT_ID,
                                        .uuid = license.uuid,
                                        .authkey = license.authkey,
                                        // .firmware_key      = TUYA_DEVICE_FIRMWAREKEY,
                                        .event_handler = user_event_handler_on,
                                        .network_check = user_network_check,
                                    });
    assert(ret == OPRT_OK);

    // 初始化LWIP
#if defined(ENABLE_LIBLWIP) && (ENABLE_LIBLWIP == 1)
    TUYA_LwIP_Init();
#endif

    // network init
    netmgr_type_e type = 0;
#if defined(ENABLE_WIFI) && (ENABLE_WIFI == 1)
    type |= NETCONN_WIFI;
#endif
#if defined(ENABLE_WIRED) && (ENABLE_WIRED == 1)
    type |= NETCONN_WIRED;
#endif
    netmgr_init(type);
#if defined(ENABLE_WIFI) && (ENABLE_WIFI == 1)
    netmgr_conn_set(NETCONN_WIFI, NETCONN_CMD_NETCFG, &(netcfg_args_t){.type = NETCFG_TUYA_BLE});
#endif

    PR_DEBUG("tuya_iot_init success");

    ret = board_register_hardware();
    if (ret != OPRT_OK) {
        PR_ERR("board_register_hardware failed");
    }

    // 7. 应用层初始化
    ret = app_chat_bot_init();
    if (ret != OPRT_OK) {
        PR_ERR("tuya_audio_recorde_init failed");
    }

    app_system_info();

    // 8. 智能药品管理系统初始化
    PR_INFO("🔧 开始初始化智能药品管理系统...");

    // 8.1 初始化IoT数据管理器
    iot_data_init();

    // 8.2 初始化Flash日志系统
    ret = inline_medication_log_init();
    if (ret != OPRT_OK) {
        PR_ERR("❌ Flash日志系统初始化失败: %d", ret);
        report_to_error_log("Flash日志系统初始化失败");
    } else {
        PR_INFO("✅ Flash日志系统初始化完成");
    }

    // 8.3 初始化舵机系统
    ret = inline_medication_servo_init();
    if (ret != OPRT_OK) {
        PR_ERR("❌ 舵机系统初始化失败: %d", ret);
        report_to_error_log("舵机系统初始化失败");
    } else {
        PR_INFO("✅ 舵机系统初始化完成");
    }

    // 8.4 初始化药品配置数组
    memset(g_medication_configs, 0, sizeof(g_medication_configs));
    for (int i = 0; i < MAX_MEDICATION_SLOTS; i++) {
        g_medication_configs[i].is_active = false;
        g_medication_configs[i].created_time = tal_time_get_posix();
    }

    PR_INFO("🎉 智能药品管理系统初始化完成!");

    /* Start tuya iot task */
    tuya_iot_start(&ai_client);

    tkl_wifi_set_lp_mode(0, 0);

    reset_netconfig_check();

    for (;;) {
        /* Loop to receive packets, and handles client keepalive */
        tuya_iot_yield(&ai_client);
    }
}

/**
 * @brief main
 *
 * @param argc
 * @param argv
 * @return void
 */
#if OPERATING_SYSTEM == SYSTEM_LINUX
void main(int argc, char *argv[])
{
    user_main();
}
#else

/* Tuya thread handle */
static THREAD_HANDLE ty_app_thread = NULL;

/**
 * @brief  task thread
 *
 * @param[in] arg:Parameters when creating a task
 * @return none
 */
static void tuya_app_thread(void *arg)
{
    user_main();

    tal_thread_delete(ty_app_thread);
    ty_app_thread = NULL;
}

void tuya_app_main(void)
{
    THREAD_CFG_T thrd_param = {4096, 4, "tuya_app_main"};
    tal_thread_create_and_start(&ty_app_thread, NULL, NULL, tuya_app_thread, NULL, &thrd_param);
}
#endif
