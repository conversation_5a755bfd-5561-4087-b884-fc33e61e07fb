/**
 * @file pwm_door_test.c
 * @brief 智能药盒双门PWM控制系统测试程序
 *
 * 专为智能药盒双门系统设计的测试程序
 * 测试P06和P07引脚控制的2个ST90A舵机
 *
 * <AUTHOR> Team
 * @date 2024-12-23
 */

#include "pwm_door_control.h"
#include "tal_api.h"
#include "tkl_output.h"

/**
 * @brief 测试单个舵机的开关功能
 * 
 * @param servo_id 舵机ID
 * @param test_name 测试名称
 */
static void test_single_servo(servo_id_e servo_id, const char* test_name)
{
    PR_INFO("🧪 开始测试 %s (舵机%d)...", test_name, servo_id + 1);
    
    // 测试关闭位置
    PR_INFO("   ➤ 测试关闭位置 (0°)");
    OPERATE_RET ret = pwm_door_control_set_state(servo_id, false);
    if (ret != OPRT_OK) {
        PR_ERR("   ❌ 关闭位置测试失败: %d", ret);
        return;
    }
    tal_system_sleep(1000);
    
    // 测试开启位置
    PR_INFO("   ➤ 测试开启位置 (90°)");
    ret = pwm_door_control_set_state(servo_id, true);
    if (ret != OPRT_OK) {
        PR_ERR("   ❌ 开启位置测试失败: %d", ret);
        return;
    }
    tal_system_sleep(1000);
    
    // 回到关闭位置
    PR_INFO("   ➤ 回到关闭位置");
    ret = pwm_door_control_set_state(servo_id, false);
    if (ret != OPRT_OK) {
        PR_ERR("   ❌ 回到关闭位置失败: %d", ret);
        return;
    }
    tal_system_sleep(500);
    
    PR_INFO("✅ %s 测试完成", test_name);
}

/**
 * @brief 测试角度控制功能
 * 
 * @param servo_id 舵机ID
 */
static void test_angle_control(servo_id_e servo_id)
{
    PR_INFO("🎯 测试舵机%d角度控制...", servo_id + 1);
    
    uint16_t test_angles[] = {0, 45, 90, 135, 180, 90, 0};
    size_t angle_count = sizeof(test_angles) / sizeof(test_angles[0]);
    
    for (size_t i = 0; i < angle_count; i++) {
        PR_INFO("   ➤ 设置角度: %d°", test_angles[i]);
        OPERATE_RET ret = pwm_door_control_set_angle(servo_id, test_angles[i]);
        if (ret != OPRT_OK) {
            PR_ERR("   ❌ 角度设置失败: %d", ret);
            return;
        }
        tal_system_sleep(800);
    }
    
    PR_INFO("✅ 角度控制测试完成");
}

/**
 * @brief 测试DP命令处理
 */
static void test_dp_commands(void)
{
    PR_INFO("📡 测试DP命令处理...");
    
    // 测试DP111命令
    PR_INFO("   ➤ 测试DP111 (舱门1) 开启");
    OPERATE_RET ret = pwm_door_control_handle_dp111(true);
    if (ret != OPRT_OK) {
        PR_ERR("   ❌ DP111开启命令失败: %d", ret);
        return;
    }
    tal_system_sleep(1000);
    
    PR_INFO("   ➤ 测试DP111 (舱门1) 关闭");
    ret = pwm_door_control_handle_dp111(false);
    if (ret != OPRT_OK) {
        PR_ERR("   ❌ DP111关闭命令失败: %d", ret);
        return;
    }
    tal_system_sleep(1000);
    
    // 测试DP112命令
    PR_INFO("   ➤ 测试DP112 (舱门2) 开启");
    ret = pwm_door_control_handle_dp112(true);
    if (ret != OPRT_OK) {
        PR_ERR("   ❌ DP112开启命令失败: %d", ret);
        return;
    }
    tal_system_sleep(1000);
    
    PR_INFO("   ➤ 测试DP112 (舱门2) 关闭");
    ret = pwm_door_control_handle_dp112(false);
    if (ret != OPRT_OK) {
        PR_ERR("   ❌ DP112关闭命令失败: %d", ret);
        return;
    }
    tal_system_sleep(1000);
    
    PR_INFO("✅ DP命令处理测试完成");
}

/**
 * @brief 测试系统状态获取
 */
static void test_status_query(void)
{
    PR_INFO("📊 测试系统状态查询...");
    
    // 获取系统状态
    pwm_door_system_t system_status;
    OPERATE_RET ret = pwm_door_control_get_system_status(&system_status);
    if (ret != OPRT_OK) {
        PR_ERR("   ❌ 获取系统状态失败: %d", ret);
        return;
    }
    
    PR_INFO("   ➤ 系统初始化状态: %s", system_status.system_initialized ? "已初始化" : "未初始化");
    PR_INFO("   ➤ 总操作次数: %d", system_status.total_operations);
    PR_INFO("   ➤ 初始化时间: %d ms", system_status.init_time);
    
    // 获取各舵机状态
    for (servo_id_e i = SERVO_ID_DOOR_1; i < SERVO_ID_MAX; i++) {
        servo_status_t servo_status;
        ret = pwm_door_control_get_status(i, &servo_status);
        if (ret == OPRT_OK) {
            PR_INFO("   ➤ 舵机%d状态:", i + 1);
            PR_INFO("     - GPIO引脚: P%02d", servo_status.gpio_pin);
            PR_INFO("     - 当前角度: %d°", servo_status.current_angle);
            PR_INFO("     - 门状态: %s", servo_status.door_state == DOOR_STATE_OPEN ? "开启" : 
                                      servo_status.door_state == DOOR_STATE_CLOSED ? "关闭" : "未知");
            PR_INFO("     - 操作次数: %d", servo_status.operation_count);
            PR_INFO("     - 初始化状态: %s", servo_status.is_initialized ? "已初始化" : "未初始化");
        }
    }
    
    PR_INFO("✅ 系统状态查询测试完成");
}

/**
 * @brief 运行完整的PWM门控制系统测试
 */
void pwm_door_run_full_test(void)
{
    PR_INFO("🚀 开始PWM门控制系统完整测试...");
    PR_INFO("================================================");
    
    // 初始化系统
    PR_INFO("🔧 初始化PWM门控制系统...");
    OPERATE_RET ret = pwm_door_control_init();
    if (ret != OPRT_OK) {
        PR_ERR("❌ 系统初始化失败: %d", ret);
        return;
    }
    PR_INFO("✅ 系统初始化成功");
    
    // 等待系统稳定
    tal_system_sleep(1000);
    
    // 测试单个舵机功能
    test_single_servo(SERVO_ID_DOOR_1, "舱门1 (P06)");
    tal_system_sleep(500);
    
    test_single_servo(SERVO_ID_DOOR_2, "舱门2 (P07)");
    tal_system_sleep(500);
    
    // 测试角度控制
    test_angle_control(SERVO_ID_DOOR_1);
    tal_system_sleep(500);
    
    test_angle_control(SERVO_ID_DOOR_2);
    tal_system_sleep(500);
    
    // 测试DP命令处理
    test_dp_commands();
    tal_system_sleep(500);
    
    // 测试系统状态查询
    test_status_query();
    
    // 运行内置测试
    PR_INFO("🧪 运行内置测试程序...");
    pwm_door_control_run_tests();
    
    PR_INFO("================================================");
    PR_INFO("🎉 PWM门控制系统完整测试完成!");
    
    // 清理系统
    PR_INFO("🧹 清理系统资源...");
    pwm_door_control_cleanup();
    PR_INFO("✅ 系统清理完成");
}

/**
 * @brief 运行智能药盒双门快速测试
 */
void pwm_door_run_simple_test(void)
{
    PR_INFO("🚀 开始智能药盒双门快速测试...");

    // 初始化系统
    OPERATE_RET ret = pwm_door_control_init();
    if (ret != OPRT_OK) {
        PR_ERR("❌ 双门系统初始化失败: %d", ret);
        return;
    }

    // 双门开关测试
    PR_INFO("🚪 测试1号门(P06)开关...");
    pwm_door_control_set_state(SERVO_ID_DOOR_1, true);   // 开启
    tal_system_sleep(1000);
    pwm_door_control_set_state(SERVO_ID_DOOR_1, false);  // 关闭
    tal_system_sleep(1000);

    PR_INFO("🚪 测试2号门(P07)开关...");
    pwm_door_control_set_state(SERVO_ID_DOOR_2, true);   // 开启
    tal_system_sleep(1000);
    pwm_door_control_set_state(SERVO_ID_DOOR_2, false);  // 关闭
    tal_system_sleep(1000);

    PR_INFO("🎉 智能药盒双门测试完成!");
}

/**
 * @brief 运行双门同步测试
 */
void pwm_door_run_dual_sync_test(void)
{
    PR_INFO("🚀 开始双门同步测试...");

    // 初始化系统
    OPERATE_RET ret = pwm_door_control_init();
    if (ret != OPRT_OK) {
        PR_ERR("❌ 系统初始化失败: %d", ret);
        return;
    }

    // 同时开启两个门
    PR_INFO("🚪 同时开启1号门和2号门...");
    pwm_door_control_set_state(SERVO_ID_DOOR_1, true);
    pwm_door_control_set_state(SERVO_ID_DOOR_2, true);
    tal_system_sleep(2000);

    // 同时关闭两个门
    PR_INFO("🚪 同时关闭1号门和2号门...");
    pwm_door_control_set_state(SERVO_ID_DOOR_1, false);
    pwm_door_control_set_state(SERVO_ID_DOOR_2, false);
    tal_system_sleep(2000);

    // 交替开关测试
    PR_INFO("🚪 交替开关测试...");
    for (int i = 0; i < 3; i++) {
        PR_INFO("   第%d轮: 1号门开启，2号门关闭", i + 1);
        pwm_door_control_set_state(SERVO_ID_DOOR_1, true);
        pwm_door_control_set_state(SERVO_ID_DOOR_2, false);
        tal_system_sleep(1000);

        PR_INFO("   第%d轮: 1号门关闭，2号门开启", i + 1);
        pwm_door_control_set_state(SERVO_ID_DOOR_1, false);
        pwm_door_control_set_state(SERVO_ID_DOOR_2, true);
        tal_system_sleep(1000);
    }

    // 回到初始状态
    PR_INFO("🚪 回到初始状态(全部关闭)...");
    pwm_door_control_set_state(SERVO_ID_DOOR_1, false);
    pwm_door_control_set_state(SERVO_ID_DOOR_2, false);
    tal_system_sleep(1000);

    PR_INFO("🎉 双门同步测试完成!");
}
