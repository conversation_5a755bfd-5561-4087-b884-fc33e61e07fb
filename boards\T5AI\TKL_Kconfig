config LITTLE_END
    int
    default 1
    ---help---
        0       /* big endian */
        1       /* little endian */

config ENABLE_FILE_SYSTEM
    bool
    default n

config ENABLE_WIFI
    bool
    default y

config ENABLE_WIRED
    bool
    default n

config ENABLE_BLUETOOTH
    bool
    default y

config ENABLE_HCI
    bool
    default y

config ENABLE_RTC
    bool
    default y

config ENABLE_WATCHDOG
    bool
    default y

config ENABLE_UART
    bool
    default y

config ENABLE_FLASH
    bool
    default y

config TUYA_FLASH_TYPE_MAX_PARTITION_NUM
    int
    default 10

config ENABLE_EXT_RAM
    bool
    default y

config ENABLE_ADC
    bool
    default y

config ENABLE_PWM
    bool
    default y

config ENABLE_GPIO
    bool
    default y

config ENABLE_I2C
    bool
    default y

config ENABLE_SPI
    bool
    default y

config ENABLE_QSPI
    bool
    default y

config ENABLE_MCU8080
    bool
    default y   

config ENABLE_TIMER
    bool
    default y

config ENABLE_MEDIA
    bool
    default y

config ENABLE_RGB
    bool
    default y

config ENABLE_PM
    bool
    default y

config ENABLE_STORAGE
    bool
    default n

config ENABLE_DAC
    bool
    default n

config ENABLE_I2S
    bool
    default n

config ENABLE_WAKEUP
    bool
    default y

config ENABLE_REGISTER
    bool
    default n

config ENABLE_PINMUX
    bool
    default y
 
config ENABLE_PIN2FUNC
    bool
    default y    

config ENABLE_PLATFORM_AES
    bool
    default n

config ENABLE_PLATFORM_SHA256
    bool
    default n

config ENABLE_PLATFORM_MD5
    bool
    default n

config ENABLE_PLATFORM_SHA1
    bool
    default n

config ENABLE_PLATFORM_RSA
    bool
    default n

config ENABLE_PLATFORM_ECC
    bool
    default n
